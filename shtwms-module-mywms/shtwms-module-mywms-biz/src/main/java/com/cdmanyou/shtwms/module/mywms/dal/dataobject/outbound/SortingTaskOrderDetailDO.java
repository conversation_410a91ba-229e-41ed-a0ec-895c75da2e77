package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 分拣任务单明细 DO
 *
 * <AUTHOR>
 */
@TableName("sorting_task_order_detail")
@KeySequence("sorting_task_order_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SortingTaskOrderDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 分拣任务单ID（关联主表）
     */
    private Long sortingOrderId;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格（非必填）
     */
    private String specification;
    /**
     * 单位（非必填）
     */
    private String unit;
    /**
     * 待分拣数量
     */
    private Integer pendingSortingQuantity;
    /**
     * 已分拣数量
     */
    private Integer sortedQuantity;
    /**
     * 差异数量
     */
    private Integer differenceQuantity;
    /**
     * 批次号（非必填）
     */
    private String batchNumber;
    /**
     * 生产日期（非必填）
     */
    private LocalDate productionDate;
    /**
     * 拣货容器
     */
    private String pickingContainerType;
    /**
     * 拣货容器号
     */
    private String pickingContainerCode;

}