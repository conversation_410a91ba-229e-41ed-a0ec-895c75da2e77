package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 分拣任务单主新增/修改 Request VO")
@Data
public class SortingTaskOrderSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11422")
    private Long id;

    @Schema(description = "分拣单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分拣单号不能为空")
    private String sortingOrderCode;

    @Schema(description = "出库订单号（关联出库订单主表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "出库订单号（关联出库订单主表）不能为空")
    private String outboundOrderCode;

    @Schema(description = "客户名称", example = "李四")
    private String customerName;

    @Schema(description = "商品总数量")
    private Integer totalProductQuantity;

    @Schema(description = "仓库ID（关联仓库表）", example = "21918")
    private Long warehouseId;

    @Schema(description = "分拣员")
    private String sorter;

    @Schema(description = "分拣容器（非必填）", example = "1")
    private String containerType;

    @Schema(description = "分拣容器号（非必填）")
    private String containerCode;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "分拣时间")
    private LocalDateTime sortingTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "分拣任务单明细列表")
    private List<SortingTaskOrderDetailDO> sortingTaskOrderDetails;

}