package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "库存管理响应实体")
@Data
public class ProductWareHouseLocationPageRespVO {

    @Schema(description = "自增ID")
    private Long id;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品规格")
    private String specification;

    @Schema(description = "商品单位")
    private String unit;

    @Schema(description = "生产日期")
    private String productionDate;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "库存数量")
    private Integer available;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "货位编号")
    private String warehouseLocationCode;

}
