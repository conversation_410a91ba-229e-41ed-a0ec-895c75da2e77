package com.cdmanyou.shtwms.module.mywms.dal.dataobject.industryinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 行业信息管理 DO
 *
 * <AUTHOR>
 */
@TableName("industry_info")
@KeySequence("industry_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndustryInfoDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 行业编码（唯一键）
     */
    private String industryCode;
    /**
     * 行业名称
     */
    private String industryName;
    /**
     * 行业描述（非必填）
     */
    private String description;
    /**
     * 行业状态
     */
    private String status;


}