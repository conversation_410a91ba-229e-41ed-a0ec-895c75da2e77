package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 货位管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WarehouseLocationPageReqVO extends PageParam {

    @Schema(description = "货位编码")
    private String code;

    @Schema(description = "货位名称", example = "赵六")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", example = "23872")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", example = "30224")
    private Long areaId;

    @Schema(description = "所属货架ID（关联货架表）", example = "24501")
    private Long shelfId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "长度(米)")
    private BigDecimal length;

    @Schema(description = "宽度(米)")
    private BigDecimal width;

    @Schema(description = "高度(米)")
    private BigDecimal height;

    @Schema(description = "承重(吨)")
    private BigDecimal weight;

    @Schema(description = "体积(立方米)")
    private BigDecimal volume;

    @Schema(description = "货位状态(1启用/0停用)", example = "2")
    private Integer status;

    @Schema(description = "卸货方式(0叉车卸货，1其他卸货方式)")
    private Integer unloadingMethod;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}