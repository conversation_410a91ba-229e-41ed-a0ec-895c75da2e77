package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.CustomerDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.CustomerMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.CustomerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 客户管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CustomerServiceImpl implements CustomerService {

    @Resource
    private CustomerMapper customerMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCustomer(CustomerSaveReqVO createReqVO) {
        validateCustomerUnique(createReqVO);
        String generatedCode = generateCustomerCode();
        createReqVO.setCode(generatedCode); // 覆盖传入的编码

        // 校验名称唯一性（编码已自动生成，无需校验）
        if (customerMapper.existsByName(createReqVO.getName())) {
            throw exception(CUSTOMER_NAME_DUPLICATE);
        }

        // 插入数据
        CustomerDO customer = BeanUtils.toBean(createReqVO, CustomerDO.class);
        customerMapper.insert(customer);
        return customer.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCustomer(CustomerSaveReqVO updateReqVO) {

        // 校验存在
        validateCustomerExists(updateReqVO.getId());
        validateCustomerUniqueForUpdate(updateReqVO, updateReqVO.getId());
        // 更新
        CustomerDO updateObj = BeanUtils.toBean(updateReqVO, CustomerDO.class);
        customerMapper.updateById(updateObj);
    }
    private void validateCustomerUnique(CustomerSaveReqVO reqVO) {
        // 校验客户编码唯一性
        if (customerMapper.existsByCode(reqVO.getCode())) {
            throw exception(CUSTOMER_CODE_DUPLICATE);
        }
        // 校验客户名称唯一性
        if (customerMapper.existsByName(reqVO.getName())) {
            throw exception(CUSTOMER_NAME_DUPLICATE);
        }
    }

    private void validateCustomerUniqueForUpdate(CustomerSaveReqVO reqVO, Long excludeId) {
        // 校验客户编码唯一性（排除自身）
        if (customerMapper.existsByCodeAndIdNot(reqVO.getCode(), excludeId)) {
            throw exception(CUSTOMER_CODE_DUPLICATE);
        }
        // 校验客户名称唯一性（排除自身）
        if (customerMapper.existsByNameAndIdNot(reqVO.getName(), excludeId)) {
            throw exception(CUSTOMER_NAME_DUPLICATE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomer(Long id) {
        // 校验存在
        validateCustomerExists(id);
        // 删除
        customerMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableCustomer(Long id) {
        CustomerDO customer = validateCustomerExists(id);
        // 幂等性检查：当前已启用则无需操作
        if (customer.getStatus() == 1) {
            throw exception(CUSTOMER_ALREADY_ENABLED);
        }
        // 更新状态为启用(1)
        customerMapper.updateById(new CustomerDO().setId(id).setStatus(1));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableCustomer(Long id) {
        CustomerDO customer = validateCustomerExists(id);
        // 幂等性检查：当前已禁用则无需操作
        if (customer.getStatus() == 0) {
            throw exception(CUSTOMER_ALREADY_DISABLED);
        }
        // 更新状态为禁用(0)
        customerMapper.updateById(new CustomerDO().setId(id).setStatus(0));
    }

    private String generateCustomerCode() {
        return "KH" + System.currentTimeMillis();
    }

    private CustomerDO validateCustomerExists(Long id) {
        CustomerDO customer = customerMapper.selectById(id);
        if (customer == null) {
            throw exception(CUSTOMER_NOT_EXISTS);
        }
        return customer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerDO getCustomer(Long id) {
        return customerMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageResult<CustomerDO> getCustomerPage(CustomerPageReqVO pageReqVO) {
        return customerMapper.selectPage(pageReqVO);
    }

}