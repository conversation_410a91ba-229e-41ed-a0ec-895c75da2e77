package com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 货品转移主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransferMapper extends BaseMapperX<TransferDO> {

    default PageResult<TransferDO> selectPage(TransferPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TransferDO>()
                .eqIfPresent(TransferDO::getTransferCode, reqVO.getTransferCode())
                .betweenIfPresent(TransferDO::getTransferDate, reqVO.getTransferDate())
                .eqIfPresent(TransferDO::getTotalQuantity, reqVO.getTotalQuantity())
                .eqIfPresent(TransferDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TransferDO::getTransferUser, reqVO.getTransferUser())
                .betweenIfPresent(TransferDO::getTransferTime, reqVO.getTransferTime())
                .betweenIfPresent(TransferDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TransferDO::getId));
    }

}