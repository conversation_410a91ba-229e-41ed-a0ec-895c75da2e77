package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 盘点计划主分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryPlanPageReqVO extends PageParam {

    @Schema(description = "盘点计划id")
    private String id;

    @Schema(description = "盘点计划单号")
    private String inventoryPlanCode;

    @Schema(description = "盘点类型", example = "1")
    private String inventoryType;

    @Schema(description = "仓库ID（关联仓库表）", example = "17776")
    private Long warehouseId;

    @Schema(description = "状态", example = "1")
    private String status;

    @Schema(description = "创建人id")
    private String creator;

    @Schema(description = "盘点开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String startTime;

    @Schema(description = "盘点开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String endTime;


}