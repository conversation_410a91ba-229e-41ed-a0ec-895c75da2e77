package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailInputVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "货品转移创建实体")
@Data
public class TransferInputVO {

    @Schema(description = "转移单ID")
    private Long id;

    @Schema(description = "转移总数")
    private Integer totalQuantity;

    @Schema(description = "转移零散总数")
    private Integer totalScatteredQuantity;

    @Schema(description = "转移明细")
    private List<TransferDetailInputVO> transferDetails;

}
