package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import com.cdmanyou.shtwms.framework.common.util.date.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "库存管理响应实体")
@Data
public class ProductWareHouseLocationPageRespVO {

    @Schema(description = "库存ID")
    private Long id;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品规格")
    private String specification;

    @Schema(description = "商品单位")
    private String unit;

    @Schema(description = "货主ID")
    private Long producerId;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "有效期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate effectiveDate;

    @Schema(description = "库存数量")
    private Integer count;

    @Schema(description = "零散库存量")
    private Integer scatteredCount;

    @Schema(description = "冻结数量")
    private Integer freezeCount;

    @Schema(description = "冻结零散量")
    private Integer freeScatteredCount;

    @Schema(description = "占用量")
    private Integer occupation;

    @Schema(description = "零散占用量")
    private Integer scatteredOccupation;

    @Schema(description = "可用量")
    private Integer available;

    @Schema(description = "零散可用量")
    private Integer scatteredAvailable;

    @Schema(description = "所属仓类别（0成品，1半成品，2原料）")
    private Integer houseType;

    @Schema(description = "货位ID")
    private Long warehouseLocationId;

    @Schema(description = "货位编号")
    private String warehouseLocationCode;

    @Schema(description = "是否冻结（0否1是）")
    private Integer freezeType;
}
