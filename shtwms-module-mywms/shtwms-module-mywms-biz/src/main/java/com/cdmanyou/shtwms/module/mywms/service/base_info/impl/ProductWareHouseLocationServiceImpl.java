package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.exception.ServiceException;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import groovyjarjarpicocli.CommandLine;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.PRODUCT_WAREHOUSE_LOCATION_ALREADY_FREEZE;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.PRODUCT_WAREHOUSE_LOCATION_NOT_EXISTS;


/**
 * 商品货位关联表-记录货位上的商品数量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductWareHouseLocationServiceImpl implements ProductWareHouseLocationService {

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Override
    public Long createProductWarehouseLocation(ProductWareHouseLocationSaveReqVO createReqVO) {
        // 插入
        ProductWareHouseLocationDO productWareHouseLocation = BeanUtils.toBean(createReqVO, ProductWareHouseLocationDO.class);
        productWareHouseLocationMapper.insert(productWareHouseLocation);
        // 返回
        return productWareHouseLocation.getId();
    }

    @Override
    public void updateProductWarehouseLocation(ProductWareHouseLocationSaveReqVO updateReqVO) {
        // 校验存在
        validateWarehouseLocationExists(updateReqVO.getId());
        // 更新
        ProductWareHouseLocationDO updateObj = BeanUtils.toBean(updateReqVO, ProductWareHouseLocationDO.class);
        productWareHouseLocationMapper.updateById(updateObj);
    }

    @Override
    public void deleteProductWarehouseLocation(Long id) {
        // 校验存在
        validateWarehouseLocationExists(id);
        // 删除
        productWareHouseLocationMapper.deleteById(id);
    }

    private ProductWareHouseLocationDO validateWarehouseLocationExists(Long id) {
        ProductWareHouseLocationDO productWareHouseLocationDO = productWareHouseLocationMapper.selectById(id);
        if (productWareHouseLocationDO == null) {
            throw exception(PRODUCT_WAREHOUSE_LOCATION_NOT_EXISTS);
        }
        return productWareHouseLocationDO;
    }

    @Override
    public ProductWareHouseLocationRespVO getProductWarehouseLocationById(Long id) {
        return productWareHouseLocationMapper.getProductWarehouseLocationById(id);
    }

    @Override
    public PageResult<ProductWareHouseLocationPageRespVO> getProductWarehouseLocationPage(ProductWareHouseLocationPageReqVO pageReqVO) {
        Page<ProductWareHouseLocationPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = productWareHouseLocationMapper.selectPage(page, pageReqVO);
        Long total = productWareHouseLocationMapper.selectPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

    @Override
    public void updateProductWarehouseLocationOccupation(Long id, int occupationNum) {
        LambdaUpdateWrapper<ProductWareHouseLocationDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProductWareHouseLocationDO::getId, id);
        updateWrapper.set(ProductWareHouseLocationDO::getOccupation, occupationNum);
        productWareHouseLocationMapper.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateByShelving(List<ShelvingManagementDetailSaveVO> shelvingManagementDetails) {
        for (ShelvingManagementDetailSaveVO detail : shelvingManagementDetails) {
            ProductWareHouseLocationDO productWareHouseLocationDO = productWareHouseLocationMapper.getByShelvingDetail(detail.getProducerId(), detail.getProductId(),
                    detail.getProductionDate(), detail.getWarehouseLocationId());
            if (Objects.isNull(productWareHouseLocationDO)) {
                ProductWareHouseLocationDO createObj = new ProductWareHouseLocationDO();
                createObj.setProducerId(detail.getProducerId())
                        .setProductId(detail.getProductId())
                        .setProductionDate(detail.getProductionDate())
                        .setProductBatchNumber(detail.getProductBatchNumber())
                        .setWarehouseLocationId(detail.getWarehouseLocationId())
                        .setCount(detail.getShelvedQuantity())
                        .setAvailable(detail.getShelvedQuantity())
                        .setOccupation(0)
                        .setScatteredCount(detail.getShelvedScatteredQuantity())
                        .setScatteredAvailable(detail.getShelvedScatteredQuantity())
                        .setScatteredOccupation(0);
                productWareHouseLocationMapper.insert(createObj);
            }
            ProductWareHouseLocationDO updateObj = new ProductWareHouseLocationDO();
            updateObj.setId(productWareHouseLocationDO.getId())
                    .setReversion(productWareHouseLocationDO.getReversion())
                    .setCount(productWareHouseLocationDO.getCount() + detail.getShelvedQuantity())
                    .setAvailable(productWareHouseLocationDO.getAvailable() + detail.getShelvedQuantity())
                    .setScatteredCount(productWareHouseLocationDO.getScatteredCount() + detail.getShelvedScatteredQuantity())
                    .setAvailable(productWareHouseLocationDO.getScatteredAvailable() + detail.getShelvedScatteredQuantity());
            int updateRows = productWareHouseLocationMapper.updateById(updateObj);
            if (updateRows == 0) {
                throw new ServiceException(1001_500, "网络繁忙，请稍候重试");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezeProductWareHouseLocationById(ProductWarehouseLocationFreezeVO freezeVO) {
        ProductWareHouseLocationDO productWareHouseLocationDO = validateWarehouseLocationExists(freezeVO.getId());
        if (freezeVO.getFreezeType() == 1) {
            if (productWareHouseLocationDO.getFreezeType() == 1) {
                throw exception(PRODUCT_WAREHOUSE_LOCATION_ALREADY_FREEZE);
            }
            productWareHouseLocationMapper.freezeProduct(productWareHouseLocationDO.getProductId(), productWareHouseLocationDO.getProductBatchNumber());
        }else if (freezeVO.getFreezeType() == 0) {
            productWareHouseLocationMapper.unfreezeProduct(productWareHouseLocationDO.getProductId(), productWareHouseLocationDO.getProductBatchNumber());
        }
    }

    @Override
    public SimpleLocationStatisticVO getSimpleLocationStatistics() {
        return productWareHouseLocationMapper.getSimpleLocationStatistics();
    }

    @Override
    public SimpleInventoryStatisticVO getSimpleInventoryStatistics() {
        return productWareHouseLocationMapper.getSimpleInventoryStatistics();
    }

    @Override
    public SimpleExpirationStatisticVO getSimpleExpirationStatistics() {
        return productWareHouseLocationMapper.getSimpleExpirationStatistics();
    }

    @Override
    public PageResult<InventoryPageRespVO> getInventoryPage(InventoryPageReqVO pageReqVO) {
        Page<InventoryPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = productWareHouseLocationMapper.getInventoryPage(page, pageReqVO);
        Long total = productWareHouseLocationMapper.getInventoryPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

    @Override
    public PageResult<ExpirationPageRespVO> getExpirationPage(ExpirationPageReqVO pageReqVO) {
        Page<ExpirationPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = productWareHouseLocationMapper.getExpirationPage(page, pageReqVO);
        Long total = productWareHouseLocationMapper.getExpirationPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

}