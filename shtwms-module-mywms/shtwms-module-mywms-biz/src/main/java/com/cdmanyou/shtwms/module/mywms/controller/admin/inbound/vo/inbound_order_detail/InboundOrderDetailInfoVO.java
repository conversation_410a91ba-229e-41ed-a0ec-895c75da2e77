package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "入库单明细详情实体")
@Data
public class InboundOrderDetailInfoVO {

    @Schema(description = "自增ID")
    private Long id;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品名称", example = "芋艿")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "主计量单位类型(0件，1公斤)")
    private Integer masterMeasure;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "拆零数量")
    private Integer looseQuantity;

    @Schema(description = "入库重量")
    private BigDecimal inboundWeight;

    @Schema(description = "入库体积")
    private BigDecimal inboundVolume;

    @Schema(description = "温区（0冷藏，1冷冻）")
    private Integer temperatureZone;

    @Schema(description = "入库板数")
    private Integer inboundQuantity;
}
