package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * 出库订单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OutboundOrderDetailMapper extends BaseMapperX<OutboundOrderDetailDO> {

    default List<OutboundOrderDetailDO> selectListByOutboundId(Long outboundId) {
        return selectList(OutboundOrderDetailDO::getOutboundId, outboundId);
    }

    default int deleteByOutboundId(Long outboundId) {
        return delete(OutboundOrderDetailDO::getOutboundId, outboundId);
    }

    default List<OutboundOrderDetailDO> selectListByOutboundIds(Collection<Long> outboundIds) {
        LambdaQueryWrapperX<OutboundOrderDetailDO> lqw = new LambdaQueryWrapperX<>();
        lqw.in(OutboundOrderDetailDO::getOutboundId, outboundIds);
        return selectList(lqw);
    }

    @Select("  select distinct ood.* from shtwms.outbound_order_detail ood " +
            "  left join shtwms.outbound_order_detail_product_warehouse oodpw on ood.id = oodpw.outbound_detail_id and oodpw.warehouse_id  = #{warehouseId}" +
            "  where ood.outbound_id  = #{outboundOrderId}")
    List<OutboundOrderDetailDO> selectListByOutboundIdAndWarehouseId(@Param("outboundOrderId") Long outboundOrderId,@Param("warehouseId") Long warehouseId);
}