package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 出库订单主 Service 接口
 *
 * <AUTHOR>
 */
public interface OutboundOrderService {

    /**
     * 创建出库订单主
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOutboundOrder(@Valid OutboundOrderSaveReqVO createReqVO);

    /**
     * 更新出库订单主
     *
     * @param updateReqVO 更新信息
     */
    void updateOutboundOrder(@Valid OutboundOrderSaveReqVO updateReqVO);

    /**
     * 删除出库订单主
     *
     * @param id 编号
     */
    void deleteOutboundOrder(Long id);

    /**
     * 获得出库订单主
     *
     * @param id 编号
     * @return 出库订单主
     */
    OutboundOrderRespVO getOutboundOrder(Long id);

    /**
     * 获得出库订单主分页
     *
     * @param pageReqVO 分页查询
     * @return 出库订单主分页
     */
    PageResult<OutboundOrderPageRespVO> getOutboundOrderPage(OutboundOrderPageReqVO pageReqVO);

    // ==================== 子表（出库订单明细） ====================

    /**
     * 获得出库订单明细列表
     *
     * @param outboundId 出库单ID（关联主表）
     * @return 出库订单明细列表
     */
    List<OutboundOrderDetailRespVO> getOutboundOrderDetailListByOutboundId(Long outboundId);

    void outboundStatusUpdate(OutboundStatusReqVO outboundStatusReqVO);

    List<OutboundOrderDO> getOutboundListByIds(Collection<Long> outboundIds);

}