<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.SortingTaskOrderMapper">


    <select id="selectSortingTaskOrderPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderRespVO">
        select sto.id,
               oo.booking_delivery_time,
               sto.sorting_order_code,
               tpo.picking_order_code,
               sto.status,
               oo.outbound_type,
               sto.total_product_quantity,
               sto.create_time,
               sto.creator,
               sto.sorter
        from shtwms.sorting_task_order sto
                 inner join shtwms.outbound_order oo on sto.outbound_order_id = oo.id
                 inner join shtwms.total_picking_order tpo on sto.total_picking_order_id = tpo.id
        where sto.deleted = 0
    </select>
</mapper>