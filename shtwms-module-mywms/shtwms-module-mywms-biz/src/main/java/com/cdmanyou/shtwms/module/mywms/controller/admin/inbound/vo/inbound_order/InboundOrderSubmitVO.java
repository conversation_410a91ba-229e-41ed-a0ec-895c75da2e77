package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailSubmitVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "入库单提交实体")
@Data
public class InboundOrderSubmitVO {

    @Schema(description = "入库单ID")
    private Long id;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "待入库商品总数")
    private Integer pendingQuantityTotal;

    @Schema(description = "待入库商品零散总数")
    private Integer pendingScatteredQuantityTotal;

    @Schema(description = "商品明细")
    private List<InboundOrderDetailSubmitVO> inboundOrderDetails;
}
