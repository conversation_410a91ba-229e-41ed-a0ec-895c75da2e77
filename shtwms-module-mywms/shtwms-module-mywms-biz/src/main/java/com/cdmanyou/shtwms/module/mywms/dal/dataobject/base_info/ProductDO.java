package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品管理 DO
 *
 * <AUTHOR>
 */
@TableName("product")
@KeySequence("product_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 商品编码
     */
    private String code;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 规格
     */
    private String specification;
    /**
     * 条形码
     */
    private String barcode;
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 保质期(天)
     */
    private Integer shelfLife;
    /**
     * 温区（0冷藏，1冷冻）
     */
    private Integer temperatureZone;
    /**
     * 客户ID（关联客户表）
     */
    private Long producerId;
    /**
     * 商品类别ID（关联商品类别表）
     */
    private Long categoryId;
    /**
     * 主图
     */
    private String mainImage;
    /**
     * 附图
     */
    private String subImages;
    /**
     * 商品描述
     */
    private String description;
    /**
     * 使用说明
     */
    private String usageInstruction;
    /**
     * 包装说明
     */
    private String packingInstruction;
    /**
     * 注意事项
     */
    private String precautions;
    /**
     * 安全库存
     */
    private Integer safetyStock;
    /**
     * 最大库存
     */
    private Integer maxStock;
    /**
     * 库存预警阈值
     */
    private Integer stockWarningThreshold;
    /**
     * 码盘单层数量
     */
    private Integer palletLayerQty;
    /**
     * 码盘层高
     */
    private BigDecimal palletLayerHeight;
    /**
     * 主计量单位类别（0件，1公斤）
     */
    private Integer masterMeasure;
    /**
     * 计量方式（0固定重量，1称重计量）
     */
    private Double measureType;
    /**
     * 毛重（公斤）
     */
    private BigDecimal grossWeight;
    /**
     * 副计量类别（0袋，1其他）
     */
    private Double deputyMeasure;
    /**
     * 长（厘米）
     */

    @TableField("length") // 指定数据库列名映射
    private Integer length; // 新字段名

    /**
     * 宽（厘米）
     */
    private Double wide;
    /**
     * 高（厘米） 
     */
    private Double high;
    /**
     * 体积（立方米）
     */
    private Double volume;
    /**
     * 换算率（拆零数量）
     */
    private Integer looseQuantity;
    /**
     * 状态(1启用/0停用)
     */
    private Integer status;


    private BigInteger supplierId;

    private Integer freezeStatus;





}