package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 总拣单明细")
@RestController
@RequestMapping("/mywms/picking-order-detail")
@Validated
public class PickingOrderDetailController {

    @Resource
    private PickingOrderDetailService pickingOrderDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建总拣单明细")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order-detail:create')")
    public CommonResult<Long> createPickingOrderDetail(@Valid @RequestBody PickingOrderDetailSaveReqVO createReqVO) {
        return success(pickingOrderDetailService.createPickingOrderDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新总拣单明细")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order-detail:update')")
    public CommonResult<Boolean> updatePickingOrderDetail(@Valid @RequestBody PickingOrderDetailSaveReqVO updateReqVO) {
        pickingOrderDetailService.updatePickingOrderDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除总拣单明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:picking-order-detail:delete')")
    public CommonResult<Boolean> deletePickingOrderDetail(@RequestParam("id") Long id) {
        pickingOrderDetailService.deletePickingOrderDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得总拣单明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order-detail:query')")
    public CommonResult<PickingOrderDetailRespVO> getPickingOrderDetail(@RequestParam("id") Long id) {
        PickingOrderDetailDO pickingOrderDetail = pickingOrderDetailService.getPickingOrderDetail(id);
        return success(BeanUtils.toBean(pickingOrderDetail, PickingOrderDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得总拣单明细分页")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order-detail:query')")
    public CommonResult<PageResult<PickingOrderDetailRespVO>> getPickingOrderDetailPage(@Valid PickingOrderDetailPageReqVO pageReqVO) {
        PageResult<PickingOrderDetailDO> pageResult = pickingOrderDetailService.getPickingOrderDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PickingOrderDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出总拣单明细 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPickingOrderDetailExcel(@Valid PickingOrderDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PickingOrderDetailDO> list = pickingOrderDetailService.getPickingOrderDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "总拣单明细.xls", "数据", PickingOrderDetailRespVO.class,
                        BeanUtils.toBean(list, PickingOrderDetailRespVO.class));
    }

}