package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TrayPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TraySaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.TrayDO;

import javax.validation.Valid;

/**
 * 托盘管理 Service 接口
 *
 * <AUTHOR>
 */
public interface TrayService {

    /**
     * 创建托盘管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTray(@Valid TraySaveReqVO createReqVO);

    /**
     * 更新托盘管理
     *
     * @param updateReqVO 更新信息
     */
    void updateTray(@Valid TraySaveReqVO updateReqVO);

    /**
     * 删除托盘管理
     *
     * @param id 编号
     */
    void deleteTray(Long id);

    /**
     * 获得托盘管理
     *
     * @param id 编号
     * @return 托盘管理
     */
    TrayDO getTray(Long id);

    /**
     * 获得托盘管理分页
     *
     * @param pageReqVO 分页查询
     * @return 托盘管理分页
     */
    PageResult<TrayDO> getTrayPage(TrayPageReqVO pageReqVO);

    void updateTrayStatus(Long id, Integer status);
}