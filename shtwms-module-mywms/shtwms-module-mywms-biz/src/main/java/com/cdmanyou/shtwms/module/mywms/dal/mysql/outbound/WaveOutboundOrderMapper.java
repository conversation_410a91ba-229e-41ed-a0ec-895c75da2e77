package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOutboundOrderDO;
import org.apache.ibatis.annotations.Mapper;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 出库单波次单关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WaveOutboundOrderMapper extends BaseMapperX<WaveOutboundOrderDO> {


    default List<WaveOutboundOrderDO> selectOutboundByWaveId(Long waveOrderId) {
        LambdaQueryWrapperX<WaveOutboundOrderDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(WaveOutboundOrderDO::getWellenId, waveOrderId);
        return selectList(queryWrapperX);
    }

    default void deleteByWaveId(@NotNull Long waveOrderId) {
        LambdaQueryWrapperX<WaveOutboundOrderDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(WaveOutboundOrderDO::getWellenId, waveOrderId);
        delete(queryWrapperX);
    }
}