package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Schema(description = "管理后台 - 总拣单主新增/修改 Request VO")
@Data
public class PickingOrderSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5718")
    private Long id;

    @Schema(description = "拣货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "拣货单号不能为空")
    private String pickingOrderCode;

    @Schema(description = "波次单编号（关联波次单主表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "波次单编号（关联波次单主表）不能为空")
    private String waveCode;

    @Schema(description = "待拣货总数")
    private Integer totalPending;

    @Schema(description = "已拣货总数")
    private Integer totalPicked;

    @Schema(description = "仓库ID（关联仓库表）", example = "859")
    private Long warehouseId;

    @Schema(description = "拣货员")
    private String picker;

    @Schema(description = "托盘号（非必填）")
    private String trayCode;

    @Schema(description = "拣货容器（非必填）", example = "1")
    private String containerType;

    @Schema(description = "拣货容器号（非必填）")
    private String containerCode;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "总拣单明细列表")
    private List<PickingOrderDetailDO> pickingOrderDetails;

}