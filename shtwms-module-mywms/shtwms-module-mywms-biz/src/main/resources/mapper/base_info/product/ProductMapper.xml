<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper">


    <select id="getInfoByProductOutboundInfo"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutBoundProductDetail">
        SELECT
        a.*,
        ap.*
        FROM (
        SELECT
        pwl.production_date,
        ANY_VALUE (pwl.product_batch_number) AS product_batch_number,
        SUM(pwl.available) AS product_available,
        SUM(pwl.scattered_available) AS product_scattered_available
        FROM shtwms.product_warehouse_location pwl
        INNER join shtwms.warehouse_location wl on pwl.warehouse_location_id = wl.id and wl.warehouse_id = #{warehouseId}
        WHERE pwl.product_id = #{productId} and pwl.house_type = #{houseType}
        <if test="productionDate != null">
            and pwl.production_date = #{productionDate}
        </if>
        GROUP BY pwl.production_date
        ORDER BY pwl.production_date ASC
        LIMIT 1
        ) a
        CROSS JOIN (
        SELECT
        p.loose_quantity,
        p.gross_weight,
        p.volume,
        p.specification
        FROM shtwms.product p
        WHERE p.id = #{productId}
        LIMIT 1
        ) ap where a.product_available >= #{outboundNumber}
    </select>
</mapper>