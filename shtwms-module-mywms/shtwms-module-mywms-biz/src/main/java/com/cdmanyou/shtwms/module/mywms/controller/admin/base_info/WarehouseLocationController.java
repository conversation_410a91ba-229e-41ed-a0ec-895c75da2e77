package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationSaveReqVO;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;


import java.util.*;
import java.io.IOException;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.*;


import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WarehouseLocationDO;

import com.cdmanyou.shtwms.module.mywms.service.base_info.WareHouseLocationService;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Tag(name = "管理后台 - 货位管理")
@RestController
@RequestMapping("/mywms/warehouse-location")
@Validated
public class WarehouseLocationController {

    @Autowired
    private WareHouseLocationService warehouseLocationService;

    @PostMapping("/create")
    @Operation(summary = "创建货位管理")
    @PreAuthorize("@ss.hasPermission('mywms:warehouse-location:create')")
    public CommonResult<Long> createWarehouseLocation(@Valid @RequestBody WarehouseLocationSaveReqVO createReqVO) {
        return success(warehouseLocationService.createWareHouseLocation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货位管理")
    @PreAuthorize("@ss.hasPermission('mywms:warehouse-location:update')")
    public CommonResult<Boolean> updateWarehouseLocation(@Valid @RequestBody WarehouseLocationSaveReqVO updateReqVO) {
        warehouseLocationService.updateWareHouseLocation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除货位管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:warehouse-location:delete')")
    public CommonResult<Boolean> deleteWarehouseLocation(@RequestParam("id") Long id) {
        warehouseLocationService.deleteWareHouseLocation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得货位管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:warehouse-location:query')")
    public CommonResult<WarehouseLocationRespVO> getWarehouseLocation(@RequestParam("id") Long id) {
        WarehouseLocationDO warehouseLocation = warehouseLocationService.getWareHouseLocation(id);
        return success(BeanUtils.toBean(warehouseLocation, WarehouseLocationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货位管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:warehouse-location:query')")
    public CommonResult<PageResult<WarehouseLocationRespVO>> getWarehouseLocationPage(@Valid WarehouseLocationPageReqVO pageReqVO) {
        PageResult<WarehouseLocationDO> pageResult = warehouseLocationService.getWareHouseLocationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WarehouseLocationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出货位管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:warehouse-location:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWarehouseLocationExcel(@Valid WarehouseLocationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WarehouseLocationDO> list = warehouseLocationService.getWareHouseLocationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "货位管理.xls", "数据", WarehouseLocationRespVO.class,
                        BeanUtils.toBean(list, WarehouseLocationRespVO.class));
    }

    @PutMapping("/updatestatus")
    @Operation(summary = "更新货位状态")
    @Parameters({
            @Parameter(name = "id", description = "货位ID", required = true),
            @Parameter(name = "status", description = "状态(1启用/0停用)", required = true)
    })
    @PreAuthorize("@ss.hasPermission('mywms:warehouse-location:update')")
    public CommonResult<Boolean> updateWarehouseLocationStatus(
            @RequestParam("id") Long id,
            @RequestParam("status") @Min(0) @Max(1) Integer status) {

        // 参数校验（已在注解中完成）
        warehouseLocationService.updateWareHouseLocationStatus(id, status);
        return success(true);
    }

}