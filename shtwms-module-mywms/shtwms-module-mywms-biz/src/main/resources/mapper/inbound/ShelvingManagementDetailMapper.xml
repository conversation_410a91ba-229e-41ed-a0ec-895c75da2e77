<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ShelvingManagementDetailMapper">


    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailPageRespVO">
        SELECT
            smd.id,
            sm.shelving_code,
            GROUP_CONCAT(DISTINCT io.order_code) AS inbound_order_code,
            smd.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            smd.shelved_quantity,
            smd.shelved_scattered_quantity,
            smd.warehouse_location_id,
            wl.code AS warehouse_location_code,
            sm.shelving_status
        FROM
            shelving_management_detail smd
        LEFT JOIN
            shelving_management sm ON smd.shelving_id = sm.id
        LEFT JOIN
            system_users su ON sm.shelving_operator = su.id
        LEFT JOIN
            product p ON smd.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON wl.id = smd.warehouse_location_id
        LEFT JOIN
            receipt_registration_detail rrd ON rrd.shelving_detail_id = smd.id
        LEFT JOIN
            receipt_registration rr ON rrd.receipt_id = rr.id
        LEFT JOIN
            inbound_order io ON io.id = rr.inbound_order_id
        LEFT JOIN
            producer pr ON smd.producer_id = pr.id
        WHERE
            smd.deleted = 0
        <if test="pageReqVO.shelvingCode != null and pageReqVO.shelvingCode != ''">
            AND sm.shelving_code LIKE CONCAT('%', #{pageReqVO.shelvingCode}, '%')
        </if>
        <if test="pageReqVO.producerName != null and pageReqVO.producerName != ''">
            AND pr.name LIKE CONCAT('%', #{pageReqVO.producerName}, '%')
        </if>
        <if test="pageReqVO.productName != null and pageReqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{pageReqVO.productName}, '%')
        </if>
        <if test="pageReqVO.shelvingOperatorName != null and pageReqVO.shelvingOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{pageReqVO.shelvingOperatorName}, '%')
        </if>
        <if test="pageReqVO.inboundOrderCode != null and pageReqVO.inboundOrderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{pageReqVO.inboundOrderCode}, '%')
        </if>
        <if test="pageReqVO.warehouseId != null">
            AND sm.warehouse_id = #{pageReqVO.warehouseId}
        </if>
        <if test="pageReqVO.shelvingStatus != null">
            AND sm.shelving_status = #{pageReqVO.shelvingStatus}
        </if>
        <if test="pageReqVO.shelvingSearchStartTime != null and pageReqVO.shelvingSearchStartTime != ''">
            AND sm.shelving_time &gt;= #{pageReqVO.shelvingSearchStartTime}
        </if>
        <if test="pageReqVO.shelvingSearchEndTime != null and pageReqVO.shelvingSearchEndTime != ''">
            AND sm.shelving_time &lt;= #{pageReqVO.shelvingSearchEndTime}
        </if>
        GROUP BY
            smd.id
        ORDER BY
            smd.create_time DESC


    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            count(smd.id)
        FROM
            shelving_management_detail smd
        LEFT JOIN
            shelving_management sm ON smd.shelving_id = sm.id
        LEFT JOIN
            system_users su ON sm.shelving_operator = su.id
        LEFT JOIN
            product p ON smd.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON wl.id = smd.warehouse_location_id
        LEFT JOIN
            receipt_registration_detail rrd ON rrd.shelving_detail_id = smd.id
        LEFT JOIN
            receipt_registration rr ON rrd.receipt_id = rr.id
        LEFT JOIN
            inbound_order io ON io.id = rr.inbound_order_id
        LEFT JOIN
            producer pr ON smd.producer_id = pr.id
        WHERE
            smd.deleted = 0
        <if test="pageReqVO.shelvingCode != null and pageReqVO.shelvingCode != ''">
            AND sm.shelving_code LIKE CONCAT('%', #{pageReqVO.shelvingCode}, '%')
        </if>
        <if test="pageReqVO.producerName != null and pageReqVO.producerName != ''">
            AND pr.name LIKE CONCAT('%', #{pageReqVO.producerName}, '%')
        </if>
        <if test="pageReqVO.productName != null and pageReqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{pageReqVO.productName}, '%')
        </if>
        <if test="pageReqVO.shelvingOperatorName != null and pageReqVO.shelvingOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{pageReqVO.shelvingOperatorName}, '%')
        </if>
        <if test="pageReqVO.inboundOrderCode != null and pageReqVO.inboundOrderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{pageReqVO.inboundOrderCode}, '%')
        </if>
        <if test="pageReqVO.warehouseId != null">
            AND sm.warehouse_id = #{pageReqVO.warehouseId}
        </if>
        <if test="pageReqVO.shelvingStatus != null">
            AND sm.shelving_status = #{pageReqVO.shelvingStatus}
        </if>
        <if test="pageReqVO.shelvingSearchStartTime != null and pageReqVO.shelvingSearchStartTime != ''">
            AND sm.shelving_time &gt;= #{pageReqVO.shelvingSearchStartTime}
        </if>
        <if test="pageReqVO.shelvingSearchEndTime != null and pageReqVO.shelvingSearchEndTime != ''">
            AND sm.shelving_time &lt;= #{pageReqVO.shelvingSearchEndTime}
        </if>
    </select>


</mapper>