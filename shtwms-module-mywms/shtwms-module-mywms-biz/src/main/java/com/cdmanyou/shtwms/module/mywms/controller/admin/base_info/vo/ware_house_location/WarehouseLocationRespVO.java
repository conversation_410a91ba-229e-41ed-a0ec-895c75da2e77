package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 货位管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WarehouseLocationRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23132")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "货位编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("货位编码")
    private String code;

    @Schema(description = "货位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("货位名称")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "23872")
    @ExcelProperty("所属仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "30224")
    @ExcelProperty("所属库区ID（关联库区表）")
    private Long areaId;

    @Schema(description = "所属货架ID（关联货架表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "24501")
    @ExcelProperty("所属货架ID（关联货架表）")
    private Long shelfId;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "长度(米)")
    @ExcelProperty("长度(米)")
    private BigDecimal length;

    @Schema(description = "宽度(米)")
    @ExcelProperty("宽度(米)")
    private BigDecimal width;

    @Schema(description = "高度(米)")
    @ExcelProperty("高度(米)")
    private BigDecimal height;

    @Schema(description = "承重(吨)")
    @ExcelProperty("承重(吨)")
    private BigDecimal weight;

    @Schema(description = "体积(立方米)")
    @ExcelProperty("体积(立方米)")
    private BigDecimal volume;

    @Schema(description = "货位状态(1启用/0停用)", example = "2")
    @ExcelProperty("货位状态(1启用/0停用)")
    private Integer status;

    @Schema(description = "卸货方式(0叉车卸货，1其他卸货方式)")
    @ExcelProperty("卸货方式(0叉车卸货，1其他卸货方式)")
    private Integer unloadingMethod;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}