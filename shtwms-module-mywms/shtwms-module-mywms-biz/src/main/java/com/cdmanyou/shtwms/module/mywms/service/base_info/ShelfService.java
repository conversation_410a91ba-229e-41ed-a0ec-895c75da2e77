package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ShelfDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 货架管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ShelfService {

    /**
     * 创建货架管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createShelf(@Valid ShelfSaveReqVO createReqVO);

    /**
     * 更新货架管理
     *
     * @param updateReqVO 更新信息
     */
    void updateShelf(@Valid ShelfSaveReqVO updateReqVO);

    /**
     * 删除货架管理
     *
     * @param id 编号
     */
    void deleteShelf(Long id);

    /**
     * 获得货架管理
     *
     * @param id 编号
     * @return 货架管理
     */
    ShelfDO getShelf(Long id);

    /**
     * 获得货架管理分页
     *
     * @param pageReqVO 分页查询
     * @return 货架管理分页
     */
    PageResult<ShelfDO> getShelfPage(ShelfPageReqVO pageReqVO);

    void updateShelfStatus(Long id, Integer status);

    List<ShelfDO> getShelfListByAreaId(Long areaId);

}