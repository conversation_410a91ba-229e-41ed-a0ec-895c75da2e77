package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.CustomerDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 客户管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CustomerMapper extends BaseMapperX<CustomerDO> {
    @Select("SELECT COUNT(1) FROM customer WHERE code = #{code} AND deleted = 0")
    boolean existsByCode(String code);

    // 检查名称是否存在
    @Select("SELECT COUNT(1) FROM customer WHERE name = #{name} AND deleted = 0")
    boolean existsByName(String name);

    // 缺少以下方法：
    @Select("SELECT COUNT(1) FROM customer WHERE code = #{code} AND id != #{excludeId} AND deleted = 0")
    boolean existsByCodeAndIdNot(
            @Param("code") String code,
            @Param("excludeId") Long excludeId
    );

    // 修复方法：为两个参数都添加 @Param 注解
    @Select("SELECT COUNT(1) FROM customer WHERE name = #{name} AND id != #{excludeId} AND deleted = 0")
    boolean existsByNameAndIdNot(
            @Param("name") String name,
            @Param("excludeId") Long excludeId
    );
    default PageResult<CustomerDO> selectPage(CustomerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CustomerDO>()
                .likeIfPresent(CustomerDO::getCode, reqVO.getCode())
                .likeIfPresent(CustomerDO::getName, reqVO.getName())
                .likeIfPresent(CustomerDO::getShortName, reqVO.getShortName())
                .eqIfPresent(CustomerDO::getType, reqVO.getType())
                .likeIfPresent(CustomerDO::getEnglishName, reqVO.getEnglishName())
                .eqIfPresent(CustomerDO::getBusinessLicense, reqVO.getBusinessLicense())
                .eqIfPresent(CustomerDO::getDescription, reqVO.getDescription())
                .likeIfPresent(CustomerDO::getContactPerson, reqVO.getContactPerson())                .eqIfPresent(CustomerDO::getContactPhone, reqVO.getContactPhone())
                .eqIfPresent(CustomerDO::getContactEmail, reqVO.getContactEmail())
                .eqIfPresent(CustomerDO::getRegion, reqVO.getRegion())
                .eqIfPresent(CustomerDO::getCreditLimit, reqVO.getCreditLimit())
                .eqIfPresent(CustomerDO::getSettlementMethod, reqVO.getSettlementMethod())
                .eqIfPresent(CustomerDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CustomerDO::getCooperationStatus, reqVO.getCooperationStatus())
                .betweenIfPresent(CustomerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CustomerDO::getId));
    }


}