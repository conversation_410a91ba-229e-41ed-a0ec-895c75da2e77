package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.security.core.util.SecurityFrameworkUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailInputVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailSubmitVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.InboundOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.InboundOrderMapper;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundOrderDetailService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundOrderService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ReceiptRegistrationService;
import com.cdmanyou.shtwms.module.mywms.util.SystemBatchNumberValidator;
import com.google.common.annotations.VisibleForTesting;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;
import static com.cdmanyou.shtwms.module.mywms.util.SystemBatchNumberValidator.validateNoDuplicate;

/**
 * 入库单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InboundOrderServiceImpl implements InboundOrderService {

    @Resource
    private InboundOrderMapper inboundOrderMapper;

    @Resource
    private InboundOrderDetailService inboundOrderDetailService;

    @Resource
    private InboundOrderDetailMapper inboundOrderDetailMapper;

    @Lazy
    @Resource
    private ReceiptRegistrationService receiptRegistrationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long inputInboundOrder(InboundOrderInputVO inputVO) {
        InboundOrderDO inboundOrder = BeanUtils.toBean(inputVO, InboundOrderDO.class);
        // 生成入库单号
        inboundOrder.setOrderCode(generateInboundOrderCode());
        // 设置本次入库的总额
        inboundOrder.setTotalAmount(calculateTotalAmount(inputVO.getInboundOrderDetails()));
        // 设置创建方式 - 录入
        inboundOrder.setCreateType(0);
        // 插入入库单
        inboundOrderMapper.insert(inboundOrder);
        // 插入入库单明细
        inboundOrderDetailService.createInboundOrderDetailList(inboundOrder.getId(), inputVO.getInboundOrderDetails());
        // 返回入库单ID
        return inboundOrder.getId();
    }

    @Lock4j(keys = {"'inbound_order_code'"}, autoRelease = true)
    public String generateInboundOrderCode() {
        // 生成入库单日期号
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成序列号
        Long maxSequence = inboundOrderMapper.getMaxSequence(dateStr);
        Long newMaxSequence = (maxSequence == null) ? 1L : maxSequence + 1;
        // 格式化序列号
        String sequence = String.format("%06d", newMaxSequence);
        return "IB" + dateStr + sequence;
    }

    private BigDecimal calculateTotalAmount(List<InboundOrderDetailInputVO> details) {
        return details.stream()
            .map(detail -> {
                BigDecimal price = detail.getProductPrice();
                Integer quantity = detail.getPendingQuantity();
                Integer looseQuantity = detail.getLooseQuantity();
                BigDecimal scatteredPrice = new BigDecimal(BigInteger.ZERO);
                Integer scatteredQuantity = detail.getPendingScatteredQuantity();
                if (price == null) {
                    price = BigDecimal.ZERO;
                }
                if (quantity == null) {
                    quantity = 0;
                }
                BigDecimal entirePriceTotal = price.multiply(new BigDecimal(quantity));
                if (looseQuantity != null && looseQuantity != 0) {
                    BigDecimal looseQuantityDecimal = new BigDecimal(looseQuantity);
                    scatteredPrice = price.divide(looseQuantityDecimal, 2, RoundingMode.HALF_UP);
                }
                if (scatteredQuantity == null) {
                    scatteredQuantity = 0;
                }
                BigDecimal scatteredPriceTotal = scatteredPrice.multiply(new BigDecimal(scatteredQuantity));
                return entirePriceTotal.add(scatteredPriceTotal);
            })
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInboundOrder(InboundOrderInputVO updateVO) {
        // 校验存在
        validateInboundOrderExists(updateVO.getId());
        InboundOrderDO updateObj = BeanUtils.toBean(updateVO, InboundOrderDO.class);
        // 更新入库单总额
        updateObj.setTotalAmount(calculateTotalAmount(updateVO.getInboundOrderDetails()));
        inboundOrderMapper.updateById(updateObj);
        // 更新明细
        inboundOrderDetailService.updateInboundOrderDetailList(updateVO.getId(), updateVO.getInboundOrderDetails());
    }

    
    private InboundOrderDO validateInboundOrderExists(Long id) {
        InboundOrderDO inboundOrderDO = inboundOrderMapper.selectById(id);
        if (inboundOrderDO == null) {
            throw exception(INBOUND_ORDER_NOT_EXISTS);
        }
        return inboundOrderDO;
    }

    
    private InboundOrderDO validateInboundOrderSubmit(Long id) {
        InboundOrderDO inboundOrderDO = validateInboundOrderExists(id);
        if (inboundOrderDO.getInboundOrderStatus() != 0) {
            throw exception(INBOUND_ORDER_SUBMIT_ERROR_BY_STATUS);
        }
        return inboundOrderDO;
    }

    @Override
    public InboundOrderInfoVO getInboundOrderByCode(String code) {
        return inboundOrderMapper.getInboundOrderByCode(code);
    }

    @Override
    public PageResult<InboundOrderPageRespVO> getInboundOrderPage(InboundOrderPageReqVO pageReqVO) {
        // 查询数据列表
        Page<InboundOrderPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = inboundOrderMapper.selectPage(page, pageReqVO);
        // 查询总数
        Long total = inboundOrderMapper.selectPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submitInboundOrder(InboundOrderSubmitVO submitVO) {
        if (submitVO.getId() == null) {
            // 创建
            InboundOrderDO inboundOrder = BeanUtils.toBean(submitVO, InboundOrderDO.class);
            // 生成入库单号
            inboundOrder.setOrderCode(generateInboundOrderCode());
            // 设置本次入库的总额
            List<InboundOrderDetailInputVO> details = BeanUtils.toBean(submitVO.getInboundOrderDetails(), InboundOrderDetailInputVO.class);
            inboundOrder.setTotalAmount(calculateTotalAmount(details));
            // 设置创建方式 - 录入
            inboundOrder.setCreateType(0)
                    .setInboundOrderStatus(1)
                    .setSubmitOperator(SecurityFrameworkUtils.getLoginUserId())
                    .setSubmitTime(LocalDateTime.now())
                    .setInboundDate(LocalDateTime.now())
                    .setAcceptanceCompleteTime(LocalDateTime.now());
            // 插入入库单
            inboundOrderMapper.insert(inboundOrder);
            List<InboundOrderDetailDO> detailDOList = BeanUtils.toBean(details, InboundOrderDetailDO.class);
            for (InboundOrderDetailDO inboundOrderDetailDO : detailDOList) {
                if (inboundOrderDetailDO.getProductionDate() != null) {
                    String dateStr = inboundOrderDetailDO.getProductionDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                    inboundOrderDetailDO.setSystemBatchNumber(inboundOrderDetailDO.getProductCode() + dateStr);
                }
                inboundOrderDetailDO.setInboundOrderId(inboundOrder.getId());
            }
            // 校验系统批次号是否重复
            validateNoDuplicate(detailDOList, InboundOrderDetailDO::getSystemBatchNumber);
            // 插入入库单明细
            inboundOrderDetailMapper.insertBatch(detailDOList);
            // 创建收货单
            submitVO.setId(inboundOrder.getId());
            submitVO.setInboundOrderDetails(BeanUtils.toBean(detailDOList, InboundOrderDetailSubmitVO.class));
            return receiptRegistrationService.createReceiptRegistration(submitVO);
        }
        // 校验入库单状态
        InboundOrderDO inboundOrderDO = validateInboundOrderSubmit(submitVO.getId());
        // 校验系统批次号是否重复
        validateSystemBatchNumberDuplicate(submitVO.getInboundOrderDetails());
        // 更新入库单
        inboundOrderDO.setInboundOrderStatus(1)
                .setSubmitOperator(SecurityFrameworkUtils.getLoginUserId())
                .setSubmitTime(LocalDateTime.now())
                .setInboundDate(LocalDateTime.now())
                .setAcceptanceCompleteTime(LocalDateTime.now());
        inboundOrderMapper.updateById(inboundOrderDO);
        // 更新入库单明细
        List<InboundOrderDetailSubmitVO> inboundOrderDetails = submitVO.getInboundOrderDetails();
        if (CollUtil.isEmpty(inboundOrderDetails)) {
            throw exception(RECEIPT_REGISTRATION_CREATE_ERROR_BY_DETAILS_EMPTY);
        }
        inboundOrderDetailService.updateInboundOrderDetailList(submitVO.getId(), BeanUtils.toBean(inboundOrderDetails, InboundOrderDetailInputVO.class));
        // 查询所有明细
        List<InboundOrderDetailDO> createdDetails = inboundOrderDetailMapper.selectListByOrderId(submitVO.getId());
        inboundOrderDetails.clear();
        inboundOrderDetails.addAll(BeanUtils.toBean(createdDetails, InboundOrderDetailSubmitVO.class));
        // 创建收货单
        return receiptRegistrationService.createReceiptRegistration(submitVO);
    }

    private void validateSystemBatchNumberDuplicate(List<InboundOrderDetailSubmitVO> inboundOrderDetails) {
        validateNoDuplicate(inboundOrderDetails, InboundOrderDetailSubmitVO::getSystemBatchNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShelvedQuantityByInboundOrderDetailId(Long inboundOrderDetailId, Integer shelvedQuantity, Integer shelvedScatteredQuantity, BigDecimal shelvedWeight) {
        InboundOrderDetailDO inboundOrderDetailDO = inboundOrderDetailMapper.selectById(inboundOrderDetailId);
        if (Objects.isNull(inboundOrderDetailDO)) {
            throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_INBOUND_ORDER_DETAIL_EMPTY);
        }
        int oldShelvedQuantity = inboundOrderDetailDO.getShelvedQuantity() == null ? 0 : inboundOrderDetailDO.getShelvedQuantity();
        int oldShelvedScatteredQuantity = inboundOrderDetailDO.getShelvedScatteredQuantity() == null ? 0 : inboundOrderDetailDO.getShelvedScatteredQuantity();
        BigDecimal oldShelvedWeight = inboundOrderDetailDO.getShelvedWeight() == null ? BigDecimal.ZERO : inboundOrderDetailDO.getShelvedWeight();
        InboundOrderDetailDO updateObj = new InboundOrderDetailDO();
        updateObj.setId(inboundOrderDetailId)
                .setShelvedQuantity(oldShelvedQuantity + shelvedQuantity)
                .setShelvedScatteredQuantity(oldShelvedScatteredQuantity + shelvedScatteredQuantity)
                .setShelvedWeight(oldShelvedWeight.add(shelvedWeight));
        // 更新入库单明细
        inboundOrderDetailMapper.updateById(updateObj);
    }

}