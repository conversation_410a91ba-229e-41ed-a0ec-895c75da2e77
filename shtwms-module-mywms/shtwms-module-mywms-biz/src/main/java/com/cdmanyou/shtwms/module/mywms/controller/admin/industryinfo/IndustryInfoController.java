package com.cdmanyou.shtwms.module.mywms.controller.admin.industryinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.*;

import com.cdmanyou.shtwms.module.mywms.controller.admin.industryinfo.vo.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.industryinfo.IndustryInfoDO;
import com.cdmanyou.shtwms.module.mywms.service.industryinfo.IndustryInfoService;

@Tag(name = "管理后台 - 行业信息管理")
@RestController
@RequestMapping("/mywms/industry-info")
@Validated
public class IndustryInfoController {

    @Resource
    private IndustryInfoService industryInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建行业信息管理")
    @PreAuthorize("@ss.hasPermission('mywms:industry-info:create')")
    public CommonResult<Long> createIndustryInfo(@Valid @RequestBody IndustryInfoSaveReqVO createReqVO) {
        return success(industryInfoService.createIndustryInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新行业信息管理")
    @PreAuthorize("@ss.hasPermission('mywms:industry-info:update')")
    public CommonResult<Boolean> updateIndustryInfo(@Valid @RequestBody IndustryInfoSaveReqVO updateReqVO) {
        industryInfoService.updateIndustryInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除行业信息管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:industry-info:delete')")
    public CommonResult<Boolean> deleteIndustryInfo(@RequestParam("id") Long id) {
        industryInfoService.deleteIndustryInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得行业信息管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:industry-info:query')")
    public CommonResult<IndustryInfoRespVO> getIndustryInfo(@RequestParam("id") Long id) {
        IndustryInfoDO industryInfo = industryInfoService.getIndustryInfo(id);
        return success(BeanUtils.toBean(industryInfo, IndustryInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得行业信息管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:industry-info:query')")
    public CommonResult<PageResult<IndustryInfoRespVO>> getIndustryInfoPage(@Valid IndustryInfoPageReqVO pageReqVO) {
        PageResult<IndustryInfoDO> pageResult = industryInfoService.getIndustryInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndustryInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出行业信息管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:industry-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportIndustryInfoExcel(@Valid IndustryInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<IndustryInfoDO> list = industryInfoService.getIndustryInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "行业信息管理.xls", "数据", IndustryInfoRespVO.class,
                        BeanUtils.toBean(list, IndustryInfoRespVO.class));
    }

}