package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 入库异常处理单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InboundExceptionDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26320")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "异常类型（0入库异常，1上架异常）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("异常类型（0入库异常，1上架异常）")
    private Integer exceptionType;

    @Schema(description = "关联的明细表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5262")
    @ExcelProperty("关联的明细表ID")
    private Long relatedDetailId;

    @Schema(description = "业务主表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5058")
    @ExcelProperty("业务主表id")
    private Long businessId;

    @Schema(description = "处理方案")
    @ExcelProperty("处理方案")
    private String handlingScenarios;

    @Schema(description = "处理结果")
    @ExcelProperty("处理结果")
    private String handlingResult;

    @Schema(description = "处理状态（0未处理，1已处理）", example = "2")
    @ExcelProperty("处理状态（0未处理，1已处理）")
    private Integer handlingStatus;

    @Schema(description = "处理人ID")
    @ExcelProperty("处理人ID")
    private Long handlingUser;

    @Schema(description = "处理时间")
    @ExcelProperty("处理时间")
    private LocalDateTime handlingTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}