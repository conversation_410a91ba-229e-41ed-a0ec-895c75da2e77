package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "上架单明细保存实体")
@Data
public class ShelvingManagementDetailSubmitVO {

    @Schema(description = "上架单明细ID（新增时为空）")
    private Long id;

    @Schema(description = "上架单ID")
    private Long shelvingId;

    @Schema(description = "收货单明细ID（分拆时需要赋值）")
    @NotNull(message = "收货单明细ID不能为空")
    private Long receiptDetailId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "系统批次号")
    private String systemBatchNumber;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "收货数量（待上架数量）")
    private Integer receivedQuantity;

    @Schema(description = "收货零散数量（待上架零散数量）")
    private Integer receivedScatteredQuantity;

    @Schema(description = "上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "上架零散数量")
    private Integer shelvedScatteredQuantity;

    @Schema(description = "实收重量")
    private BigDecimal receivedWeight;

    @Schema(description = "上架重量")
    private BigDecimal shelvedWeight;

    @Schema(description = "货位ID")
    private Long warehouseLocationId;

    @Schema(description = "所属仓类别（0成品，1半成品，2原料）")
    private Integer houseType;

    @Schema(description = "去重编码（前端不用传）")
    private String duplicateCode;

}
