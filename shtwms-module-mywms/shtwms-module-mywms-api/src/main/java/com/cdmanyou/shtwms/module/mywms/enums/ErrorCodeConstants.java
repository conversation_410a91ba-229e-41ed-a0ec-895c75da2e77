package com.cdmanyou.shtwms.module.mywms.enums;
import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;

// TODO 待办：请将下面的错误码复制到 shtwms-module-mywms-api 模块的 ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
public interface ErrorCodeConstants {
    ErrorCode CUSTOMER_NOT_EXISTS = new ErrorCode(10001, "客户管理不存在");
    ErrorCode INDUSTRY_INFO_NOT_EXISTS = new ErrorCode(10002, "行业信息不存在");
    ErrorCode PRODUCT_NOT_EXISTS = new ErrorCode(10003, "产品不存在");
    ErrorCode PRODUCT_CATEGORY_NOT_EXISTS = new ErrorCode(10004, "产品分类不存在");
    ErrorCode SHELF_NOT_EXISTS = new ErrorCode(10005, "货架不存在");
    ErrorCode SUPPLIER_NOT_EXISTS = new ErrorCode(10006, "供应商不存在");
    ErrorCode TRAY_NOT_EXISTS = new ErrorCode(10007, "托盘不存在");
    ErrorCode WARE_HOUSE_NOT_EXISTS = new ErrorCode(10008, "仓库不存在");
    ErrorCode WARE_HOUSE_AREA_NOT_EXISTS = new ErrorCode(10009, "货区不存在");
    ErrorCode WARE_HOUSE_LOCATION_NOT_EXISTS = new ErrorCode(10010, "货位不存在");
    ErrorCode OUTBOUND_ORDER_DETAIL_NOT_EXISTS = new ErrorCode(10011, "出库单详情不存在");
    ErrorCode OUTBOUND_ORDER_NOT_EXISTS = new ErrorCode(10012, "出库单不存在");
    ErrorCode PICKING_ORDER_DETAIL_NOT_EXISTS = new ErrorCode(10013, "总捡明细不存在");
    ErrorCode PICKING_ORDER_NOT_EXISTS = new ErrorCode(10014, "总捡单不存在");
    ErrorCode SORTING_TASK_ORDER_DETAIL_NOT_EXISTS = new ErrorCode(10015, "分捡明细不存在");
    ErrorCode SORTING_TASK_ORDER_NOT_EXISTS = new ErrorCode(10016, "分捡单不存在");
    ErrorCode WAVE_ORDER_DETAIL_NOT_EXISTS = new ErrorCode(10017, "波次单明细不存在");
    ErrorCode WAVE_ORDER_NOT_EXISTS = new ErrorCode(10018, "波次单不存在");
    ErrorCode INVENTORY_MANAGEMENT_NOT_EXISTS = new ErrorCode(10019, "库存不存在");
    ErrorCode TRANSFER_DETAIL_NOT_EXISTS = new ErrorCode(10019, "货品转移明细不存在");
    ErrorCode TRANSFER_NOT_EXISTS = new ErrorCode(10020, "货品转移不存在");
    ErrorCode PRODUCT_WAREHOUSE_LOCATION_NOT_EXISTS = new ErrorCode(10021, "货品货位不存在");
    ErrorCode CUSTOMER_CODE_DUPLICATE = new ErrorCode(10022, "客户编码已存在");
    ErrorCode CUSTOMER_NAME_DUPLICATE = new ErrorCode(10023, "客户名称已存在");
    ErrorCode PRODUCT_CATEGORY_NAME_DUPLICATE = new ErrorCode(10024, "货品类别名称已存在");
    ErrorCode PRODUCT_CATEGORY_CODE_DUPLICATE = new ErrorCode(10025, "货品类别编码已存在");
    ErrorCode PRODUCT_CODE_DUPLICATE = new ErrorCode(10026, "货品编码已存在");
    ErrorCode CUSTOMER_ALREADY_DISABLED= new ErrorCode(10027, "客户已停用，无需重复操作");
    ErrorCode CUSTOMER_ALREADY_ENABLED= new ErrorCode(10028, "客户已启用，无需重复操作");
    ErrorCode PRODUCT_CATEGORY_ALREADY_ENABLED= new ErrorCode(10029, "货品类别已经处于启用状态");
    ErrorCode PRODUCT_CATEGORY_ALREADY_DISABLED= new ErrorCode(10030, "货品类别已经处于停用状态");
    ErrorCode SUPPLIER_ALREADY_ENABLED = new ErrorCode(10031, "供应商已启用，无需重复操作");
    ErrorCode SUPPLIER_ALREADY_DISABLED = new ErrorCode(10032, "供应商已停用，无需重复操作");
    ErrorCode SUPPLIER_CODE_DUPLICATE = new ErrorCode(10033, "供应商编码已存在");
    ErrorCode SUPPLIER_NAME_DUPLICATE = new ErrorCode(10034, "供应商名称已存在");
    ErrorCode PRODUCT_ALREADY_DISABLED = new ErrorCode(10035, "货品已停用，无需重复操作");
    ErrorCode PRODUCT_ALREADY_ENABLED = new ErrorCode(10036, "货品已启用，无需重复操作");
    ErrorCode PRODUCER_ALREADY_ENABLED = new ErrorCode(10037, "货主已启用，无需重复操作");
    ErrorCode PRODUCER_ALREADY_DISABLED = new ErrorCode(10038, "货主已停用，无需重复操作");
    ErrorCode WARE_HOUSE_CODE_DUPLICATE = new ErrorCode(10039, "已存在仓库名");
    ErrorCode WARE_HOUSE_NAME_DUPLICATE = new ErrorCode(10040, "已存在仓库编码");
    ErrorCode TRAY_CODE_DUPLICATE = new ErrorCode(10041, "托盘码已存在");
    ErrorCode PRODUCT_ALREADY_FREEZED = new ErrorCode(10042, "货品已冻结");
    ErrorCode PRODUCT_ALREADY_UNFREEZED = new ErrorCode(10043, "货品未冻结");










    // 入库模块

    ErrorCode INBOUND_ORDER_NOT_EXISTS = new ErrorCode(1001_10001, "入库单不存在");
    ErrorCode INBOUND_ORDER_SUBMIT_ERROR_BY_STATUS = new ErrorCode(1001_10002, "入库单已提交或已作废，无法再次提交");
    ErrorCode RECEIPT_REGISTRATION_CREATE_ERROR_BY_DETAILS_EMPTY = new ErrorCode(1001_10003, "提交失败，货品明细不能为空");
    ErrorCode PRODUCER_NOT_EXISTS = new ErrorCode(1001_10004, "货主不存在");
    ErrorCode RECEIPT_REGISTRATION_NOT_EXISTS = new ErrorCode(1001_10005, "收货单不存在");
    ErrorCode RECEIPT_REGISTRATION_ALREADY_SUBMITTED = new ErrorCode(1001_10006, "收货单已提交，无法重复提交");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_DETAILS_EMPTY = new ErrorCode(1001_10007, "操作失败，货品明细不能为空");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_PENDING_QUANTITY_EMPTY = new ErrorCode(1001_10008, "提交失败，货品的待入库数量不能为空");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_PENDING_SCATTERED_QUANTITY_EMPTY = new ErrorCode(1001_10009, "提交失败，货品的待入库零散数量不能为空");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_RECEIVED_QUANTITY_EMPTY = new ErrorCode(1001_10010, "提交失败，{}货品的实收数量不能为空");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_RECEIVED_SCATTERED_QUANTITY_EMPTY = new ErrorCode(1001_10011, "提交失败，{}货品的实收零散数量不能为空");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_PRODUCT_BATCH_NUMBER_EMPTY = new ErrorCode(1001_10012, "操作失败，货品的货品批次号不能为空");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_PRODUCTION_DATE_EMPTY = new ErrorCode(1001_10013, "提交失败，{}货品的生产日期不能为空");
    ErrorCode RECEIPT_REGISTRATION_SAVE_ERROR_BY_RECEIVED_WEIGHT_EMPTY = new ErrorCode(1001_10014, "提交失败，{}货品的实收重量不能为空");
    ErrorCode INBOUND_ORDER_DETAIL_PENDING_QUANTITY_MISMATCH = new ErrorCode(1001_10015, "该收货单中货品编码为{}，生产日期为{}的待收数量合计与对应入库单货品入库数量不匹配");
    ErrorCode CREATE_SHELVING_ERROR_BY_RECEIPT_REGISTRATION_EMPTY = new ErrorCode(1001_10016, "选中的收货单均不存在，请刷新后重试");
    ErrorCode CREATE_SHELVING_ERROR_BY_RECEIPT_REGISTRATION_STATUS = new ErrorCode(1001_10017, "创建失败，选中的上架单中存在未提交或已上架的收货单");
    ErrorCode CREATE_SHELVING_ERROR_BY_RECEIVED_QUANTITY_TOTAL_EMPTY = new ErrorCode(1001_10018, "创建失败，收货单号为{}的收货单实收货品总数不能为空");
    ErrorCode CREATE_SHELVING_ERROR_BY_RECEIVED_SCATTERED_QUANTITY_TOTAL_EMPTY = new ErrorCode(1001_10019, "创建失败，收货单号为{}的收货单实收货品零散总数不能为空");
    ErrorCode CREATE_SHELVING_ERROR_BY_DIFFERENT_WAREHOUSE = new ErrorCode(1001_10020, "创建失败，选择的收货单必须为同一个仓库中的收货单");
    ErrorCode SHELVING_MANAGEMENT_NOT_EXISTS = new ErrorCode(1001_10021, "上架单不存在");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_DETAILS_EMPTY = new ErrorCode(1001_10022, "操作失败，货品明细不能为空");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_DUPLICATE_CODE = new ErrorCode(1001_10023, "提交失败，相同货主的同一批次的同种货品无法重复");
    ErrorCode SHELVING_MANAGEMENT_ALREADY_SUBMIT = new ErrorCode(1001_10024, "上架单已提交，无法重复提交");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_LOCATION_ID_EMPTY = new ErrorCode(1001_10025, "提交失败，{}货品的上架货位不能为空");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_SHELVING_QUANTITY_EMPTY = new ErrorCode(1001_10026, "提交失败，{}货品的上架数量不能为空");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_SHELVING_SCATTERED_QUANTITY_EMPTY = new ErrorCode(1001_10027, "提交失败，{}货品的上架零散数量不能为空");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_SHELVING_WEIGHT_EMPTY = new ErrorCode(1001_10028, "提交失败，{}货品的上架重量不能为空");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_RECEIPT_REGISTRATION_DETAILS_EMPTY = new ErrorCode(1001_10029, "提交失败，该上架单所属收货单的货品明细为空");
    ErrorCode SHELVING_MANAGEMENT_SAVE_ERROR_BY_INBOUND_ORDER_DETAIL_EMPTY = new ErrorCode(1001_10030, "提交失败，该上架单所属入库单的货品明细不存在");
    ErrorCode SHELVING_MANAGEMENT_DETAIL_RECEIVED_QUANTITY_MISMATCH = new ErrorCode(1001_10031, "该上架单中货品编码为{}，生产日期为{}的实收数量合计与对应收货单货品实收数量不匹配");




}