package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 入库异常处理单主 DO
 *
 * <AUTHOR>
 */
@TableName("inbound_exception")
@KeySequence("inbound_exception_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InboundExceptionDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 关联单据号（收货登记单号/上架单号）
     */
    private String relatedOrderCode;
    /**
     * 异常类型
     */
    private String exceptionType;
    /**
     * 处理方案（非必填）
     */
    private String solution;
    /**
     * 处理结果（非必填）
     */
    private String result;
    /**
     * 状态
     */
    private String status;
    /**
     * 处理人（非必填）
     */
    private String handler;
    /**
     * 处理时间（非必填）
     */
    private LocalDateTime handleTime;

}