package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;

import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.InboundOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundOrderDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 入库单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InboundOrderDetailServiceImpl implements InboundOrderDetailService {

    @Resource
    private InboundOrderDetailMapper inboundOrderDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createInboundOrderDetailList(Long orderId, List<InboundOrderDetailInputVO> list) {
        List<InboundOrderDetailDO> bean = BeanUtils.toBean(list, InboundOrderDetailDO.class);
        bean.forEach(o -> {
            o.setId(null);
            // 设置入库单号
            o.setInboundOrderId(orderId);
            // 设置系统批次号
            if (o.getProductionDate() != null) {
                String dateStr = o.getProductionDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                o.setSystemBatchNumber(dateStr + o.getProductCode());
            }
        });
        inboundOrderDetailMapper.insertBatch(bean);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInboundOrderDetailList(Long orderId, List<InboundOrderDetailInputVO> list) {
        inboundOrderDetailMapper.deleteByOrderId(orderId);
        createInboundOrderDetailList(orderId, list);
    }
}