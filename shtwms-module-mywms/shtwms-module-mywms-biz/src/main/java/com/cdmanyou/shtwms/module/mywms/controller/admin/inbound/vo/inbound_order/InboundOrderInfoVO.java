package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailInfoVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;

@Schema(description = "入库单详情实体")
@Data
public class InboundOrderInfoVO {

    @Schema(description = "入库单ID")
    private Long id;

    @Schema(description = "入库单号")
    private String orderCode;

    @Schema(description = "入库类型（0采购入库，1退货入库，2其他入库）", example = "2")
    private Integer inboundType;

    @Schema(description = "入库单状态（0草稿,1已提交,2作废）", example = "2")
    private Integer inboundOrderStatus;

    @Schema(description = "货主ID（关联客户表）", example = "13373")
    private Long producerId;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "货主编码")
    private String producerCode;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "单位ID")
    private Long unitId;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "单位类别（0供应商，1客户）")
    private Integer unitType;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建方式(0录入，1导入)")
    private Integer createType;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "入库状态(0待收货，1待上架，2已入库)")
    private Integer status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "到货登记时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime acceptanceCompleteTime;

    @Schema(description = "验收完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime receiptCompleteTime;

    @Schema(description = "上架完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime shelvingCompleteTime;

    @Schema(description = "待入库商品总数")
    private Integer pendingQuantityTotal;

    @Schema(description = "待入库商品零散总数")
    private Integer pendingScatteredQuantityTotal;

    @Schema(description = "上货单明细")
    private List<InboundOrderDetailInfoVO> inboundOrderDetails;

}
