package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.supplier;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 供应商管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SupplierRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3726")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "供应商编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("供应商编码")
    private String code;

    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("供应商名称")
    private String name;

    @Schema(description = "供应商简称", example = "芋艿")
    @ExcelProperty("供应商简称")
    private String shortName;

    @Schema(description = "供应商类型", example = "供应商类型（1内部，2外部，3其他）")
    @ExcelProperty("供应商类型")
    private String supplier_type;

    @Schema(description = "供应商英文名称", example = "张三")
    @ExcelProperty("供应商英文名称")
    private String englishName;

    @Schema(description = "供应商级别")
    @ExcelProperty("供应商级别")
    private String level;

    @Schema(description = "所属行业")
    @ExcelProperty("所属行业")
    private String industry;

    @Schema(description = "营业执照号")
    @ExcelProperty("营业执照号")
    private String businessLicense;

    @Schema(description = "供应商描述", example = "你猜")
    @ExcelProperty("供应商描述")
    private String description;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    @ExcelProperty("联系邮箱")
    private String contactEmail;

    @Schema(description = "所在地区(省市区)")
    @ExcelProperty("所在地区(省市区)")
    private String region;

    @Schema(description = "信用额度")
    @ExcelProperty("信用额度")
    private BigDecimal creditLimit;

    @Schema(description = "结算方式")
    @ExcelProperty("结算方式")
    private String settlementMethod;

    @Schema(description = "启用状态（0启用，1禁用）", example = "1")
    private Integer status;

    @Schema(description = "合作状态", example = "2")
    @ExcelProperty("合作状态")
    private String cooperationStatus;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "详细地址")
    @ExcelProperty("详细地址")
    private String fullAddress;

}