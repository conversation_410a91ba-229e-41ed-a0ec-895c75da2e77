package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception_detail.InboundExceptionDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 入库异常处理单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InboundExceptionDetailMapper extends BaseMapperX<InboundExceptionDetailDO> {

    default PageResult<InboundExceptionDetailDO> selectPage(InboundExceptionDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InboundExceptionDetailDO>()
                .eqIfPresent(InboundExceptionDetailDO::getExceptionType, reqVO.getExceptionType())
                .eqIfPresent(InboundExceptionDetailDO::getRelatedDetailId, reqVO.getRelatedDetailId())
                .eqIfPresent(InboundExceptionDetailDO::getBusinessId, reqVO.getBusinessId())
                .eqIfPresent(InboundExceptionDetailDO::getHandlingScenarios, reqVO.getHandlingScenarios())
                .eqIfPresent(InboundExceptionDetailDO::getHandlingResult, reqVO.getHandlingResult())
                .eqIfPresent(InboundExceptionDetailDO::getHandlingStatus, reqVO.getHandlingStatus())
                .eqIfPresent(InboundExceptionDetailDO::getHandlingUser, reqVO.getHandlingUser())
                .betweenIfPresent(InboundExceptionDetailDO::getHandlingTime, reqVO.getHandlingTime())
                .betweenIfPresent(InboundExceptionDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InboundExceptionDetailDO::getId));
    }

//    default List<InboundExceptionDetailDO> selectListByExceptionId(Long exceptionId) {
//        return selectList(InboundExceptionDetailDO::getExceptionId, exceptionId);
//    }
//
//    default int deleteByExceptionId(Long exceptionId) {
//        return delete(InboundExceptionDetailDO::getExceptionId, exceptionId);
//    }

}