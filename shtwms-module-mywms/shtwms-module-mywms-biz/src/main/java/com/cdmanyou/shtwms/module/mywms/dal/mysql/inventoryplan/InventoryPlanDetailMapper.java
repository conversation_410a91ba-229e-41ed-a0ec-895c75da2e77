package com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 盘点计划明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryPlanDetailMapper extends BaseMapperX<InventoryPlanDetailDO> {

    default PageResult<InventoryPlanDetailDO> selectPage(InventoryPlanDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryPlanDetailDO>()
                .eqIfPresent(InventoryPlanDetailDO::getInventoryPlanId, reqVO.getInventoryPlanId())
                .eqIfPresent(InventoryPlanDetailDO::getProductCode, reqVO.getProductCode())
                .likeIfPresent(InventoryPlanDetailDO::getProductName, reqVO.getProductName())
                .betweenIfPresent(InventoryPlanDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryPlanDetailDO::getId));
    }


    default List<InventoryPlanDetailDO> selectListByInventoryPlanId(Long inventoryPlanId) {
        return selectList(InventoryPlanDetailDO::getInventoryPlanId, inventoryPlanId);
    }

    default int deleteByInventoryPlanId(Long inventoryPlanId) {
        return delete(InventoryPlanDetailDO::getInventoryPlanId, inventoryPlanId);
    }




}