package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail;

import com.cdmanyou.shtwms.framework.common.util.date.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;

@Schema(description = "入库单商品明细录入/编辑实体")
@Data
public class InboundOrderDetailInputVO {

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "商品单价")
    private BigDecimal productPrice;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "到货时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime arrivalTime;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "待入库零散数量")
    private Integer pendingScatteredQuantity;

    @Schema(description = "拆零数量")
    private Integer looseQuantity;

    @Schema(description = "入库重量")
    private BigDecimal inboundWeight;

    @Schema(description = "入库体积")
    private BigDecimal inboundVolume;
}
