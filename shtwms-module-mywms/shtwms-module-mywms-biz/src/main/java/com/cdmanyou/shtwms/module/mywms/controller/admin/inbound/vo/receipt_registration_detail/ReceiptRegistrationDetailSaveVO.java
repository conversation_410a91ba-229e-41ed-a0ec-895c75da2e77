package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "收货单明细保存实体")
@Data
public class ReceiptRegistrationDetailSaveVO {

    @Schema(description = "收货明细ID（新增时为空）")
    private Long id;

    @Schema(description = "入库单明细ID")
    @NotNull(message = "入库单明细ID不能为空")
    private Long inboundOrderDetailId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "系统批次号")
    private String systemBatchNumber;

    @Schema(description = "待入库数量")
    @NotNull(message = "待入库数量不能为空")
    private Integer pendingQuantity;

    @Schema(description = "实收数量")
    private Integer receivedQuantity;

    @Schema(description = "待入库零散数量")
    @NotNull(message = "待入库零散数量不能为空")
    private Integer pendingScatteredQuantity;

    @Schema(description = "实收零散数量")
    private Integer receivedScatteredQuantity;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "待入库重量")
    @NotNull(message = "待入库重量不能为空")
    private BigDecimal inboundWeight;

    @Schema(description = "实收重量")
    private BigDecimal receivedWeight;

    @Schema(description = "差异备注（非必填）")
    private String differenceRemark;
}
