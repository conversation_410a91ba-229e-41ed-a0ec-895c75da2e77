package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSubmitVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "上架单保存/提交请求实体")
@Data
public class ShelvingManagementSubmitVO {

    @Schema(description = "保存标识（0保存，1提交）")
    private Integer saveFlag;

    @Schema(description = "上架单ID")
    private Long id;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "收货单ID")
    private Long receiptId;

    @Schema(description = "入库单ID")
    private Long inboundOrderId;

    @Schema(description = "是否整单冻结（0否1是）")
    private Integer freezeType;

    @Schema(description = "上架时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime shelvingTime;

    @Schema(description = "上架单明细列表")
    private List<ShelvingManagementDetailSubmitVO> shelvingManagementDetails;

}