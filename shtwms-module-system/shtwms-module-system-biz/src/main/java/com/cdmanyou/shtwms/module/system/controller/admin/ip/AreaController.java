package com.cdmanyou.shtwms.module.system.controller.admin.ip;

import cn.hutool.core.lang.Assert;
import cn.hutool.db.Page;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.ip.core.Area;
import com.cdmanyou.shtwms.framework.ip.core.enums.AreaTypeEnum;
import com.cdmanyou.shtwms.framework.ip.core.utils.AreaUtils;
import com.cdmanyou.shtwms.framework.ip.core.utils.IPUtils;
import com.cdmanyou.shtwms.module.system.controller.admin.ip.vo.AreaNodeRespVO;
import com.cdmanyou.shtwms.module.system.controller.admin.ip.vo.AreaPageRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 地区")
@RestController
@RequestMapping("/system/area")
@Validated
public class AreaController {

    @GetMapping("/tree")
    @Operation(summary = "获得地区树")
    public CommonResult<List<AreaNodeRespVO>> getAreaTree() {
        Area area = AreaUtils.getArea(Area.ID_CHINA);
        Assert.notNull(area, "获取不到中国");
        return success(BeanUtils.toBean(area.getChildren(), AreaNodeRespVO.class));
    }

    @GetMapping("/get-by-ip")
    @Operation(summary = "获得 IP 对应的地区名")
    @Parameter(name = "ip", description = "IP", required = true)
    public CommonResult<String> getAreaByIp(@RequestParam("ip") String ip) {
        // 获得城市
        Area area = IPUtils.getArea(ip);
        if (area == null) {
            return success("未知");
        }
        // 格式化返回
        return success(AreaUtils.format(area.getId()));
    }

    @GetMapping("/list")
    @Operation(summary = "获得中国地区下的所有地区完整列表（不分页）")
    public CommonResult<List<AreaPageRespVO>> getAllAreas() {
        // 获取中国节点
        Area china = AreaUtils.getArea(Area.ID_CHINA);
        Assert.notNull(china, "获取不到中国");

        // 递归收集所有地区节点（包括省、市、区县）
        List<Area> allAreas = new ArrayList<>();
        collectAllAreas(china, allAreas);

        // 转换为VO
        List<AreaPageRespVO> voList = allAreas.stream()
                .map(area -> {
                    AreaPageRespVO vo = new AreaPageRespVO();
                    vo.setId(area.getId());
                    vo.setName(area.getName());
                    vo.setType(area.getType());
                    vo.setParentId(area.getParent() != null ? area.getParent().getId() : null);
                    return vo;
                })
                .collect(Collectors.toList());

        return success(voList);
    }

    // 递归收集所有地区节点（包括省、市、区县）
    private void collectAllAreas(Area area, List<Area> result) {
        if (area == null) return;

        // 跳过中国节点本身（ID=1）
        if (!area.getId().equals(Area.ID_CHINA)) {
            result.add(area);
        }

        // 递归遍历子节点
        if (area.getChildren() != null) {
            for (Area child : area.getChildren()) {
                collectAllAreas(child, result);
            }
        }
    }



}
