package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 上架管理单主分页 Request VO")
@Data
public class ShelvingManagementPageReqVO extends PageParam {

    @Schema(description = "上架单号")
    private String shelvingCode;

    @Schema(description = "仓库ID", example = "10765")
    private Long warehouseId;

    @Schema(description = "上架单状态（0未上架，1已上架）", example = "2")
    private Integer shelvingStatus;

    @Schema(description = "上架员名称")
    private String shelvingOperatorName;

    @Schema(description = "上架时间搜索开始时间")
    private String shelvingSearchStartTime;

    @Schema(description = "上架时间搜索结束时间")
    private String shelvingSearchEndTime;

}