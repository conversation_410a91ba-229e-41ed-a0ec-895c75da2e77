package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.InventoryManagementDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.InventoryManagementMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.INVENTORY_MANAGEMENT_NOT_EXISTS;

/**
 * 库存管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryManagementServiceImpl implements InventoryManagementService {

    @Resource
    private InventoryManagementMapper inventoryManagementMapper;

    @Override
    public Long createInventoryManagement(InventoryManagementSaveReqVO createReqVO) {
        // 插入
        InventoryManagementDO inventoryManagement = BeanUtils.toBean(createReqVO, InventoryManagementDO.class);
        inventoryManagementMapper.insert(inventoryManagement);
        // 返回
        return inventoryManagement.getId();
    }

    @Override
    public void updateInventoryManagement(InventoryManagementSaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryManagementExists(updateReqVO.getId());
        // 更新
        InventoryManagementDO updateObj = BeanUtils.toBean(updateReqVO, InventoryManagementDO.class);
        inventoryManagementMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventoryManagement(Long id) {
        // 校验存在
        validateInventoryManagementExists(id);
        // 删除
        inventoryManagementMapper.deleteById(id);
    }

    private void validateInventoryManagementExists(Long id) {
        if (inventoryManagementMapper.selectById(id) == null) {
            throw exception(INVENTORY_MANAGEMENT_NOT_EXISTS);
        }
    }

    @Override
    public InventoryManagementDO getInventoryManagement(Long id) {
        return inventoryManagementMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryManagementDO> getInventoryManagementPage(InventoryManagementPageReqVO pageReqVO) {
        return inventoryManagementMapper.selectPage(pageReqVO);
    }

}