package com.cdmanyou.shtwms.module.mywms.service.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDetailDO;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 入库单 Service 接口
 *
 * <AUTHOR>
 */
public interface InboundOrderService {

    /**
     * 创建入库单 - 录入
     *
     * @param inputVO 录入信息
     * @return 编号
     */
    Long inputInboundOrder(@Valid InboundOrderInputVO inputVO);

    /**
     * 更新入库单
     *
     * @param inputVO 更新信息
     */
    void updateInboundOrder(@Valid InboundOrderInputVO inputVO);

    /**
     * 通过入库单号获取入库单详情
     *
     * @param code 入库单号
     * @return 入库单详情
     */
    InboundOrderInfoVO getInboundOrderByCode(String code);

    /**
     * 获得入库单分页
     *
     * @param pageReqVO 分页查询
     * @return 入库单分页
     */
    PageResult<InboundOrderPageRespVO> getInboundOrderPage(InboundOrderPageReqVO pageReqVO);

    /**
     * 提交入库单 - 生成收货单
     *
     * @param submitVO 提交信息
     */
    String submitInboundOrder(@Valid InboundOrderSubmitVO submitVO);

    /**
     * 修改入库单明细上架数量 - 上架单提交
     *
     * @param inboundOrderDetailId 入库单明细ID
     * @param shelvedQuantity 上架数量
     * @param shelvedScatteredQuantity 上架零散数量
     * @param shelvedWeight 上架重量
     */
    void updateShelvedQuantityByInboundOrderDetailId(Long inboundOrderDetailId, Integer shelvedQuantity, Integer shelvedScatteredQuantity, BigDecimal shelvedWeight);

    /**
     * 作废入库单
     *
     * @param id 入库单ID
     */
    void voidInboundOrderById(Long id);

    /**
     * 获得入库单导出列表
     *
     * @return 入库单导出列表
     */
    List<InboundOrderExportVO> getInboundOrderExportList();
}