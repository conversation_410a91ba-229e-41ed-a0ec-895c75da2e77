package com.cdmanyou.shtwms.module.mywms.service.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 上架管理单 Service 接口
 *
 * <AUTHOR>
 */
public interface ShelvingManagementService {

    /**
     * 创建上架管理单 - 选择收货单创建
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createManagement(@Valid ShelvingManagementCreateVO createReqVO);

    /**
     * 更新上架管理单
     *
     * @param updateReqVO 更新信息
     */
    void updateManagement(@Valid ShelvingManagementSaveVO updateReqVO);

    /**
     * 删除上架管理单
     *
     * @param id 编号
     */
    void deleteManagement(Long id);

    /**
     * 通过上架单号获取上架单详情
     *
     * @param code 上架单号
     * @return 上架单详情
     */
    ShelvingManagementInfoVO getShelvingManagementByCode(String code);

    /**
     * 获得上架管理单分页
     *
     * @param pageReqVO 分页查询
     * @return 上架管理单分页
     */
    PageResult<ShelvingManagementPageRespVO> getManagementPage(ShelvingManagementPageReqVO pageReqVO);

    // ==================== 子表（上架管理单明细） ====================

    /**
     * 获得上架管理单明细列表
     *
     * @param shelvingId 上架单ID（关联主表）
     * @return 上架管理单明细列表
     */
    List<ShelvingManagementDetailDO> getShelvingManagementDetailListByShelvingId(Long shelvingId);

    /**
     * 保存/提交上架单
     *
     * @param saveReqVO 保存信息
     */
    void saveShelvingManagement(@Valid ShelvingManagementSaveVO saveReqVO);
}