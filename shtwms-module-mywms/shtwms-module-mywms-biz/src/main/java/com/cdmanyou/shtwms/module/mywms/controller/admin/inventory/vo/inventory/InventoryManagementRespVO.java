package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryManagementRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9309")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "仓库编码（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("仓库编码（关联仓库表）")
    private String warehouseCode;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("仓库名称")
    private String warehouseName;

    @Schema(description = "库区编码（关联库区表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库区编码（关联库区表）")
    private String areaCode;

    @Schema(description = "库区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("库区名称")
    private String areaName;

    @Schema(description = "库位编码（关联库位表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库位编码（关联库位表）")
    private String locationCode;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "商品规格")
    @ExcelProperty("商品规格")
    private String specification;

    @Schema(description = "计量单位")
    @ExcelProperty("计量单位")
    private String unit;

    @Schema(description = "批次号")
    @ExcelProperty("批次号")
    private String batchNumber;

    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate productionDate;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存数量")
    private Integer quantity;

    @Schema(description = "库存状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("库存状态")
    private String status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}