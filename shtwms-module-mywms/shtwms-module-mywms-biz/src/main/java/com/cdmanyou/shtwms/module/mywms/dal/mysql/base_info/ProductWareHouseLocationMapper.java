package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.bo.ProductAndHouseBO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.bo.ProductHouseLocationBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.security.core.parameters.P;

import java.time.LocalDate;
import java.util.List;

/**
 * 商品货位关联表-记录货位上的商品数量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductWareHouseLocationMapper extends BaseMapperX<ProductWareHouseLocationDO> {

    Page<ProductWareHouseLocationPageRespVO> selectPage(Page<ProductWareHouseLocationPageRespVO> page,
                                                        @Param("reqVO") ProductWareHouseLocationPageReqVO reqVO);

    Long selectPageCount(@Param("reqVO") ProductWareHouseLocationPageReqVO pageReqVO);

    @Select("        select wl.area_id,pwl.id,pwl.warehouse_location_id,pwl.available,pwl.occupation  from shtwms.product_warehouse_location pwl " +
            "        left join shtwms.warehouse_location wl on pwl.warehouse_location_id = wl.id " +
            "        where pwl.product_id = #{productId} and pwl.production_date = #{productionDate}  and wl.warehouse_id = #{warehouseLocationId} " +
            "        order by available desc")
    List<ProductHouseLocationBO> selectListByWareHouse(@Param("productId") Long productId, @Param("productionDate") LocalDate productionDate, @Param("warehouseLocationId") String warehouseLocationId);

    @Select("                select pwl.*,wl.warehouse_id from shtwms.product_warehouse_location pwl" +
            "                INNER  join shtwms.warehouse_location wl on pwl.warehouse_location_id = wl.id" +
            "                where pwl.product_id = #{productId} and pwl.production_date = #{productionDate}" +
            "                order by scattered_available desc, available desc")
    List<ProductAndHouseBO> selectListByProductIdAndProductionDate(@Param("productId") Long productId, @Param("productionDate") LocalDate productionDate);




    @Select("SELECT * FROM product_warehouse_location WHERE producer_id = #{producerId} AND product_id = #{productId} AND production_date = #{productionDate} AND warehouse_location_id = #{warehouseLocationId} AND deleted = 0")
    ProductWareHouseLocationDO getByShelvingDetail(@Param("producerId") Long producerId, @Param("productId") Long productId, @Param("productionDate") LocalDate productionDate, @Param("warehouseLocationId") Long warehouseLocationId);


    ProductWareHouseLocationRespVO getProductWarehouseLocationById(@Param("id") Long id);

    void freezeProduct(@Param("productId") Long productId, @Param("productBatchNumber") String productBatchNumber);

    void unfreezeProduct(@Param("productId") Long productId, @Param("productBatchNumber") String productBatchNumber);

    ProductWareHouseLocationDO selectByConditions(@Param("producerId") Long producerId,
                                        @Param("productId") Long productId,
                                        @Param("productionDate") String productionDate,
                                        @Param("fromWareHouseLocationId") Long fromWareHouseLocationId);

    SimpleLocationStatisticVO getSimpleLocationStatistics();

    SimpleInventoryStatisticVO getSimpleInventoryStatistics();

    SimpleExpirationStatisticVO getSimpleExpirationStatistics();

    Page<InventoryPageRespVO> getInventoryPage(Page<InventoryPageRespVO> page,
                                               @Param("reqVO") InventoryPageReqVO pageReqVO);

    Long getInventoryPageCount(@Param("reqVO") InventoryPageReqVO pageReqVO);

    Page<ExpirationPageRespVO> getExpirationPage(Page<ExpirationPageRespVO> page,
                                                 @Param("reqVO") ExpirationPageReqVO pageReqVO);

    Long getExpirationPageCount(@Param("reqVO") ExpirationPageReqVO pageReqVO);

}