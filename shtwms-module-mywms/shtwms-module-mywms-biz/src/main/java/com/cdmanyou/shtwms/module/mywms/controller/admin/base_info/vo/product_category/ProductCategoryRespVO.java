package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品类别管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductCategoryRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15616")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "类别编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("类别编码")
    private String code;

    @Schema(description = "类别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("类别名称")
    private String name;

    @Schema(description = "类别描述", example = "随便")
    @ExcelProperty("类别描述")
    private String description;

    @Schema(description = "父类别ID", example = "18790")
    @ExcelProperty("父类别ID")
    private Long parentId;

    @Schema(description = "是否启用(1启用/0停用)", example = "2")
    @ExcelProperty("是否启用(1启用/0停用)")
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}