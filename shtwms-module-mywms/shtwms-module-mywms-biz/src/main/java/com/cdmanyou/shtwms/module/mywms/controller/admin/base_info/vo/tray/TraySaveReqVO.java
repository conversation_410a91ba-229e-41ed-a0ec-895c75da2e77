package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 托盘管理新增/修改 Request VO")
@Data
public class TraySaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31372")
    private Long id;

    @Schema(description = "托盘编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "托盘编码不能为空")
    private String code;

    @Schema(description = "所属仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "8756")
    @NotNull(message = "所属仓库ID（关联仓库表）不能为空")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "27569")
    @NotNull(message = "所属库区ID（关联库区表）不能为空")
    private Long areaId;

    @Schema(description = "托盘名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "托盘名称不能为空")
    private String name;

    @Schema(description = "托盘类型", example = "2")
    private String type;

    @Schema(description = "托盘容量(吨)")
    private BigDecimal capacity;

    @Schema(description = "条码号")
    private String barcode;

    @Schema(description = "状态(1启用/0停用)", example = "1")
    private Integer status;

}