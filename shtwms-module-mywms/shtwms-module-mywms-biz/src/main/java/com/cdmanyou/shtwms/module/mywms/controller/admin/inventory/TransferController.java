package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferInputVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import com.cdmanyou.shtwms.module.mywms.service.inventory.TransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 货品转移")
@RestController
@RequestMapping("/mywms/transfer")
@Validated
public class TransferController {

    @Resource
    private TransferService transferService;

    @PostMapping("/create")
    @Operation(summary = "创建货品转移")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:create')")
    public CommonResult<Long> createTransfer(@Valid @RequestBody TransferInputVO inputVO) {
        return success(transferService.createTransfer(inputVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货品转移")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:update')")
    public CommonResult<Boolean> updateTransfer(@Valid @RequestBody TransferInputVO inputVO) {
        transferService.updateTransfer(inputVO);
        return success(true);
    }

    @GetMapping("/voidByCode")
    @Operation(summary = "通过转移单单号作废转移单")
    @Parameter(name = "code", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:transfer:update')")
    public CommonResult<Boolean> voidByCode(@RequestParam("code") String code) {
        transferService.voidByCode(code);
        return success(true);
    }

    @GetMapping("/completeByCode")
    @Operation(summary = "通过转移单单号完成转移单")
    @Parameter(name = "code", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:transfer:update')")
    public CommonResult<Boolean> completeByCode(@RequestParam("code") String code) {
        transferService.completeByCode(code);
        return success(true);
    }

    @GetMapping("/getByCode")
    @Operation(summary = "获得货品转移")
    @Parameter(name = "code", description = "转移单号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:query')")
    public CommonResult<TransferRespVO> getTransferByCode(@RequestParam("code") String code) {
        return success(transferService.getTransferByCode(code));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货品转移分页")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:query')")
    public CommonResult<PageResult<TransferPageRespVO>> getTransferPage(@Valid TransferPageReqVO pageReqVO) {
        return success(transferService.getTransferPage(pageReqVO));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出货品转移 Excel")
//    @PreAuthorize("@ss.hasPermission('mywms:transfer:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportTransferExcel(@Valid TransferPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<TransferDO> list = transferService.getTransferPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "货品转移.xls", "数据", TransferRespVO.class,
//                        BeanUtils.toBean(list, TransferRespVO.class));
//    }
//
//    // ==================== 子表（货品转移明细） ====================
//
//    @GetMapping("/transfer-detail/list-by-transfer-id")
//    @Operation(summary = "获得货品转移明细列表")
//    @Parameter(name = "transferId", description = "转移单ID（关联主表）")
//    @PreAuthorize("@ss.hasPermission('mywms:transfer:query')")
//    public CommonResult<List<TransferDetailDO>> getTransferDetailListByTransferId(@RequestParam("transferId") Long transferId) {
//        return success(transferService.getTransferDetailListByTransferId(transferId));
//    }

}