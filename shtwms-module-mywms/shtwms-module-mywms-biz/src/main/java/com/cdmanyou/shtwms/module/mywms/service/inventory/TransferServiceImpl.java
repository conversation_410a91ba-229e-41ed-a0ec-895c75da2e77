package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.TRANSFER_NOT_EXISTS;

/**
 * 货品转移主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransferServiceImpl implements TransferService {

    @Resource
    private TransferMapper transferMapper;
    @Resource
    private TransferDetailMapper transferDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransfer(TransferSaveReqVO createReqVO) {
        // 插入
        TransferDO transfer = BeanUtils.toBean(createReqVO, TransferDO.class);
        transferMapper.insert(transfer);

        // 插入子表
        createTransferDetailList(transfer.getId(), createReqVO.getTransferDetails());
        // 返回
        return transfer.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransfer(TransferSaveReqVO updateReqVO) {
        // 校验存在
        validateTransferExists(updateReqVO.getId());
        // 更新
        TransferDO updateObj = BeanUtils.toBean(updateReqVO, TransferDO.class);
        transferMapper.updateById(updateObj);

        // 更新子表
        updateTransferDetailList(updateReqVO.getId(), updateReqVO.getTransferDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTransfer(Long id) {
        // 校验存在
        validateTransferExists(id);
        // 删除
        transferMapper.deleteById(id);

        // 删除子表
        deleteTransferDetailByTransferId(id);
    }

    private void validateTransferExists(Long id) {
        if (transferMapper.selectById(id) == null) {
            throw exception(TRANSFER_NOT_EXISTS);
        }
    }

    @Override
    public TransferDO getTransfer(Long id) {
        return transferMapper.selectById(id);
    }

    @Override
    public PageResult<TransferDO> getTransferPage(TransferPageReqVO pageReqVO) {
        return transferMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（货品转移明细） ====================

    @Override
    public List<TransferDetailDO> getTransferDetailListByTransferId(Long transferId) {
        return transferDetailMapper.selectListByTransferId(transferId);
    }

    private void createTransferDetailList(Long transferId, List<TransferDetailDO> list) {
        list.forEach(o -> o.setTransferId(transferId));
        transferDetailMapper.insertBatch(list);
    }

    private void updateTransferDetailList(Long transferId, List<TransferDetailDO> list) {
        deleteTransferDetailByTransferId(transferId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createTransferDetailList(transferId, list);
    }

    private void deleteTransferDetailByTransferId(Long transferId) {
        transferDetailMapper.deleteByTransferId(transferId);
    }

}