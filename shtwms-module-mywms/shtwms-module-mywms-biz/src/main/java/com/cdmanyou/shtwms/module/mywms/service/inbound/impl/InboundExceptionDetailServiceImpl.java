package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;

import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception_detail.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.InboundExceptionDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundExceptionDetailService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 入库异常处理单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InboundExceptionDetailServiceImpl implements InboundExceptionDetailService {

    @Resource
    private InboundExceptionDetailMapper exceptionDetailMapper;

    @Override
    public Long createExceptionDetail(InboundExceptionDetailSaveReqVO createReqVO) {
        // 插入
        InboundExceptionDetailDO exceptionDetail = BeanUtils.toBean(createReqVO, InboundExceptionDetailDO.class);
        exceptionDetailMapper.insert(exceptionDetail);
        // 返回
        return exceptionDetail.getId();
    }

    @Override
    public void updateExceptionDetail(InboundExceptionDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateExceptionDetailExists(updateReqVO.getId());
        // 更新
        InboundExceptionDetailDO updateObj = BeanUtils.toBean(updateReqVO, InboundExceptionDetailDO.class);
        exceptionDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteExceptionDetail(Long id) {
        // 校验存在
        validateExceptionDetailExists(id);
        // 删除
        exceptionDetailMapper.deleteById(id);
    }

    private void validateExceptionDetailExists(Long id) {
        if (exceptionDetailMapper.selectById(id) == null) {
            throw exception(new ErrorCode(500,"对象不存在"));
        }
    }

    @Override
    public InboundExceptionDetailDO getExceptionDetail(Long id) {
        return exceptionDetailMapper.selectById(id);
    }

    @Override
    public PageResult<InboundExceptionDetailDO> getExceptionDetailPage(InboundExceptionDetailPageReqVO pageReqVO) {
        return exceptionDetailMapper.selectPage(pageReqVO);
    }

}