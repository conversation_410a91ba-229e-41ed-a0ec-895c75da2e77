package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import cn.hutool.core.collection.CollUtil;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.*;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ShelvingManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 上架管理单")
@RestController
@RequestMapping("/mywms/shelving-management")
@Validated
public class ShelvingManagementController { 

    @Resource
    private ShelvingManagementService managementService;

    @PostMapping("/save-submit")
    @Operation(summary = "保存/提交上架单")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management:update')")
    public CommonResult<Boolean> saveShelvingManagement(@Valid @RequestBody ShelvingManagementSubmitVO saveReqVO) {
        managementService.saveShelvingManagement(saveReqVO);
        return success(true);
    }

    @GetMapping("/getByCode")
    @Operation(summary = "通过上架单号获取上架单详情")
    @Parameter(name = "code", description = "上架单号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management:query')")
    public CommonResult<ShelvingManagementInfoVO> getShelvingManagementByCode(@RequestParam("code") String code) {
        return success(managementService.getShelvingManagementByCode(code));
    }

    @PostMapping("/page")
    @Operation(summary = "获得上架管理单分页")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management:query')")
    public CommonResult<PageResult<ShelvingManagementPageRespVO>> getManagementPage(@Valid @RequestBody ShelvingManagementPageReqVO pageReqVO) {
        return success(managementService.getManagementPage(pageReqVO));
    }

    @GetMapping("/voidById")
    @Operation(summary = "通过上架单ID作废上架单")
    @Parameter(name = "id", description = "上架单ID", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management:update')")
    public CommonResult<Boolean> voidShelvingManagementById(@RequestParam("id") Long id) {
        managementService.voidShelvingManagementById(id);
        return success(true);
    }

    @PostMapping("/batch-void")
    @Operation(summary = "批量作废上架单")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management:update')")
    public CommonResult<Boolean> batchVoidShelvingManagement(@Valid @RequestBody ShelvingManagementBatchVoidVO batchVoidVO) {
        if (CollUtil.isNotEmpty(batchVoidVO.getShelvingIds())) {
            batchVoidVO.getShelvingIds().forEach(id -> {
                managementService.voidShelvingManagementById(id);
            });
        }
        return success(true);
    }


}