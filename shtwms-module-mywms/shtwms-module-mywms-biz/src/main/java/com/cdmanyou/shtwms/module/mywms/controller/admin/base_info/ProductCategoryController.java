package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategoryPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategoryRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategorySaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductCategoryDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品类别管理")
@RestController
@RequestMapping("/mywms/product-category")
@Validated
public class ProductCategoryController {

    @Resource
    private ProductCategoryService productCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建商品类别管理")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:create')")
    public CommonResult<Long> createProductCategory(@Valid @RequestBody ProductCategorySaveReqVO createReqVO) {
        return success(productCategoryService.createProductCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品类别管理")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:update')")
    public CommonResult<Boolean> updateProductCategory(@Valid @RequestBody ProductCategorySaveReqVO updateReqVO) {
        productCategoryService.updateProductCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品类别管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:product-category:delete')")
    public CommonResult<Boolean> deleteProductCategory(@RequestParam("id") Long id) {
        productCategoryService.deleteProductCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品类别管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:query')")
    public CommonResult<ProductCategoryRespVO> getProductCategory(@RequestParam("id") Long id) {
        ProductCategoryDO productCategory = productCategoryService.getProductCategory(id);
        return success(BeanUtils.toBean(productCategory, ProductCategoryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品类别管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:query')")
    public CommonResult<PageResult<ProductCategoryRespVO>> getProductCategoryPage(@Valid ProductCategoryPageReqVO pageReqVO) {
        PageResult<ProductCategoryDO> pageResult = productCategoryService.getProductCategoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品类别管理列表（全量数据）")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getProductCategoryList() {
        List<ProductCategoryDO> list = productCategoryService.getProductCategoryList();
        return success(BeanUtils.toBean(list, ProductCategoryRespVO.class));
    }
    @PutMapping("/updateProductCategoryStatus")
    @Operation(summary = "更新商品类别状态", description = "启用/停用商品类别状态，status=1启用，status=0停用")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:update')")
    public CommonResult<Boolean> updateProductCategoryStatus(
            @RequestParam("id") Long id,
            @RequestParam("status") Integer status) {

        // 校验状态值合法性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("非法的状态值：" + status + "，只允许0(停用)或1(启用)");
        }

        if (status == 1) {
            productCategoryService.enableProductCategory(id);
        } else {
            productCategoryService.disableProductCategory(id);
        }
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品类别管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductCategoryExcel(@Valid ProductCategoryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductCategoryDO> list = productCategoryService.getProductCategoryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品类别管理.xls", "数据", ProductCategoryRespVO.class,
                        BeanUtils.toBean(list, ProductCategoryRespVO.class));
    }

    // ==================== 子表（商品管理） ====================

    @GetMapping("/product/list-by-category-id")
    @Operation(summary = "获得商品管理列表")
    @Parameter(name = "categoryId", description = "商品类别ID（关联商品类别表）")
    @PreAuthorize("@ss.hasPermission('mywms:product-category:query')")
    public CommonResult<List<ProductDO>> getProductListByCategoryId(@RequestParam("categoryId") Long categoryId) {
        return success(productCategoryService.getProductListByCategoryId(categoryId));
    }

}