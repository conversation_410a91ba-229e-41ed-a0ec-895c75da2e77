package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 货架管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShelfPageReqVO extends PageParam {

    @Schema(description = "货架编码")
    private String code;

    @Schema(description = "货架名称", example = "赵六")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", example = "26769")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", example = "29479")
    private Long areaId;

    @Schema(description = "货架类型", example = "2")
    private String type;

    @Schema(description = "货架状态(1启用/0停用)", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "层数")
    @NotEmpty(message = "货架层数不能为空")
    private Integer layers;

    @Schema(description = "列数")
    @NotEmpty(message = "货架列数不能为空")
    private Integer columns;


    @Schema(description = "样式")
    @NotEmpty(message = "货架样式不能为空")
    private Integer style;

    @Schema(description = "储位数")
    @NotEmpty(message = "储位数不能为空")
    private Integer location_count;

}