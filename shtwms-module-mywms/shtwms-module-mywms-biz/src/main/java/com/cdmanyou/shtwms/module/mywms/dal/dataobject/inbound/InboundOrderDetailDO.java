package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 入库单明细 DO
 *
 * <AUTHOR>
 */
@TableName("inbound_order_detail")
@KeySequence("inbound_order_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InboundOrderDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 入库单ID（关联inbound_order表）
     */
    private Long inboundOrderId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 商品单价
     */
    private BigDecimal productPrice;
    /**
     * 商品批次号
     */
    private String productBatchNumber;
    /**
     * 系统批次号
     */
    private String systemBatchNumber;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 到货时间
     */
    private LocalDateTime arrivalTime;
    /**
     * 待入库数量
     */
    private Integer pendingQuantity;
    /**
     * 待入库零散数量
     */
    private Integer pendingScatteredQuantity;
    /**
     * 验收数量
     */
    private Integer receivedQuantity;
    /**
     * 验收零散数量
     */
    private Integer receivedScatteredQuantity;
    /**
     * 上架数量
     */
    private Integer shelvedQuantity;
    /**
     * 上架零散数量
     */
    private Integer shelvedScatteredQuantity;
    /**
     * 拆零数量
     */
    private Integer looseQuantity;
    /**
     * 入库重量
     */
    private BigDecimal inboundWeight;
    /**
     * 上架重量
     */
    private BigDecimal shelvedWeight;
    /**
     * 入库体积
     */
    private BigDecimal inboundVolume;

}