package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 总拣单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PickingOrderDetailMapper extends BaseMapperX<PickingOrderDetailDO> {

    default List<PickingOrderDetailDO> selectListByPickingOrderId(Long pickingOrderId) {
        return selectList(PickingOrderDetailDO::getPickingOrderId, pickingOrderId);
    }

    default int deleteByPickingOrderId(Long pickingOrderId) {
        return delete(PickingOrderDetailDO::getPickingOrderId, pickingOrderId);
    }

    List<PickingOrderDetailPageRespVO> selectPickingOrderDetailListByPickingOrderId(@Param("pickingOrderId") Long pickingOrderId);
}