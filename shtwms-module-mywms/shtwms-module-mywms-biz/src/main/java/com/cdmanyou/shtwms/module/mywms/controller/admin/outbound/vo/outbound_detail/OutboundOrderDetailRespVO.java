package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 出库订单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OutboundOrderDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3801")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "出库单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "32711")
    @ExcelProperty("出库单ID（关联主表）")
    private Long outboundId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    @ExcelProperty("单位（非必填）")
    private String unit;

    @Schema(description = "待出库数量")
    @ExcelProperty("待出库数量")
    private Integer pendingQuantity;

    @Schema(description = "已出库数量")
    @ExcelProperty("已出库数量")
    private Integer receivedQuantity;

    @Schema(description = "差异数量")
    @ExcelProperty("差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    @ExcelProperty("批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    @ExcelProperty("生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "出库价格", example = "15796")
    @ExcelProperty("出库价格")
    private BigDecimal outboundPrice;

    @Schema(description = "出库金额(含税额)")
    @ExcelProperty("出库金额(含税额)")
    private BigDecimal outboundAmount;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "分拣完成时间")
    @ExcelProperty("分拣完成时间")
    private LocalDateTime sortingCompleteTime;

    @Schema(description = "是否越库(0否，1是)")
    @ExcelProperty("是否越库(0否，1是)")
    private Integer straddleWarehouse;

    @Schema(description = "出库体积")
    @ExcelProperty("出库体积")
    private Long volume;

    @Schema(description = "出库重量")
    @ExcelProperty("出库重量")
    private Long grossWeight;

}