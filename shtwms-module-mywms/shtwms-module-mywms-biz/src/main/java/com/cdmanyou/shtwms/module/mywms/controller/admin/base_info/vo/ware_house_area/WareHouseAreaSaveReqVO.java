package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 库区管理新增/修改 Request VO")
@Data
public class WareHouseAreaSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27524")
    private Long id;

    @Schema(description = "库区编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "库区编码不能为空")
    private String code;

    @Schema(description = "库区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1909")
    @NotNull(message = "所属仓库ID（关联仓库表）不能为空")
    private Long warehouseId;

    @Schema(description = "库区类型", example = "1")
    private String type;

    @Schema(description = "库区面积(㎡)")
    private BigDecimal area;

    @Schema(description = "库区状态(1启用/0停用)", example = "2")
    private Integer status;

    @Schema(description = "所属仓库名")
    private String warehouseName;
}