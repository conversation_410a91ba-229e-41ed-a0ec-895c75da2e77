package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 货主管理 DO
 *
 * <AUTHOR>
 */
@TableName("producer")
@KeySequence("producer_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProducerDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 货主编码
     */
    private String code;
    /**
     * 货主名称
     */
    private String name;
    /**
     * 货主简称
     */
    private String shortName;
    /**
     * 货主类型
     */
    private String type;
    /**
     * 货主英文名称
     */
    private String englishName;
    /**
     * 营业执照号
     */
    private String businessLicense;
    /**
     * 货主描述
     */
    private String description;
    /**
     * 联系人
     */
    private String contactPerson;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 联系邮箱
     */
    private String contactEmail;
    /**
     * 所在地区(省市区)
     */
    private String region;
    /**
     * 信用额度
     */
    private BigDecimal creditLimit;
    /**
     * 结算方式
     */
    private String settlementMethod;
    /**
     * 启用状态
     */
    private String status;
    /**
     * 合作状态
     */
    private String cooperationStatus;

}