package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品货位关联表-记录货位上的商品数量 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductWareHouseLocationService {

    /**
     * 创建商品货位关联表-记录货位上的商品数量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductWarehouseLocation(@Valid ProductWareHouseLocationSaveReqVO createReqVO);

    /**
     * 更新商品货位关联表-记录货位上的商品数量
     *
     * @param updateReqVO 更新信息
     */
    void updateProductWarehouseLocation(@Valid ProductWareHouseLocationSaveReqVO updateReqVO);

    /**
     * 删除商品货位关联表-记录货位上的商品数量
     *
     * @param id 编号
     */
    void deleteProductWarehouseLocation(Long id);

    /**
     * 获取库存管理详情
     *
     * @param id 编号
     * @return 库存管理详情
     */
    ProductWareHouseLocationRespVO getProductWarehouseLocationById(Long id);

    /**
     * 获得商品货位关联表-记录货位上的商品数量分页
     *
     * @param pageReqVO 分页查询
     * @return 商品货位关联表-记录货位上的商品数量分页
     */
    PageResult<ProductWareHouseLocationPageRespVO> getProductWarehouseLocationPage(ProductWareHouseLocationPageReqVO pageReqVO);

    void updateProductWarehouseLocationOccupation(Long id, int occupationNum);


    /**
     * 创建或修改库存信息 - 上架提交
     *
     * @param shelvingManagementDetails 上架提交的商品明细
     */
    void createOrUpdateByShelving(List<ShelvingManagementDetailSaveVO> shelvingManagementDetails);

    /**
     * 通过id冻结库存
     *
     * @param freezeVO 冻结实体
     */
    void freezeProductWareHouseLocationById(ProductWarehouseLocationFreezeVO freezeVO);
}