package com.cdmanyou.shtwms.module.mywms.dal.mysql.industryinfo;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.industryinfo.IndustryInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.cdmanyou.shtwms.module.mywms.controller.admin.industryinfo.vo.*;

/**
 * 行业信息管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface IndustryInfoMapper extends BaseMapperX<IndustryInfoDO> {

    default PageResult<IndustryInfoDO> selectPage(IndustryInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<IndustryInfoDO>()
                .likeIfPresent(IndustryInfoDO::getIndustryName, reqVO.getIndustryName())
                .eqIfPresent(IndustryInfoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(IndustryInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(IndustryInfoDO::getId));
    }

}