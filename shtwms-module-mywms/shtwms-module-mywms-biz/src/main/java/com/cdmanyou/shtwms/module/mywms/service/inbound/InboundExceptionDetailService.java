package com.cdmanyou.shtwms.module.mywms.service.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception_detail.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;

import javax.validation.Valid;

/**
 * 入库异常处理单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface InboundExceptionDetailService {

    /**
     * 创建入库异常处理单明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createExceptionDetail(@Valid InboundExceptionDetailSaveReqVO createReqVO);

    /**
     * 更新入库异常处理单明细
     *
     * @param updateReqVO 更新信息
     */
    void updateExceptionDetail(@Valid InboundExceptionDetailSaveReqVO updateReqVO);

    /**
     * 删除入库异常处理单明细
     *
     * @param id 编号
     */
    void deleteExceptionDetail(Long id);

    /**
     * 获得入库异常处理单明细
     *
     * @param id 编号
     * @return 入库异常处理单明细
     */
    InboundExceptionDetailDO getExceptionDetail(Long id);

    /**
     * 获得入库异常处理单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 入库异常处理单明细分页
     */
    PageResult<InboundExceptionDetailDO> getExceptionDetailPage(InboundExceptionDetailPageReqVO pageReqVO);

}