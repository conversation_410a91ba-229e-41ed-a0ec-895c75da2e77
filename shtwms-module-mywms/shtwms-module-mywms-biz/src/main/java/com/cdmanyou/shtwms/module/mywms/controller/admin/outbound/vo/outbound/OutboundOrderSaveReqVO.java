package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 出库订单主新增/修改 Request VO")
@Data
public class OutboundOrderSaveReqVO {

    @Schema(description = "自增ID", example = "23497")
    private Long id;

    @Schema(description = "出库单号")
    @NotEmpty(message = "出库单号不能为空")
    private String outboundCode;

    @Schema(description = "要求交货日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime requiredDeliveryDate;

    @Schema(description = "出库类型(0'销售出库',1'调拨出库',2'其他出库')", example = "2")
    private Integer outboundType;

    @Schema(description = "客户ID（关联客户表）", example = "7441")
    private Long customerId;

    @Schema(description = "出库总量")
    private Integer totalPending;

    @Schema(description = "实质出库总量")
    private Integer totalReceived;

    @Schema(description = "收货人")
    private String consignee;

    @Schema(description = "收货人电话")
    private String consigneePhone;

    @Schema(description = "出库单状态(0'草稿',1'已提交',2'已完成')", example = "2")
    private Integer orderStatus;

    @Schema(description = "拣货状态(0'待拣货',1'部份拣货',2'已拣货')", example = "2")
    private Integer pickingStatus;

    @Schema(description = "分拣状态(0'待分拣',1'已完成')", example = "1")
    private Integer sortingStatus;

    @Schema(description = "承运商（非必填）")
    private String carrier;

    @Schema(description = "运费（非必填）")
    private BigDecimal freightCost;

    @Schema(description = "配送方式（非必填）")
    private String deliveryMethod;

    @Schema(description = "车牌号（非必填）")
    private String licensePlate;

    @Schema(description = "物流单号（非必填）")
    private String logisticsNumber;

    @Schema(description = "出库员")
    private String outboundOperator;

    @Schema(description = "审核人")
    private String auditOperator;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime auditTime;

    @Schema(description = "出库方式(0普通出库,1紧急出库)，(出库方式为紧急出库直接生成总检单和分拣单)")
    private Integer outboundWay;

    @Schema(description = "备注（非必填）", example = "你说的对")
    private String remark;

    @Schema(description = "分拣完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime sortingCompleteTime;

    @Schema(description = "发运时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime sendTime;

    @Schema(description = "预约提货时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime bookingDeliveryTime;

    @Schema(description = "出库单总价", example = "28741")
    private Long price;

    @Schema(description = "货主id", example = "29049")
    private Long producerId;

    @Schema(description = "货主名称", example = "张三")
    private String producerName;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "发运状态", example = "1")
    private String sendStatus;

    @Schema(description = "出库订单明细列表")
    private List<OutboundOrderDetailDO> outboundOrderDetails;

}