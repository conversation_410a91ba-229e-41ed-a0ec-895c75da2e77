package com.cdmanyou.shtwms.module.mywms.service.industryinfo;

import java.util.*;
import javax.validation.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.industryinfo.vo.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.industryinfo.IndustryInfoDO;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;

/**
 * 行业信息管理 Service 接口
 *
 * <AUTHOR>
 */
public interface IndustryInfoService {

    /**
     * 创建行业信息管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIndustryInfo(@Valid IndustryInfoSaveReqVO createReqVO);

    /**
     * 更新行业信息管理
     *
     * @param updateReqVO 更新信息
     */
    void updateIndustryInfo(@Valid IndustryInfoSaveReqVO updateReqVO);

    /**
     * 删除行业信息管理
     *
     * @param id 编号
     */
    void deleteIndustryInfo(Long id);

    /**
     * 获得行业信息管理
     *
     * @param id 编号
     * @return 行业信息管理
     */
    IndustryInfoDO getIndustryInfo(Long id);

    /**
     * 获得行业信息管理分页
     *
     * @param pageReqVO 分页查询
     * @return 行业信息管理分页
     */
    PageResult<IndustryInfoDO> getIndustryInfoPage(IndustryInfoPageReqVO pageReqVO);

}