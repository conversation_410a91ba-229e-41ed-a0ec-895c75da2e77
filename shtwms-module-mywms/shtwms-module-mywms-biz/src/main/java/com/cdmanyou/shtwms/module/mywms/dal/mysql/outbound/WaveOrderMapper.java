package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 波次单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WaveOrderMapper extends BaseMapperX<WaveOrderDO> {

    default PageResult<WaveOrderDO> selectPage(WaveOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WaveOrderDO>()
                .eqIfPresent(WaveOrderDO::getWaveCode, reqVO.getWaveCode())
                .eqIfPresent(WaveOrderDO::getWaveType, reqVO.getWaveType())
                .eqIfPresent(WaveOrderDO::getWarehouseId, reqVO.getWarehouseId())
                .eqIfPresent(WaveOrderDO::getPicker, reqVO.getPicker())
                .eqIfPresent(WaveOrderDO::getPickingStatus, reqVO.getPickingStatus())
                .eqIfPresent(WaveOrderDO::getPriority, reqVO.getPriority())
                .eqIfPresent(WaveOrderDO::getWaveStatus, reqVO.getWaveStatus())
                .betweenIfPresent(WaveOrderDO::getPickingTime, reqVO.getPickingTime())
                .eqIfPresent(WaveOrderDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(WaveOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WaveOrderDO::getId));
    }

}