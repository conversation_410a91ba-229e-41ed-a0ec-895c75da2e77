<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.PickingOrderDetailMapper">


    <select id="selectPickingOrderDetailListByPickingOrderId"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageRespVO">
        SELECT tpod.id,
               wl.code            AS warehouse_location_code,
               tpod.product_name,
               tpod.picking_detail_status,
               tpod.specification,
               tpod.production_date,
               tpod.pending_quantity,
               tpod.picked_quantity,
               tpod.difference_quantity,
               tpod.picked_simple_quantity,
               tpod.pending_simple_quantity,
               tpod.difference_simple_quantity,
               w.name             AS warehouse_name,
               tpod.area_id,
               tpod.location_id,
               tpod.create_time,
               SUM(pwl.count)     AS product_count,
               SUM(pwl.available) AS product_available
        FROM shtwms.total_picking_order_detail tpod
                 LEFT JOIN shtwms.product_warehouse_location pwl
                           ON tpod.product_id = pwl.product_id
                               AND tpod.production_date = pwl.production_date
                               AND tpod.house_type = pwl.house_type
                 LEFT JOIN shtwms.warehouse_location wl ON tpod.location_id = wl.id
                 LEFT JOIN shtwms.warehouse w ON wl.warehouse_id = w.id
        WHERE tpod.deleted = 0
          and tpod.picking_order_id = #{pickingOrderId}
        GROUP BY tpod.id, -- 主键必须包含
                 tpod.product_name,
                 tpod.picking_detail_status,
                 tpod.specification,
                 tpod.production_date,
                 tpod.pending_quantity,
                 tpod.picked_quantity,
                 tpod.difference_quantity,
                 tpod.picked_simple_quantity,
                 tpod.pending_simple_quantity,
                 tpod.difference_simple_quantity,
                 w.name,
                 tpod.area_id,
                 tpod.location_id,
                 tpod.create_time,
                 wl.code -- 所有SELECT中的非聚合列都需要在GROUP BY中
    </select>
</mapper>