package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 出库订单主分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutboundOrderPageReqVO extends PageParam {

    @Schema(description = "出库单号")
    private String outboundCode;

    @Schema(description = "要求交货日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] requiredDeliveryDate;

    @Schema(description = "出库类型", example = "2")
    private String outboundType;

    @Schema(description = "客户ID（关联客户表）", example = "8164")
    private Long customerId;

    @Schema(description = "出库单状态", example = "1")
    private String orderStatus;

    @Schema(description = "出库员")
    private String outboundOperator;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}