package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;


import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WarehouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.WareHouseLocationService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationSaveReqVO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WarehouseLocationDO;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.WARE_HOUSE_LOCATION_NOT_EXISTS;

/**
 * 货位管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class WareHouseLocationServiceImpl implements WareHouseLocationService {

    @Resource
    private WarehouseLocationMapper wareHouseLocationMapper;

    // 统一状态校验方法
    private void validateStatus(Integer status) {
        if (status != null && status != 0 && status != 1) {
            throw new IllegalArgumentException("货位状态值必须是0或1");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createWareHouseLocation(@Valid WarehouseLocationSaveReqVO createReqVO) {
        // 状态校验
        validateStatus(createReqVO.getStatus());

        // 插入（使用正确的DO类名）
        WarehouseLocationDO wareHouseLocation = BeanUtils.toBean(createReqVO, WarehouseLocationDO.class);
        wareHouseLocationMapper.insert(wareHouseLocation);
        return wareHouseLocation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWareHouseLocation(@Valid WarehouseLocationSaveReqVO updateReqVO) {
        // 状态校验
        validateStatus(updateReqVO.getStatus());

        // 校验存在并更新
        validateWareHouseLocationExists(updateReqVO.getId());
        WarehouseLocationDO updateObj = BeanUtils.toBean(updateReqVO, WarehouseLocationDO.class);
        wareHouseLocationMapper.updateById(updateObj);
    }

    @Override
    public void deleteWareHouseLocation(Long id) {
        // 校验存在
        validateWareHouseLocationExists(id);
        // 删除
        wareHouseLocationMapper.deleteById(id);
    }

    private void validateWareHouseLocationExists(Long id) {
        if (wareHouseLocationMapper.selectById(id) == null) {
            throw exception(WARE_HOUSE_LOCATION_NOT_EXISTS);
        }
    }

    @Override
    public WarehouseLocationDO getWareHouseLocation(Long id) {
        return wareHouseLocationMapper.selectById(id);
    }

    @Override
    public PageResult<WarehouseLocationDO> getWareHouseLocationPage(WarehouseLocationPageReqVO pageReqVO) {
        return wareHouseLocationMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWareHouseLocationStatus(Long id, Integer status) {
        // 校验货位是否存在
        validateWareHouseLocationExists(id);

        // 校验状态值合法性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("状态值只能是0或1");
        }

        // 更新状态
        WarehouseLocationDO updateObj = new WarehouseLocationDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        wareHouseLocationMapper.updateById(updateObj);
    }

}