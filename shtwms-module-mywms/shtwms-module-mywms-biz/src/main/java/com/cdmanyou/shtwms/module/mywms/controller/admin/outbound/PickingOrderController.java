package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 总拣单主")
@RestController
@RequestMapping("/mywms/picking-order")
@Validated
public class PickingOrderController {

    @Resource
    private PickingOrderService pickingOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建总拣单主")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order:create')")
    public CommonResult<Long> createPickingOrder( @RequestBody List<Long> waveOrderIds) {
        return success(pickingOrderService.createPickingOrder(waveOrderIds,0,null,null));
    }

    @PutMapping("/update")
    @Operation(summary = "更新总拣单主")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order:update')")
    public CommonResult<Boolean> updatePickingOrder(@Valid @RequestBody PickingOrderSaveReqVO updateReqVO) {
        pickingOrderService.updatePickingOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除总拣单主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:picking-order:delete')")
    public CommonResult<Boolean> deletePickingOrder(@RequestParam("id") Long id) {
        pickingOrderService.deletePickingOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得总拣单主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order:query')")
    public CommonResult<PickingOrderRespVO> getPickingOrder(@RequestParam("id") Long id) {
        PickingOrderDO pickingOrder = pickingOrderService.getPickingOrder(id);
        return success(BeanUtils.toBean(pickingOrder, PickingOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得总拣单主分页")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order:query')")
    public CommonResult<PageResult<PickingOrderPageRespVO>> getPickingOrderPage(@Valid PickingOrderPageReqVO pageReqVO) {
        PageResult<PickingOrderPageRespVO> pageResult = pickingOrderService.getPickingOrderPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出总拣单主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPickingOrderExcel(@Valid PickingOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PickingOrderPageRespVO> list = pickingOrderService.getPickingOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "总拣单主.xls", "数据", PickingOrderPageRespVO.class,
                        list);
    }

    // ==================== 子表（总拣单明细） ====================

    @GetMapping("/picking-order-detail/list-by-picking-order-id")
    @Operation(summary = "获得总拣单明细列表")
    @Parameter(name = "pickingOrderId", description = "拣货单ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('mywms:picking-order:query')")
    public CommonResult<List<PickingOrderDetailPageRespVO>> getPickingOrderDetailListByPickingOrderId(@RequestParam("pickingOrderId") Long pickingOrderId) {
        return success(pickingOrderService.getPickingOrderDetailListByPickingOrderId(pickingOrderId));
    }

}