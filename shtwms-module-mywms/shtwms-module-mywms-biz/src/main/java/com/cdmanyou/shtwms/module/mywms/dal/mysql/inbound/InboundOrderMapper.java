package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import java.time.LocalDateTime;
import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.InboundOrderInfoVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.InboundOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.InboundOrderPageRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 入库单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InboundOrderMapper extends BaseMapperX<InboundOrderDO> {

    InboundOrderInfoVO getInboundOrderByCode(@Param("code") String code);

    Page<InboundOrderPageRespVO> selectPage(Page<InboundOrderPageRespVO> page, @Param("reqVO") InboundOrderPageReqVO reqVO);

    Long selectPageCount(@Param("reqVO") InboundOrderPageReqVO reqVO);

    Long getMaxSequence(@Param("dateStr") String dateStr);

    @Update("UPDATE inbound_order SET receipt_complete_time = #{receiptTime} WHERE id = #{inboundOrderId} AND deleted = 0")
    void updateReceiptCompleteTime(@Param("inboundOrderId") Long inboundOrderId, @Param("receiptTime") LocalDateTime receiptTime);
}