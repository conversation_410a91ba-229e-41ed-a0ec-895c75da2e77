package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 库区管理 DO
 *
 * <AUTHOR>
 */
@TableName("warehouse_area")
@KeySequence("warehouse_area_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WareHouseAreaDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 库区编码
     */
    private String code;
    /**
     * 库区名称
     */
    private String name;
    /**
     * 所属仓库ID（关联仓库表）
     */
    private Long warehouseId;
    /**
     * 库区类型
     */
    private String type;
    /**
     * 库区面积(㎡)
     */
    private BigDecimal area;
    /**
     * 库区状态(1启用/0停用)
     */
    private Integer status;

}