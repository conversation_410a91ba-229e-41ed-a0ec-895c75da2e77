package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cdmanyou.shtwms.framework.common.exception.ServiceException;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.PRODUCT_WAREHOUSE_LOCATION_NOT_EXISTS;


/**
 * 商品货位关联表-记录货位上的商品数量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductWareHouseLocationServiceImpl implements ProductWareHouseLocationService {

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Override
    public Long createProductWarehouseLocation(ProductWareHouseLocationSaveReqVO createReqVO) {
        // 插入
        ProductWareHouseLocationDO productWareHouseLocation = BeanUtils.toBean(createReqVO, ProductWareHouseLocationDO.class);
        productWareHouseLocationMapper.insert(productWareHouseLocation);
        // 返回
        return productWareHouseLocation.getId();
    }

    @Override
    public void updateProductWarehouseLocation(ProductWareHouseLocationSaveReqVO updateReqVO) {
        // 校验存在
        validateWarehouseLocationExists(updateReqVO.getId());
        // 更新
        ProductWareHouseLocationDO updateObj = BeanUtils.toBean(updateReqVO, ProductWareHouseLocationDO.class);
        productWareHouseLocationMapper.updateById(updateObj);
    }

    @Override
    public void deleteProductWarehouseLocation(Long id) {
        // 校验存在
        validateWarehouseLocationExists(id);
        // 删除
        productWareHouseLocationMapper.deleteById(id);
    }

    private void validateWarehouseLocationExists(Long id) {
        if (productWareHouseLocationMapper.selectById(id) == null) {
            throw exception(PRODUCT_WAREHOUSE_LOCATION_NOT_EXISTS);
        }
    }

    @Override
    public ProductWareHouseLocationDO getProductWarehouseLocation(Long id) {
        return productWareHouseLocationMapper.selectById(id);
    }

    @Override
    public PageResult<ProductWareHouseLocationDO> getProductWarehouseLocationPage(ProductWareHouseLocationPageReqVO pageReqVO) {
        return productWareHouseLocationMapper.selectPage(pageReqVO);
    }

    @Override
    public void updateProductWarehouseLocationOccupation(Long id, int occupationNum) {
        LambdaUpdateWrapper<ProductWareHouseLocationDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProductWareHouseLocationDO::getId, id);
        updateWrapper.set(ProductWareHouseLocationDO::getOccupation, occupationNum);
        productWareHouseLocationMapper.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateByShelving(List<ShelvingManagementDetailSaveVO> shelvingManagementDetails) {
        for (ShelvingManagementDetailSaveVO detail : shelvingManagementDetails) {
            ProductWareHouseLocationDO productWareHouseLocationDO = productWareHouseLocationMapper.getByShelvingDetail(detail.getProducerId(), detail.getProductId(),
                    detail.getProductionDate(), detail.getWarehouseLocationId());
            if (Objects.isNull(productWareHouseLocationDO)) {
                ProductWareHouseLocationDO createObj = new ProductWareHouseLocationDO();
                createObj.setProducerId(detail.getProducerId())
                        .setProductId(detail.getProductId())
                        .setProductionDate(detail.getProductionDate())
                        .setWarehouseLocationId(detail.getWarehouseLocationId())
                        .setCount(detail.getShelvedQuantity())
                        .setAvailable(detail.getShelvedQuantity())
                        .setOccupation(0)
                        .setScatteredCount(detail.getShelvedScatteredQuantity())
                        .setScatteredAvailable(detail.getShelvedScatteredQuantity())
                        .setScatteredOccupation(0);
                productWareHouseLocationMapper.insert(createObj);
            }
            ProductWareHouseLocationDO updateObj = new ProductWareHouseLocationDO();
            updateObj.setId(productWareHouseLocationDO.getId())
                    .setReversion(productWareHouseLocationDO.getReversion())
                    .setCount(productWareHouseLocationDO.getCount() + detail.getShelvedQuantity())
                    .setAvailable(productWareHouseLocationDO.getAvailable() + detail.getShelvedQuantity())
                    .setScatteredCount(productWareHouseLocationDO.getScatteredCount() + detail.getShelvedScatteredQuantity())
                    .setAvailable(productWareHouseLocationDO.getScatteredAvailable() + detail.getShelvedScatteredQuantity());
            int updateRows = productWareHouseLocationMapper.updateById(updateObj);
            if (updateRows == 0) {
                throw new ServiceException(1001_500, "网络繁忙，请稍候重试");
            }
        }
    }

}