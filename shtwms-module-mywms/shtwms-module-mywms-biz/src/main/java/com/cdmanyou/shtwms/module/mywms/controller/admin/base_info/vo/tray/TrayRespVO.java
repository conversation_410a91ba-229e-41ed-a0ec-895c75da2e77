package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 托盘管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TrayRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31372")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "托盘编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("托盘编码")
    private String code;

    @Schema(description = "所属仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "8756")
    @ExcelProperty("所属仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "27569")
    @ExcelProperty("所属库区ID（关联库区表）")
    private Long areaId;

    @Schema(description = "托盘名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("托盘名称")
    private String name;

    @Schema(description = "托盘类型", example = "2")
    @ExcelProperty("托盘类型")
    private String type;

    @Schema(description = "托盘容量(吨)")
    @ExcelProperty("托盘容量(吨)")
    private BigDecimal capacity;

    @Schema(description = "条码号")
    @ExcelProperty("条码号")
    private String barcode;

    @Schema(description = "状态(1启用/0停用)", example = "1")
    @ExcelProperty("状态(1启用/0停用)")
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}