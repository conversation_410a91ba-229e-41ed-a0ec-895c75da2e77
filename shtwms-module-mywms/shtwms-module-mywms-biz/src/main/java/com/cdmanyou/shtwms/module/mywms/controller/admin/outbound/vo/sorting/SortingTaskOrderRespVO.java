package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 分拣任务单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SortingTaskOrderRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11422")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "分拣单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分拣单号")
    private String sortingOrderCode;

    @Schema(description = "出库订单号（关联出库订单主表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出库订单号（关联出库订单主表）")
    private String outboundOrderCode;

    @Schema(description = "客户名称", example = "李四")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "商品总数量")
    @ExcelProperty("商品总数量")
    private Integer totalProductQuantity;

    @Schema(description = "仓库ID（关联仓库表）", example = "21918")
    @ExcelProperty("仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "分拣员")
    @ExcelProperty("分拣员")
    private String sorter;

    @Schema(description = "分拣容器（非必填）", example = "1")
    @ExcelProperty("分拣容器（非必填）")
    private String containerType;

    @Schema(description = "分拣容器号（非必填）")
    @ExcelProperty("分拣容器号（非必填）")
    private String containerCode;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "分拣时间")
    @ExcelProperty("分拣时间")
    private LocalDateTime sortingTime;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}