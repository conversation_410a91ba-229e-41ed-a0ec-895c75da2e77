package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 出库订单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OutboundOrderRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23497")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "出库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出库单号")
    private String outboundCode;

    @Schema(description = "要求交货日期")
    @ExcelProperty("要求交货日期")
    private LocalDateTime requiredDeliveryDate;

    @Schema(description = "出库类型(0'销售出库',1'调拨出库',2'其他出库')", example = "2")
    @ExcelProperty("出库类型(0'销售出库',1'调拨出库',2'其他出库')")
    private Integer outboundType;

    @Schema(description = "客户ID（关联客户表）", example = "7441")
    @ExcelProperty("客户ID（关联客户表）")
    private Long customerId;

    @Schema(description = "出库总量")
    @ExcelProperty("出库总量")
    private Integer totalPending;

    @Schema(description = "实质出库总量")
    @ExcelProperty("实质出库总量")
    private Integer totalReceived;

    @Schema(description = "收货人")
    @ExcelProperty("收货人")
    private String consignee;

    @Schema(description = "收货人电话")
    @ExcelProperty("收货人电话")
    private String consigneePhone;

    @Schema(description = "出库单状态(0'草稿',1'已提交',2'已完成')", example = "2")
    @ExcelProperty("出库单状态(0'草稿',1'已提交',2'已完成')")
    private Integer orderStatus;

    @Schema(description = "拣货状态(0'待拣货',1'部份拣货',2'已拣货')", example = "2")
    @ExcelProperty("拣货状态(0'待拣货',1'部份拣货',2'已拣货')")
    private Integer pickingStatus;

    @Schema(description = "分拣状态(0'待分拣',1'已完成')", example = "1")
    @ExcelProperty("分拣状态(0'待分拣',1'已完成')")
    private Integer sortingStatus;

    @Schema(description = "承运商（非必填）")
    @ExcelProperty("承运商（非必填）")
    private String carrier;

    @Schema(description = "运费（非必填）")
    @ExcelProperty("运费（非必填）")
    private BigDecimal freightCost;

    @Schema(description = "配送方式（非必填）")
    @ExcelProperty("配送方式（非必填）")
    private String deliveryMethod;

    @Schema(description = "车牌号（非必填）")
    @ExcelProperty("车牌号（非必填）")
    private String licensePlate;

    @Schema(description = "物流单号（非必填）")
    @ExcelProperty("物流单号（非必填）")
    private String logisticsNumber;

    @Schema(description = "出库员")
    @ExcelProperty("出库员")
    private String outboundOperator;

    @Schema(description = "审核人")
    @ExcelProperty("审核人")
    private String auditOperator;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "出库方式(0普通出库,1紧急出库)，(出库方式为紧急出库直接生成总检单和分拣单)")
    @ExcelProperty("出库方式(0普通出库,1紧急出库)，(出库方式为紧急出库直接生成总检单和分拣单)")
    private Integer outboundWay;

    @Schema(description = "备注（非必填）", example = "你说的对")
    @ExcelProperty("备注（非必填）")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "分拣完成时间")
    @ExcelProperty("分拣完成时间")
    private LocalDateTime sortingCompleteTime;

    @Schema(description = "发运时间")
    @ExcelProperty("发运时间")
    private LocalDateTime sendTime;

    @Schema(description = "预约提货时间")
    @ExcelProperty("预约提货时间")
    private LocalDateTime bookingDeliveryTime;

    @Schema(description = "出库单总价", example = "28741")
    @ExcelProperty("出库单总价")
    private Long price;

    @Schema(description = "货主id", example = "29049")
    @ExcelProperty("货主id")
    private Long producerId;

    @Schema(description = "货主名称", example = "张三")
    @ExcelProperty("货主名称")
    private String producerName;

    @Schema(description = "货主单号")
    @ExcelProperty("货主单号")
    private String producerOrderCode;

    @Schema(description = "发运状态", example = "1")
    @ExcelProperty("发运状态")
    private String sendStatus;

    @Schema(description = "出库单详情")
    private List<OutboundOrderDetailDO> outboundOrderDetailList;
}