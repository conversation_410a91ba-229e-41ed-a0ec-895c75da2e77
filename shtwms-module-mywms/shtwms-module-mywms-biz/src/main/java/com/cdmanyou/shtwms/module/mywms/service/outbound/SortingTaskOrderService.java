package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 分拣任务单主 Service 接口
 *
 * <AUTHOR>
 */
public interface SortingTaskOrderService {

    /**
     * 创建分拣任务单主
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSortingTaskOrder(@Valid SortingTaskOrderSaveReqVO createReqVO);

    /**
     * 更新分拣任务单主
     *
     * @param updateReqVO 更新信息
     */
    void updateSortingTaskOrder(@Valid SortingTaskOrderSaveReqVO updateReqVO);

    /**
     * 删除分拣任务单主
     *
     * @param id 编号
     */
    void deleteSortingTaskOrder(Long id);

    /**
     * 获得分拣任务单主
     *
     * @param id 编号
     * @return 分拣任务单主
     */
    SortingTaskOrderDO getSortingTaskOrder(Long id);

    /**
     * 获得分拣任务单主分页
     *
     * @param pageReqVO 分页查询
     * @return 分拣任务单主分页
     */
    PageResult<SortingTaskOrderDO> getSortingTaskOrderPage(SortingTaskOrderPageReqVO pageReqVO);

    // ==================== 子表（分拣任务单明细） ====================

    /**
     * 获得分拣任务单明细列表
     *
     * @param sortingOrderId 分拣任务单ID（关联主表）
     * @return 分拣任务单明细列表
     */
    List<SortingTaskOrderDetailDO> getSortingTaskOrderDetailListBySortingOrderId(Long sortingOrderId);

}