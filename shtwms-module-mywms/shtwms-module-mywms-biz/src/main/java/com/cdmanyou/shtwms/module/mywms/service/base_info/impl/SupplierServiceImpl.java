package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.supplier.SupplierPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.supplier.SupplierSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.SupplierDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.SupplierMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.SupplierService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 供应商管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SupplierServiceImpl implements SupplierService {

    @Resource
    private SupplierMapper supplierMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSupplier(SupplierSaveReqVO createReqVO) {
        // 生成供应商编码: GYS + 毫秒级时间戳
        String generatedCode = "GYS" + System.currentTimeMillis();
        createReqVO.setCode(generatedCode); // 覆盖请求参数中的编码

        validateSupplierUnique(createReqVO);

        // 插入
        SupplierDO supplier = BeanUtils.toBean(createReqVO, SupplierDO.class);
        supplierMapper.insert(supplier);
        // 返回
        return supplier.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSupplier(SupplierSaveReqVO updateReqVO) {
        // 校验存在
        validateSupplierExists(updateReqVO.getId());
        // 新增唯一性校验（排除自身）
        validateSupplierUniqueForUpdate(updateReqVO, updateReqVO.getId());
        // 更新
        SupplierDO updateObj = BeanUtils.toBean(updateReqVO, SupplierDO.class);
        supplierMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSupplier(Long id) {
        // 校验存在
        validateSupplierExists(id);
        // 删除
        supplierMapper.deleteById(id);
    }

    private SupplierDO validateSupplierExists(Long id) {
        SupplierDO supplier = supplierMapper.selectById(id);
        if (supplier == null) {
            throw exception(SUPPLIER_NOT_EXISTS);
        }
        return supplier;
    }

    @Override
    public SupplierDO getSupplier(Long id) {
        return supplierMapper.selectById(id);
    }

    @Override
    public PageResult<SupplierDO> getSupplierPage(SupplierPageReqVO pageReqVO) {
        return supplierMapper.selectPage(pageReqVO);
    }

    private void validateSupplierUnique(SupplierSaveReqVO reqVO) {
        // 校验供应商编码唯一性
        if (supplierMapper.existsByCode(reqVO.getCode())) {
            throw exception(SUPPLIER_CODE_DUPLICATE); // 需定义错误码
        }
        // 校验供应商名称唯一性
        if (supplierMapper.existsByName(reqVO.getName())) {
            throw exception(SUPPLIER_NAME_DUPLICATE); // 需定义错误码
        }
    }

    private void validateSupplierUniqueForUpdate(SupplierSaveReqVO reqVO, Long excludeId) {
        // 校验供应商编码唯一性（排除自身）
        if (supplierMapper.existsByCodeAndIdNot(reqVO.getCode(), excludeId)) {
            throw exception(SUPPLIER_CODE_DUPLICATE);
        }
        // 校验供应商名称唯一性（排除自身）
        if (supplierMapper.existsByNameAndIdNot(reqVO.getName(), excludeId)) {
            throw exception(SUPPLIER_NAME_DUPLICATE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableSupplier(Long id) {
        SupplierDO supplier = validateSupplierExists(id);
        // 幂等性检查：当前已启用则无需操作
        if (supplier.getStatus() == 1) {
            throw exception(SUPPLIER_ALREADY_ENABLED); // 需定义新错误码
        }

        // 更新状态为启用(1)
        SupplierDO updateObj = new SupplierDO();
        updateObj.setId(id);
        updateObj.setStatus(1);
        supplierMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableSupplier(Long id) {
        SupplierDO supplier = validateSupplierExists(id);
        // 幂等性检查：当前已停用则无需操作
        if (supplier.getStatus() == 0) {
            throw exception(SUPPLIER_ALREADY_DISABLED); // 需定义新错误码
        }

        // 更新状态为停用(0)
        SupplierDO updateObj = new SupplierDO();
        updateObj.setId(id);
        updateObj.setStatus(0);
        supplierMapper.updateById(updateObj);
    }



}