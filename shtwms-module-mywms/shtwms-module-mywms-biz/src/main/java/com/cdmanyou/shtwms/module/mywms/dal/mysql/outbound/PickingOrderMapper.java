package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 总拣单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PickingOrderMapper extends BaseMapperX<PickingOrderDO> {

    default PageResult<PickingOrderDO> selectPage(PickingOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PickingOrderDO>()
                .eqIfPresent(PickingOrderDO::getPickingOrderCode, reqVO.getPickingOrderCode())
                .eqIfPresent(PickingOrderDO::getWaveCode, reqVO.getWaveCode())
                .eqIfPresent(PickingOrderDO::getWarehouseId, reqVO.getWarehouseId())
                .eqIfPresent(PickingOrderDO::getPicker, reqVO.getPicker())
                .eqIfPresent(PickingOrderDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(PickingOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PickingOrderDO::getId));
    }

}