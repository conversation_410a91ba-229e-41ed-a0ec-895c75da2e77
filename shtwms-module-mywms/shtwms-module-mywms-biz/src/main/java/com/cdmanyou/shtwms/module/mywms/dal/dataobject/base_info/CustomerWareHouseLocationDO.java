package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * （入库）货主商品货位关联表-记录货位上的商品数量 DO
 *
 * <AUTHOR>
 */
@TableName("customer_warehouse_location")
@KeySequence("customer_warehouse_location_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerWareHouseLocationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 入库单ID
     */
    private Long inboundOrderId;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 货位ID
     */
    private Long warehouseLocationId;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 商品总数
     */
    private Integer count;
    /**
     * 占用数量
     */
    private Integer occupation;
    /**
     * 可用数量
     */
    private Integer available;

}
