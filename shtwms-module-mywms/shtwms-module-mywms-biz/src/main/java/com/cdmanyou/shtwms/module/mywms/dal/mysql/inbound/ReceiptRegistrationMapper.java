package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.ReceiptRegistrationInfoVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.ReceiptRegistrationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.ReceiptRegistrationPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.ReceiptRegistrationSubmitVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ReceiptRegistrationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 收货登记单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReceiptRegistrationMapper extends BaseMapperX<ReceiptRegistrationDO> {

    ReceiptRegistrationInfoVO getReceiptRegistrationByCode(@Param("code") String code);

    Page<ReceiptRegistrationPageRespVO> selectPage(Page<ReceiptRegistrationPageRespVO> page, @Param("reqVO") ReceiptRegistrationPageReqVO reqVO);

    Long selectPageCount(@Param("reqVO") ReceiptRegistrationPageReqVO pageReqVO);

    Long getMaxSequence(@Param("dateStr") String dateStr);

    List<ReceiptRegistrationSubmitVO> getReceiptRegistrationSubmitVOListByIds(@Param("receiptIds") List<Long> receiptIds);

    default List<ReceiptRegistrationDO> selectListByIds(List<Long> receiptIds) {
        return selectList(new LambdaQueryWrapperX<ReceiptRegistrationDO>()
                .inIfPresent(ReceiptRegistrationDO::getId, receiptIds)
                .eqIfPresent(ReceiptRegistrationDO::getDeleted, false));
    }
}
