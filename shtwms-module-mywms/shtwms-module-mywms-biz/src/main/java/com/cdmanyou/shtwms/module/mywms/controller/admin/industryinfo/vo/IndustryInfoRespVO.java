package com.cdmanyou.shtwms.module.mywms.controller.admin.industryinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 行业信息管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class IndustryInfoRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12698")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "行业编码（唯一键）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("行业编码（唯一键）")
    private String industryCode;

    @Schema(description = "行业名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("行业名称")
    private String industryName;

    @Schema(description = "行业描述（非必填）", example = "你猜")
    @ExcelProperty("行业描述（非必填）")
    private String description;

    @Schema(description = "行业状态", example = "1")
    @ExcelProperty("行业状态")
    private String status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}