package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库区管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WareHouseAreaRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27524")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "库区编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库区编码")
    private String code;

    @Schema(description = "库区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("库区名称")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1909")
    @ExcelProperty("所属仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "库区类型", example = "1")
    @ExcelProperty("库区类型")
    private String type;

    @Schema(description = "库区面积(㎡)")
    @ExcelProperty("库区面积(㎡)")
    private BigDecimal area;

    @Schema(description = "库区状态(1启用/0停用)", example = "2")
    @ExcelProperty("库区状态(1启用/0停用)")
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "所属仓库名")
    private String warehouseName;
}