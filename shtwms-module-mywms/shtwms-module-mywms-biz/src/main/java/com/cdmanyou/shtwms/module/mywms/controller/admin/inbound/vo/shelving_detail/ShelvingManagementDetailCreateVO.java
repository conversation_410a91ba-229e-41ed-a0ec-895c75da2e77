package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "上架单明细创建实体")
@Data
public class ShelvingManagementDetailCreateVO {

    @Schema(description = "货主ID")
    private Long producerId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "系统批次号")
    private String systemBatchNumber;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "待入库零散数量")
    private Integer pendingScatteredQuantity;

    @Schema(description = "收货数量（待上架数量）")
    private Integer receivedQuantity;

    @Schema(description = "收货零散数量（待上架零散数量）")
    private Integer receivedScatteredQuantity;

    @Schema(description = "实收重量")
    private BigDecimal receivedWeight;
}
