package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;

import com.cdmanyou.shtwms.framework.common.util.date.DateUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailInputVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "货品转移创建实体")
@Data
public class TransferInputVO {

    @Schema(description = "转移单ID")
    private Long id;

    @Schema(description = "转移日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate transferDate;

    @Schema(description = "转移总数")
    private Integer totalQuantity;

    @Schema(description = "转移零散总数")
    private Integer totalScatteredQuantity;

    @Schema(description = "转移明细")
    private List<TransferDetailInputVO> transferDetails;

}
