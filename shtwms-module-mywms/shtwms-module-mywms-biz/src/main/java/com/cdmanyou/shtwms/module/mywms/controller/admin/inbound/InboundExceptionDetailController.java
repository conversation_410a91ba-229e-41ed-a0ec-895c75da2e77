package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception_detail.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundExceptionDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 入库异常处理单明细")
@RestController
@RequestMapping("/inbound/exception-detail")
@Validated
public class InboundExceptionDetailController {

    @Resource
    private InboundExceptionDetailService exceptionDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建入库异常处理单明细")
    @PreAuthorize("@ss.hasPermission('inbound:exception-detail:create')")
    public CommonResult<Long> createExceptionDetail(@Valid @RequestBody InboundExceptionDetailSaveReqVO createReqVO) {
        return success(exceptionDetailService.createExceptionDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新入库异常处理单明细")
    @PreAuthorize("@ss.hasPermission('inbound:exception-detail:update')")
    public CommonResult<Boolean> updateExceptionDetail(@Valid @RequestBody InboundExceptionDetailSaveReqVO updateReqVO) {
        exceptionDetailService.updateExceptionDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除入库异常处理单明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('inbound:exception-detail:delete')")
    public CommonResult<Boolean> deleteExceptionDetail(@RequestParam("id") Long id) {
        exceptionDetailService.deleteExceptionDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得入库异常处理单明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('inbound:exception-detail:query')")
    public CommonResult<InboundExceptionDetailRespVO> getExceptionDetail(@RequestParam("id") Long id) {
        InboundExceptionDetailDO exceptionDetail = exceptionDetailService.getExceptionDetail(id);
        return success(BeanUtils.toBean(exceptionDetail, InboundExceptionDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得入库异常处理单明细分页")
    @PreAuthorize("@ss.hasPermission('inbound:exception-detail:query')")
    public CommonResult<PageResult<InboundExceptionDetailRespVO>> getExceptionDetailPage(@Valid InboundExceptionDetailPageReqVO pageReqVO) {
        PageResult<InboundExceptionDetailDO> pageResult = exceptionDetailService.getExceptionDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InboundExceptionDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出入库异常处理单明细 Excel")
    @PreAuthorize("@ss.hasPermission('inbound:exception-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportExceptionDetailExcel(@Valid InboundExceptionDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InboundExceptionDetailDO> list = exceptionDetailService.getExceptionDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "入库异常处理单明细.xls", "数据", InboundExceptionDetailRespVO.class,
                        BeanUtils.toBean(list, InboundExceptionDetailRespVO.class));
    }

}