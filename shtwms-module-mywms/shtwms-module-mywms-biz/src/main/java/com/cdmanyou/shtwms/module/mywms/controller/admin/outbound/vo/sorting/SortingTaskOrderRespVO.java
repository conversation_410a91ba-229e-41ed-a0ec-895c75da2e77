package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 分拣任务单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SortingTaskOrderRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11422")
    private Long id;

    @Schema(description = "日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("日期")
    private LocalDateTime bookingDeliveryTime;

    @Schema(description = "分拣单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分拣单号")
    private String sortingOrderCode;

    @Schema(description = "总检单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总检单号")
    private String pickingOrderCode;


    @Schema(description = "状态（0'待分拣',1'已完成'）", example = "2")
    @ExcelProperty("状态（0'待分拣',1'已完成'）")
    private String status;

    @Schema(description = "出库方式", example = "2")
    @ExcelProperty("出库方式")
    private Integer outboundType;


    @Schema(description = "分拣数量")
    @ExcelProperty("分拣数量")
    private Integer totalProductQuantity;

    @Schema(description = "拣货重量")
    @ExcelProperty("拣货重量")
    private Integer totalProductWeight;


    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "客户名称", example = "李四")
    @ExcelProperty("客户名称")
    private String customerName;


    @Schema(description = "仓库ID（关联仓库表）", example = "21918")
    @ExcelProperty("仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "分拣员")
    @ExcelProperty("分拣员")
    private String sorter;

    @Schema(description = "分拣时间")
    @ExcelProperty("分拣时间")
    private LocalDateTime sortingTime;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "货主名称")
    @ExcelProperty("货主名称")
    private String producerName;


}