package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area.WareHouseAreaPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area.WareHouseAreaSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 库区管理 Service 接口
 *
 * <AUTHOR>
 */
public interface WareHouseAreaService {

    /**
     * 创建库区管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWareHouseArea(@Valid WareHouseAreaSaveReqVO createReqVO);

    /**
     * 更新库区管理
     *
     * @param updateReqVO 更新信息
     */
    void updateWareHouseArea(@Valid WareHouseAreaSaveReqVO updateReqVO);

    /**
     * 删除库区管理
     *
     * @param id 编号
     */
    void deleteWareHouseArea(Long id);

    /**
     * 获得库区管理
     *
     * @param id 编号
     * @return 库区管理
     */
    WareHouseAreaDO getWareHouseArea(Long id);

    /**
     * 获得库区管理分页
     *
     * @param pageReqVO 分页查询
     * @return 库区管理分页
     */
    PageResult<WareHouseAreaDO> getWareHouseAreaPage(WareHouseAreaPageReqVO pageReqVO);

    void updateWareHouseAreaStatus(Long id, Integer status);

    List<WareHouseAreaDO> getWareHouseAreaListByWarehouseId(Long warehouseId);

    Long createShelf(ShelfSaveReqVO createReqVO);

    Map<Long, String> getAreaNamesByIds(Set<Long> ids);
}