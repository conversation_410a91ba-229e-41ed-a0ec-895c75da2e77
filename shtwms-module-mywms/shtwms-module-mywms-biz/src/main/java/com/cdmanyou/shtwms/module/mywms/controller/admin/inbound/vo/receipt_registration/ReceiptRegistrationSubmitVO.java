package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailSubmitVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReceiptRegistrationSubmitVO {

    @Schema(description = "收货单ID")
    private Long receiptId;

    @Schema(description = "入库单ID")
    private Long inboundOrderId;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "待上架总数（实收货品总数）")
    private Integer receivedQuantityTotal;

    @Schema(description = "待上架零散总数（实收货品零散总数）")
    private Integer receivedScatteredQuantityTotal;

    @Schema(description = "待上架总重量（实收货品总重量）")
    private BigDecimal receivedWeightTotal;

    @Schema(description = "商品明细")
    private List<ReceiptRegistrationDetailSubmitVO> receiptRegistrationDetails;

}
