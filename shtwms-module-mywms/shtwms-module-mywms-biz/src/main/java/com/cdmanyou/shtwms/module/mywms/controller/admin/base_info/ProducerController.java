package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProducerDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProducerService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 货主管理")
@RestController
@RequestMapping("/mywms/producer/")
@Validated
public class ProducerController {

    @Resource
    private ProducerService producerService;

    @PostMapping("/create")
    @Operation(summary = "创建货主管理")
    @PreAuthorize("@ss.hasPermission('producer::create')")
    public CommonResult<Long> create(@Valid @RequestBody ProducerSaveReqVO createReqVO) {
        return success(producerService.create(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货主管理")
    @PreAuthorize("@ss.hasPermission('producer::update')")
    public CommonResult<Boolean> update(@Valid @RequestBody ProducerSaveReqVO updateReqVO) {
        producerService.update(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除货主管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('producer::delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        producerService.delete(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得货主管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('producer::query')")
    public CommonResult<ProducerRespVO> get(@RequestParam("id") Long id) {
        ProducerDO producerDO  = producerService.get(id);
        return success(BeanUtils.toBean(producerDO, ProducerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货主管理分页")
    @PreAuthorize("@ss.hasPermission('producer::query')")
    public CommonResult<PageResult<ProducerRespVO>> getPage(@Valid ProducerPageReqVO pageReqVO) {
        PageResult<ProducerDO> pageResult = producerService.getPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProducerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出货主管理 Excel")
    @PreAuthorize("@ss.hasPermission('producer::export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportExcel(@Valid ProducerPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProducerDO> list = producerService.getPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "货主管理.xls", "数据", ProducerRespVO.class,
                        BeanUtils.toBean(list, ProducerRespVO.class));
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新货主状态",
            description = "启用/停用货主状态，status=1启用，status=0停用")
    @PreAuthorize("@ss.hasPermission('producer::update')")
    public CommonResult<Boolean> updateStatus(
            @Parameter(description = "货主ID", required = true)
            @RequestParam("id") Long id,

            @Parameter(description = "状态值(0=停用, 1=启用)", required = true)
            @RequestParam("status") Integer status) {

        // 校验状态值合法性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("非法的状态值：" + status + "，只允许0(停用)或1(启用)");
        }

        if (status == 1) {
            producerService.enableProducer(id);
        } else {
            producerService.disableProducer(id);
        }
        return success(true);
    }

}