package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.SortingTaskOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 分拣任务单主")
@RestController
@RequestMapping("/mywms/sorting-task-order")
@Validated
public class SortingTaskOrderController {

    @Resource
    private SortingTaskOrderService sortingTaskOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建分拣任务单主")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order:create')")
    public CommonResult<Long> createSortingTaskOrder(@Valid @RequestBody SortingTaskOrderSaveReqVO createReqVO) {
        return success(sortingTaskOrderService.createSortingTaskOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新分拣任务单主")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order:update')")
    public CommonResult<Boolean> updateSortingTaskOrder(@Valid @RequestBody SortingTaskOrderSaveReqVO updateReqVO) {
        sortingTaskOrderService.updateSortingTaskOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除分拣任务单主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order:delete')")
    public CommonResult<Boolean> deleteSortingTaskOrder(@RequestParam("id") Long id) {
        sortingTaskOrderService.deleteSortingTaskOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得分拣任务单主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order:query')")
    public CommonResult<SortingTaskOrderRespVO> getSortingTaskOrder(@RequestParam("id") Long id) {
        SortingTaskOrderDO sortingTaskOrder = sortingTaskOrderService.getSortingTaskOrder(id);
        return success(BeanUtils.toBean(sortingTaskOrder, SortingTaskOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得分拣任务单主分页")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order:query')")
    public CommonResult<PageResult<SortingTaskOrderRespVO>> getSortingTaskOrderPage(@Valid SortingTaskOrderPageReqVO pageReqVO) {
        PageResult<SortingTaskOrderDO> pageResult = sortingTaskOrderService.getSortingTaskOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SortingTaskOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出分拣任务单主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSortingTaskOrderExcel(@Valid SortingTaskOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SortingTaskOrderDO> list = sortingTaskOrderService.getSortingTaskOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "分拣任务单主.xls", "数据", SortingTaskOrderRespVO.class,
                        BeanUtils.toBean(list, SortingTaskOrderRespVO.class));
    }

    // ==================== 子表（分拣任务单明细） ====================

    @GetMapping("/sorting-task-order-detail/list-by-sorting-order-id")
    @Operation(summary = "获得分拣任务单明细列表")
    @Parameter(name = "sortingOrderId", description = "分拣任务单ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order:query')")
    public CommonResult<List<SortingTaskOrderDetailDO>> getSortingTaskOrderDetailListBySortingOrderId(@RequestParam("sortingOrderId") Long sortingOrderId) {
        return success(sortingTaskOrderService.getSortingTaskOrderDetailListBySortingOrderId(sortingOrderId));
    }

}