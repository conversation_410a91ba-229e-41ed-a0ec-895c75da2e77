package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 供应商管理新增/修改 Request VO")
@Data
public class SupplierSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3726")
    private Long id;

    @Schema(description = "供应商编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "供应商名称不能为空")
    private String name;

    @Schema(description = "供应商简称", example = "芋艿")
    private String shortName;

    @Schema(description = "供应商类型", example = "2")
    private String type;

    @Schema(description = "供应商英文名称", example = "张三")
    private String englishName;

    @Schema(description = "供应商级别")
    private int level;

    @Schema(description = "所属行业")
    private String industry;

    @Schema(description = "营业执照号")
    private String businessLicense;

    @Schema(description = "供应商描述", example = "你猜")
    private String description;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "联系人不能为空")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "所在地区(省市区)")
    private String region;

    @Schema(description = "信用额度")
    private BigDecimal creditLimit;

    @Schema(description = "启用状态（0启用，1禁用）", example = "1")
    private Integer status;

    @Schema(description = "结算方式")
    private String settlementMethod;

    @Schema(description = "合作状态", example = "2")
    private String cooperationStatus;

}