package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import lombok.*;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品货位关联表-记录货位上的商品数量 DO
 *
 * <AUTHOR>
 */
@TableName("product_warehouse_location")
@KeySequence("product_warehouse_location_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductWareHouseLocationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 货主ID
     */
    private Long producerId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 货位ID
     */
    private Long warehouseLocationId;
    /**
     * 商品批次号
     */
    private String productBatchNumber;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 商品总数
     */
    private Integer count;
    /**
     * 占用数量
     */
    private Integer occupation;
    /**
     * 可用数量
     */
    private Integer available;
    /**
     * 零散总量
     */
    private Integer scatteredCount;
    /**
     * 零散占用量
     */
    private Integer scatteredOccupation;
    /**
     * 零散可用量
     */
    private Integer scatteredAvailable;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer reversion;

    /**
     * 是否冻结（0否1是）
     */
    private Integer freezeType;

    /**
     * 冻结数量
     */
    private Integer freezeCount;

    /**
     * 冻结零散数量
     */
    private Integer freezeScatteredCount;

    /**
     * 所属仓类别
     */
    private Integer houseType;


}