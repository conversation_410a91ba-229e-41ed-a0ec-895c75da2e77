package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 收货登记单主分页 Request VO")
@Data
public class ReceiptRegistrationPageReqVO extends PageParam {

    @Schema(description = "收货单号")
    private String receiptCode;

    @Schema(description = "入库单号")
    private String inboundOrderCode;

    @Schema(description = "收货状态（0草稿，1已提交，2已作废）", example = "1")
    private Integer receiptStatus;

    @Schema(description = "收货员名称")
    private String receiptOperatorName;

    @Schema(description = "上架状态（0未上架，1已上架）")
    private Integer shelvingStatus;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "仓库ID", example = "8626")
    private Long warehouseId;

    @Schema(description = "收货搜索开始时时间(带时分秒)")
    private String receiptSearchStartTime;

    @Schema(description = "收货搜索结束时间(带时分秒)")
    private String receiptSearchEndTime;

}