<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper">
    <update id="freezeProduct">
        UPDATE
            product_warehouse_location
        SET
            freeze_type = 1
        WHERE
            product_id = #{productId}
        AND
            product_batch_number = #{productBatchNumber}
    </update>
    <update id="unfreezeProduct">
        UPDATE
            product_warehouse_location
        SET
            freeze_type = 0
        WHERE
            product_id = #{productId}
        AND
            product_batch_number = #{productBatchNumber}
    </update>

    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationPageRespVO">
        SELECT
            pwl.id,
            pr.name AS producer_name,
            pwl.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            pwl.production_date,
            CASE
                WHEN pwl.production_date IS NOT NULL AND p.shelf_life IS NOT NULL
                THEN DATE_ADD(pwl.production_date, INTERVAL p.shelf_life DAY)
                ELSE NULL
            END AS effective_date,
            pwl.product_batch_number,
            pwl.available,
            w.name AS warehouse_name,
            wl.code AS warehouse_location_code
        FROM
            product_warehouse_location pwl
        LEFT JOIN
            producer pr ON pwl.producer_id = pr.id
        LEFT JOIN
            product p ON pwl.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON pwl.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        WHERE
            pwl.deleted = 0
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND wl.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.productBatchNumber != null and reqVO.productBatchNumber != ''">
            AND pwl.product_batch_number LIKE CONCAT('%', #{reqVO.productBatchNumber}, '%')
        </if>
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            COUNT(pwl.id)
        FROM
            product_warehouse_location pwl
        LEFT JOIN
            producer pr ON pwl.producer_id = pr.id
        LEFT JOIN
            product p ON pwl.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON pwl.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        WHERE
            pwl.deleted = 0
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND wl.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.productBatchNumber != null and reqVO.productBatchNumber != ''">
            AND pwl.product_batch_number LIKE CONCAT('%', #{reqVO.productBatchNumber}, '%')
        </if>
    </select>
    <select id="getProductWarehouseLocationById"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationRespVO">
        SELECT
            pwl.id,
            pr.name AS producer_name,
            pwl.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            pwl.production_date,
            pwl.product_batch_number,
            w.name AS warehouse_name,
            wl.code AS warehouse_location_code,
            pwl.count,
            pwl.occupation,
            pwl.available,
            pwl.scattered_count,
            pwl.scattered_occupation,
            pwl.scattered_available
        FROM
            product_warehouse_location pwl
        LEFT JOIN
            producer pr ON pwl.producer_id = pr.id
        LEFT JOIN
            product p ON pwl.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON pwl.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        WHERE
            pwl.deleted = 0
        AND
            pwl.id = #{id}
    </select>
    <select id="selectByConditions"
            resultType="com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO">
        SELECT
            *
        FROM
            product_warehouse_location
        WHERE
            producer_id = #{producerId}
        AND
            product_id = #{productId}
        AND
            production_date = #{productionDate}
        AND
            warehouse_location_id = #{warehouseLocationId}
        AND
            deleted = 0
    </select>
    <select id="getSimpleLocationStatistics"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.SimpleLocationStatisticVO">
        SELECT
            total_locations.location_total_count,
            COALESCE(occupied_locations.location_occupation_count, 0) AS location_occupation_count,
            (total_locations.location_total_count - COALESCE(occupied_locations.location_occupation_count, 0)) AS location_available_count,
            CASE
                WHEN total_locations.location_total_count > 0 THEN
                    ROUND(
                        ((total_locations.location_total_count - COALESCE(occupied_locations.location_occupation_count, 0)) * 100.0 / total_locations.location_total_count),
                        2
                    )
                ELSE 0
            END AS location_available_percent
        FROM
            (
                SELECT COUNT(*) AS location_total_count
                FROM warehouse_location wl
                WHERE wl.deleted = 0
            ) total_locations
        LEFT JOIN
            (
                SELECT COUNT(DISTINCT pwl.warehouse_location_id) AS location_occupation_count
                FROM product_warehouse_location pwl
                INNER JOIN warehouse_location wl ON pwl.warehouse_location_id = wl.id
                WHERE pwl.deleted = 0
                  AND wl.deleted = 0
                  AND (pwl.available > 0 OR pwl.occupation > 0 OR pwl.scattered_available > 0 OR pwl.scattered_occupation > 0)
            ) occupied_locations ON 1=1
    </select>

    <select id="getSimpleInventoryStatistics"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.SimpleInventoryStatisticVO">
        WITH product_warehouse_location_stats AS (
            SELECT
                pwl.product_id,
                COALESCE(pwl.available, 0) AS current_stock,
                COALESCE(pwl.scattered_available) AS scattered_current_stock
            FROM
                product_warehouse_location pwl
            WHERE
                pwl.deleted = 0
        ),
        product_status AS (
            SELECT
                p.category_id,
                CASE
                    WHEN p.safety_stock IS NOT NULL AND SUM(ps.current_stock) &lt;= p.safety_stock THEN 0
                    WHEN p.max_stock IS NOT NULL AND SUM(ps.current_stock) &gt;= p.max_stock THEN 1
                    WHEN p.stock_warning_threshold IS NOT NULL AND SUM(ps.current_stock) &lt;= p.stock_warning_threshold THEN 2
                    ELSE -1
                END AS inventory_status
            FROM
                product p
            LEFT JOIN
                product_category pc ON pc.id = p.category_id
            LEFT JOIN
                product_warehouse_location_stats ps ON ps.product_id = p.id
            WHERE
                p.deleted = 0
            GROUP BY
                p.id
        )
        SELECT
            COUNT(DISTINCT ps.category_id) AS product_category_total_count,
            SUM(CASE WHEN ps.inventory_status = 0 THEN 1 ELSE 0 END) AS safety_product_count,
            SUM(CASE WHEN ps.inventory_status = 1 THEN 1 ELSE 0 END) AS max_product_count,
            SUM(CASE WHEN ps.inventory_status = 2 THEN 1 ELSE 0 END) AS warning_product_count
        FROM
            product_status ps
    </select>

    <select id="getInventoryPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.InventoryPageRespVO">
        WITH product_stats AS (
            SELECT
                pwl.product_id,
                COALESCE(pwl.available, 0) AS current_stock,
                COALESCE(pwl.scattered_available) AS scattered_current_stock
            FROM
                product_warehouse_location pwl
            WHERE
                pwl.deleted = 0
        )
        SELECT
            p.id AS product_id,
            p.name AS product_name,
            p.category_id,
            pc.name AS category_name,
            p.specification,
            p.unit,
            p.max_stock,
            p.safety_stock,
            p.stock_warning_threshold,
            SUM(ps.current_stock) AS current_stock,
            CASE
                WHEN p.safety_stock IS NOT NULL AND SUM(ps.current_stock) &lt;= p.safety_stock THEN 0
                WHEN p.max_stock IS NOT NULL AND SUM(ps.current_stock) &gt;= p.max_stock THEN 1
                WHEN p.stock_warning_threshold IS NOT NULL AND SUM(ps.current_stock) &lt;= p.stock_warning_threshold THEN 2
                ELSE NULL
            END AS inventory_status
        FROM
            product p
        LEFT JOIN
            product_category pc ON pc.id = p.category_id
        LEFT JOIN
            product_stats ps ON ps.product_id = p.id
        WHERE
            p.deleted = 0
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        GROUP BY
            p.id
        <if test="reqVO.inventoryStatus != null">
            HAVING inventory_status = #{reqVO.inventoryStatus}
        </if>
    </select>

    <select id="getInventoryPageCount" resultType="java.lang.Long">

    </select>

    <select id="getExpirationPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ExpirationPageRespVO">
        WITH product_stats AS (
            SELECT
                pwl.product_id,
                pwl.production_date,
                pwl.product_batch_number,
                COALESCE(pwl.available, 0) AS current_stock,
                COALESCE(pwl.scattered_available) AS scattered_current_stock,
                wl.code AS warehouse_location_code
            FROM
                product_warehouse_location pwl
            LEFT JOIN
                warehouse_location wl ON pwl.warehouse_location_id = wl.id
            WHERE
                pwl.deleted = 0
        )
        SELECT
            p.id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            ps.production_date,
            ps.product_batch_number,
            p.shelf_life,
            CASE
                WHEN p.shelf_life IS NOT NULL THEN DATE_ADD(ps.production_date, INTERVAL p.shelf_life DAY)
                ELSE NULL
            END AS effective_date,
            DATEDIFF(CASE
                WHEN p.shelf_life IS NOT NULL THEN DATE_ADD(ps.production_date, INTERVAL p.shelf_life DAY)
                ELSE NULL
            END, CURDATE()) AS remaining_days,
            COALESCE(SUM(ps.current_stock), 0) AS inventory_count,
            CASE
                WHEN p.shelf_life IS NOT NULL AND DATEDIFF(DATE_ADD(ps.production_date, INTERVAL p.shelf_life DAY), CURDATE()) &gt; 0 THEN 0
                WHEN p.shelf_life IS NOT NULL AND DATEDIFF(DATE_ADD(ps.production_date, INTERVAL p.shelf_life DAY), CURDATE()) &lt;= 30
                         AND DATEDIFF(DATE_ADD(ps.production_date, INTERVAL p.shelf_life DAY), CURDATE()) &gt; 0 THEN 1
                WHEN p.shelf_life IS NOT NULL AND DATEDIFF(DATE_ADD(ps.production_date, INTERVAL p.shelf_life DAY), CURDATE()) &lt;= 0 THEN 2
                ELSE NULL
            END AS expiration_status,
            GROUP_CONCAT(DISTINCT ps.warehouse_location_code) AS warehouse_location_codes
        FROM
            product p
        LEFT JOIN
            product_stats ps ON ps.product_id = p.id
        WHERE
            p.deleted = 0
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        <if test="reqVO.productBatchNumber != null and reqVO.productBatchNumber != ''">
            AND ps.product_batch_number LIKE CONCAT('%', #{reqVO.productBatchNumber}, '%')
        </if>
        <if test="reqVO.productionSearchStartTime != null and reqVO.productionSearchStartTime != ''">
            AND ps.production_date &gt;= #{reqVO.productionSearchStartTime}
        </if>
        <if test="reqVO.productionSearchEndTime != null and reqVO.productionSearchEndTime != ''">
            AND ps.production_date &lt;= #{reqVO.productionSearchEndTime}
        </if>
        GROUP BY
            p.id, ps.production_date, ps.product_batch_number
        <if test="reqVO.expirationStatus != null">
            HAVING expiration_status = #{reqVO.expirationStatus}
        </if>
        ORDER BY
            p.id, ps.production_date
    </select>
    <select id="getExpirationPageCount" resultType="java.lang.Long">


    </select>


</mapper>