package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 货品转移主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransferRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27494")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "转移单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("转移单号")
    private String transferCode;

    @Schema(description = "转移日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("转移日期")
    private LocalDateTime transferDate;

    @Schema(description = "转移数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("转移数量")
    private Integer totalQuantity;

    @Schema(description = "状态（0待转移，1转移成功，2转移失败）", example = "2")
    @ExcelProperty("状态（0待转移，1转移成功，2转移失败）")
    private Integer status;

    @Schema(description = "操作人员ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("操作人员ID")
    private String transferUser;

    @Schema(description = "操作时间")
    @ExcelProperty("操作时间")
    private LocalDateTime transferTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}