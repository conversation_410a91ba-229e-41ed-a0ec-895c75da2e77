package com.cdmanyou.shtwms.module.mywms.service.inventoryplan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan.InventoryPlanDetailMapper;
import com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;

import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan.InventoryPlanMapper;

import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WarehouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WarehouseLocationDO;
/**
 * 盘点计划主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryPlanServiceImpl implements InventoryPlanService {

    @Resource
    private InventoryPlanMapper inventoryPlanMapper;
    @Resource
    private InventoryPlanDetailMapper planDetailMapper;

    @Autowired
    private NumberFieldUtil numberFieldUtil;

    @Resource
    private WareHouseMapper wareHouseMapper;
    @Resource
    private WarehouseLocationMapper wareHouseLocationMapper;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Resource
    private ProductMapper productMapper;
    @Autowired
    private InventoryPlanDetailMapper inventoryPlanDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createInventoryPlan(InventoryPlanSaveReqVO createReqVO) {
        // 插入
        InventoryPlanDO inventoryPlan = BeanUtils.toBean(createReqVO, InventoryPlanDO.class);
        inventoryPlan.setInventoryPlanCode(numberFieldUtil.createWaveOrderCode("InventoryPlan","PD" ));
        inventoryPlanMapper.insert(inventoryPlan);
        // 返回
        return inventoryPlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInventoryPlan(InventoryPlanSaveReqVO updateReqVO) {
        // 更新
        InventoryPlanDO updateObj = BeanUtils.toBean(updateReqVO, InventoryPlanDO.class);
        if (updateObj.getStatus().equals("6") || updateObj.getStatus().equals("3")){
            // 将商品转为启用
            List<InventoryPlanDetailDO> inventoryPlanDetailDOS = inventoryPlanDetailMapper.selectList(new QueryWrapper<InventoryPlanDetailDO>().eq("inventory_plan_id", updateObj.getId()));
            if (!inventoryPlanDetailDOS.isEmpty()){
                List<Long> collect = inventoryPlanDetailDOS.stream().map(InventoryPlanDetailDO::getProductId).collect(Collectors.toList());
                productMapper.update(new UpdateWrapper<ProductDO>().set("status", 0).in("id", collect));
            }
        }
        inventoryPlanMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInventoryPlan(Long id) {
        // 校验存在
        // 删除
        inventoryPlanMapper.deleteById(id);

        // 删除子表
        deletePlanDetailByInventoryPlanId(id);
    }


    @Override
    public InventoryPlanDO getInventoryPlan(Long id) {
        return inventoryPlanMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryPlanDO> getInventoryPlanPage(InventoryPlanPageReqVO pageReqVO) {
        return inventoryPlanMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（盘点计划明细） ====================

    @Override
    public List<InventoryPlanDetailDO> getPlanDetailListByInventoryPlanId(Long inventoryPlanId) {
        return planDetailMapper.selectListByInventoryPlanId(inventoryPlanId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createInventory(InventoryPlanSaveReqVO updateReqVO) throws Exception {
        List<InventoryPlanDetailDO> inventoryPlanDetailDOS = new ArrayList<>();
        List<Long> longs = new ArrayList<>();
        Long planId = updateReqVO.getId();
        Long warehouseId = updateReqVO.getWarehouseId();
        List<WarehouseLocationDO> warehouseLocationDOS = wareHouseLocationMapper.selectList(new QueryWrapper<WarehouseLocationDO>().eq("warehouse_id", warehouseId));
        if (warehouseLocationDOS == null && warehouseLocationDOS.isEmpty()) {
            throw new Exception("仓库没有货物");
        }else {
            List<Long> collect = warehouseLocationDOS.stream().map(WarehouseLocationDO::getId).collect(Collectors.toList());
            List<ProductWareHouseLocationDO> productWareHouseLocationDOS1 = productWareHouseLocationMapper.selectList(new QueryWrapper<ProductWareHouseLocationDO>().in("warehouse_location_id", collect));
            int count = productWareHouseLocationDOS1.stream()
                    .map(ProductWareHouseLocationDO::getOccupation)
                    .filter(Objects::nonNull)  // 过滤掉 null 值
                    .mapToInt(Integer::intValue)  // 转换为 IntStream
                    .sum();
            int count1 = productWareHouseLocationDOS1.stream()
                    .map(ProductWareHouseLocationDO::getScatteredOccupation)
                    .filter(Objects::nonNull)  // 过滤掉 null 值
                    .mapToInt(Integer::intValue)  // 转换为 IntStream
                    .sum();
            if (count>0 || count1>0) {
                if (updateReqVO.getInventoryType() == 1)
                throw new Exception("有商品正在出库中，不能进行全盘");
            }
            for (WarehouseLocationDO wareHouseLocationDO : warehouseLocationDOS) {
                // 去查询货位上的商品数量以及商品信息
                List<ProductWareHouseLocationDO> productWareHouseLocationDOS = productWareHouseLocationMapper.
                        selectList(new QueryWrapper<ProductWareHouseLocationDO>()
                                .in("warehouse_location_id", wareHouseLocationDO.getId())
                                .eq("deleted", 0));

                if (productWareHouseLocationDOS == null && productWareHouseLocationDOS.isEmpty()) {
                }else {
                    for (ProductWareHouseLocationDO productWareHouseLocationDO : productWareHouseLocationDOS) {
                        InventoryPlanDetailDO inventoryPlanDetailDO = new InventoryPlanDetailDO();
                        inventoryPlanDetailDO.setInventoryPlanId(planId);
                        ProductDO productDO = productMapper.selectById(productWareHouseLocationDO.getProductId());
                        WarehouseLocationDO warehouseLocationDO = wareHouseLocationMapper.selectById(productWareHouseLocationDO.getWarehouseLocationId());
                        inventoryPlanDetailDO.setProductCode(productDO.getCustomerCode());
                        inventoryPlanDetailDO.setProductName(productDO.getName());
                        inventoryPlanDetailDO.setSpecification(productDO.getSpecification());
                        inventoryPlanDetailDO.setManufactureDate(productWareHouseLocationDO.getProductionDate());
                        inventoryPlanDetailDO.setLocationId(productWareHouseLocationDO.getWarehouseLocationId());
                        inventoryPlanDetailDO.setEffectiveDate(productWareHouseLocationDO.getProductionDate().plusDays(productDO.getShelfLife()));
                        inventoryPlanDetailDO.setBookQuantity(productWareHouseLocationDO.getCount());
                        inventoryPlanDetailDO.setScatteredBookQuantity(productWareHouseLocationDO.getScatteredCount());
                        inventoryPlanDetailDO.setLocationCode(warehouseLocationDO.getCode());
                        inventoryPlanDetailDO.setProductId(productDO.getId());
                        inventoryPlanDetailDOS.add(inventoryPlanDetailDO);
                        longs.add(productWareHouseLocationDO.getProductId());
                    }
                }

            }
        }
        // 去停用商品,不能再做出入库操作
        if (updateReqVO.getInventoryType() == 1){
            productMapper.update(new UpdateWrapper<ProductDO>().set("status",1).in("id",longs));
        }
        planDetailMapper.insertBatch(inventoryPlanDetailDOS);
        // 修改状态为盘点中
        inventoryPlanMapper.update(new UpdateWrapper<InventoryPlanDO>().set("status",2).in("id",updateReqVO.getId()));
    }

    @Override
    public PageResult<InventoryPlanDetailRespVO> getInventoryDetail(InventoryPlanDetailPageReqVO updateReqVO) {
        PageResult<InventoryPlanDetailRespVO> listPageResult = new PageResult<>();
        QueryWrapper<InventoryPlanDetailDO> inventoryPlanDetailDOQueryWrapper = new QueryWrapper<>();
        inventoryPlanDetailDOQueryWrapper.eq("inventory_plan_id", updateReqVO.getInventoryPlanId());
        inventoryPlanDetailDOQueryWrapper.eq("deleted", 0);
        inventoryPlanDetailDOQueryWrapper.like("product_name",updateReqVO.getProductName());
        inventoryPlanDetailDOQueryWrapper.like("location_code",updateReqVO.getLocationCode());
        Long count = planDetailMapper.selectCount(inventoryPlanDetailDOQueryWrapper);
        inventoryPlanDetailDOQueryWrapper.last("limit "+(updateReqVO.getPageNo()-1)*updateReqVO.getPageSize()+","+updateReqVO.getPageSize());
        QueryWrapper<InventoryPlanDetailDO> inventoryPlanDetailDOQueryWrapper1 = new QueryWrapper<>();
        List<InventoryPlanDetailDO> inventoryPlanDetailDOS = planDetailMapper.selectList(inventoryPlanDetailDOQueryWrapper1);
        List<InventoryPlanDetailRespVO> bean = BeanUtils.toBean(inventoryPlanDetailDOS, InventoryPlanDetailRespVO.class);
        for (InventoryPlanDetailRespVO inventoryPlanDetailRespVO : bean) {
            WarehouseLocationDO warehouseLocationDO = wareHouseLocationMapper.selectById(inventoryPlanDetailRespVO.getLocationId());
            inventoryPlanDetailRespVO.setLocationCode(warehouseLocationDO.getCode());
            WareHouseDO wareHouseDO = wareHouseMapper.selectById(updateReqVO.getWarehouseId());
            inventoryPlanDetailRespVO.setWarehouseName(wareHouseDO.getName());
        }
        listPageResult.setList(bean);
        listPageResult.setTotal(count);
        return listPageResult;
    }

    private void createPlanDetailList(Long inventoryPlanId, List<InventoryPlanDetailDO> list) {
        list.forEach(o -> o.setInventoryPlanId(inventoryPlanId));
        planDetailMapper.insertBatch(list);
    }

    private void updatePlanDetailList(Long inventoryPlanId, List<InventoryPlanDetailDO> list) {
        deletePlanDetailByInventoryPlanId(inventoryPlanId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPlanDetailList(inventoryPlanId, list);
    }

    private void deletePlanDetailByInventoryPlanId(Long inventoryPlanId) {
        planDetailMapper.deleteByInventoryPlanId(inventoryPlanId);
    }

}