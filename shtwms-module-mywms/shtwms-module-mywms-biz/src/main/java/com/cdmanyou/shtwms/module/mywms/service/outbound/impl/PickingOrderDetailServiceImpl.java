package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailStatusReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.*;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.*;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderDetailService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.SortingTaskOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.PICKING_ORDER_DETAIL_NOT_EXISTS;

/**
 * 总拣单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PickingOrderDetailServiceImpl implements PickingOrderDetailService {

    @Resource
    private PickingOrderDetailMapper pickingOrderDetailMapper;

    @Resource
    private WaveOrderDetailMapper waveOrderDetailMapper;

    @Resource
    private PickingOrderMapper pickingOrderMapper;

    @Resource
    private WaveOrderMapper waveOrderMapper;

    @Resource
    private OrderDetailProductWarehouseMapper orderDetailProductWarehouseMapper;

    @Resource
    private OutboundOrderDetailMapper outboundOrderDetailMapper;

    @Resource
    private OutboundOrderMapper outboundOrderMapper;
    @Resource
    private SortingTaskOrderService sortingTaskOrderService;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Override
    public Long createPickingOrderDetail(PickingOrderDetailSaveReqVO createReqVO) {
        // 插入
        PickingOrderDetailDO pickingOrderDetail = BeanUtils.toBean(createReqVO, PickingOrderDetailDO.class);
        pickingOrderDetailMapper.insert(pickingOrderDetail);
        // 返回
        return pickingOrderDetail.getId();
    }

    @Override
    public void updatePickingOrderDetail(PickingOrderDetailSaveReqVO updateReqVO) {
        // 校验存在
        validatePickingOrderDetailExists(updateReqVO.getId());
        // 更新
        PickingOrderDetailDO updateObj = BeanUtils.toBean(updateReqVO, PickingOrderDetailDO.class);
        pickingOrderDetailMapper.updateById(updateObj);
    }

    @Override
    public void deletePickingOrderDetail(Long id) {
        // 校验存在
        validatePickingOrderDetailExists(id);
        // 删除
        pickingOrderDetailMapper.deleteById(id);
    }

    private void validatePickingOrderDetailExists(Long id) {
        if (pickingOrderDetailMapper.selectById(id) == null) {
            throw exception(PICKING_ORDER_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public PickingOrderDetailDO getPickingOrderDetail(Long id) {
        return pickingOrderDetailMapper.selectById(id);
    }

    @Override
    public PageResult<PickingOrderDetailDO> getPickingOrderDetailPage(PickingOrderDetailPageReqVO pageReqVO) {
        return pickingOrderDetailMapper.selectPage(pageReqVO, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPickingOrderDetailList(PickingOrderDO pickingOrderDO) {
        List<PickingOrderDetailDO> list = new ArrayList<>();
        List<WaveOrderDetailDO> waveOrderDetailDOS = waveOrderDetailMapper.selectListByWaveId(pickingOrderDO.getWaveId());
        for (WaveOrderDetailDO waveOrderDetailDO : waveOrderDetailDOS) {
            PickingOrderDetailDO pickingOrderDetailDO = BeanUtils.toBean(waveOrderDetailDO, PickingOrderDetailDO.class);
            pickingOrderDetailDO.setId(null);
            pickingOrderDetailDO.setPickingOrderId(pickingOrderDO.getId());
            pickingOrderDetailDO.setWaveDetailId(waveOrderDetailDO.getId());
            pickingOrderDetailDO.setPendingQuantity(waveOrderDetailDO.getPendingPicking());
            pickingOrderDetailDO.setPendingSimpleQuantity(waveOrderDetailDO.getPendingSimplePicking());
            list.add(pickingOrderDetailDO);
        }
        pickingOrderDetailMapper.insertBatch(list);
    }

    @Override
    public void updatePickingOrderDetailStatus(PickingOrderDetailStatusReqVO statusReqVO) {
        //确认详情
        LambdaUpdateWrapper<PickingOrderDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PickingOrderDetailDO::getPickingDetailStatus, statusReqVO.getPickingDetailStatus());
        updateWrapper.set(Objects.nonNull(statusReqVO.getPickedQuantity()), PickingOrderDetailDO::getPickedQuantity, statusReqVO.getPickedQuantity());
        updateWrapper.set(Objects.nonNull(statusReqVO.getDifferenceQuantity()), PickingOrderDetailDO::getDifferenceQuantity, statusReqVO.getDifferenceQuantity());
        updateWrapper.set(Objects.nonNull(statusReqVO.getPickedSimpleQuantity()), PickingOrderDetailDO::getPickedSimpleQuantity, statusReqVO.getPickedSimpleQuantity());
        updateWrapper.set(Objects.nonNull(statusReqVO.getDifferenceSimpleQuantity()), PickingOrderDetailDO::getDifferenceSimpleQuantity, statusReqVO.getDifferenceSimpleQuantity());
        updateWrapper.eq(PickingOrderDetailDO::getId, statusReqVO.getPickingDetailOrderId());
        pickingOrderDetailMapper.update(updateWrapper);

        PickingOrderDetailDO pickingOrderDetailDO = pickingOrderDetailMapper.selectById(statusReqVO.getPickingDetailOrderId());
        PickingOrderDO pickingOrderDO = pickingOrderMapper.selectById(statusReqVO.getPickingOrderId());
        OrderDetailProductWarehouseDO orderDetailProductWarehouseDO;
        //跟新波次单详情内容
        WaveOrderDetailDO waveOrderDetailDO = waveOrderDetailMapper.selectById(pickingOrderDetailDO.getWaveDetailId());
        WaveOrderDO waveOrderDO = waveOrderMapper.selectById(waveOrderDetailDO.getWaveId());
        if (pickingOrderDO.getPickingType() == 0) {
            waveOrderDO.setPickingStatus(1);
            orderDetailProductWarehouseDO = orderDetailProductWarehouseMapper.selectById(waveOrderDetailDO.getOutboundDetailProductWarehouseId());
        } else {
            // 紧急总检单，更新出库单详情和库存内容
            orderDetailProductWarehouseDO = orderDetailProductWarehouseMapper.selectById(pickingOrderDetailDO.getOutboundDetailProductWarehouseId());
        }
        //修改库存
        ProductWareHouseLocationDO houseLocationDO = productWareHouseLocationMapper.selectById(orderDetailProductWarehouseDO.getProductWarehouseId());
        houseLocationDO.setAvailable(houseLocationDO.getAvailable() - statusReqVO.getPickedQuantity());
        houseLocationDO.setOccupation(houseLocationDO.getOccupation() - statusReqVO.getPickedQuantity());
        houseLocationDO.setScatteredAvailable(houseLocationDO.getScatteredAvailable() - statusReqVO.getPickedSimpleQuantity());
        houseLocationDO.setScatteredOccupation(houseLocationDO.getScatteredOccupation() - statusReqVO.getPickedSimpleQuantity());
        productWareHouseLocationMapper.updateById(houseLocationDO);

        //修改出库单和出库单详情状态
        OutboundOrderDetailDO outboundOrderDetailDO = outboundOrderDetailMapper.selectById(orderDetailProductWarehouseDO.getOutboundDetailId());
        outboundOrderDetailDO.setPickingStatus(1);
        outboundOrderDetailMapper.updateById(outboundOrderDetailDO);
        List<OutboundOrderDetailDO> outboundOrderDetailDOS = outboundOrderDetailMapper.selectListByOutboundId(outboundOrderDetailDO.getOutboundId());
        List<Integer> collect = outboundOrderDetailDOS.stream().map(OutboundOrderDetailDO::getPickingStatus).distinct().collect(Collectors.toList());
        OutboundOrderDO outboundOrderDO = outboundOrderMapper.selectById(outboundOrderDetailDO.getOutboundId());
        if (collect.size() == 1 && collect.get(0) == 1) {
            outboundOrderDO.setPickingStatus(2);
        } else {
            outboundOrderDO.setPickingStatus(1);
        }
        outboundOrderMapper.updateById(outboundOrderDO);


        //判断总检单主
        List<PickingOrderDetailDO> pickingOrderDetailDOS = pickingOrderDetailMapper.selectListByPickingOrderId(statusReqVO.getPickingOrderId());
        List<Integer> statusCollect = pickingOrderDetailDOS.stream().map(PickingOrderDetailDO::getPickingDetailStatus).distinct().collect(Collectors.toList());
        if (statusCollect.size() == 1 && statusCollect.get(0) == 1) {
            //总检单完成修改总检单

            //分拣数量
            int pickedQuantity = pickingOrderDetailDOS.stream().mapToInt(PickingOrderDetailDO::getPickedQuantity).sum();
            pickingOrderDO.setTotalPicked(pickedQuantity);
            //分拣零散数量
            int pickedSimpleQuantity = pickingOrderDetailDOS.stream().mapToInt(PickingOrderDetailDO::getPickedSimpleQuantity).sum();
            pickingOrderDO.setTotalSimplePicked(pickedSimpleQuantity);
            //差异数量
            int differenceQuantity = pickingOrderDetailDOS.stream().mapToInt(PickingOrderDetailDO::getDifferenceQuantity).sum();
            pickingOrderDO.setTotalDifferent(differenceQuantity);
            //零散差异数量
            int differenceSimpleQuantity = pickingOrderDetailDOS.stream().mapToInt(PickingOrderDetailDO::getDifferenceSimpleQuantity).sum();
            pickingOrderDO.setTotalSimpleDifferent(differenceSimpleQuantity);
            pickingOrderDO.setStatus(1);

            pickingOrderMapper.updateById(pickingOrderDO);
            waveOrderDO.setPickingStatus(2);

            //生成分拣单
            sortingTaskOrderService.createSortingTaskOrder(pickingOrderDO.getId());
        }
        //修改波次单状态
        waveOrderMapper.updateById(waveOrderDO);
    }


}