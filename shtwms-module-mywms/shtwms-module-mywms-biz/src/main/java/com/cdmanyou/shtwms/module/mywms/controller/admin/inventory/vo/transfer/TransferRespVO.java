package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;


import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailRespVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


import java.time.LocalDateTime;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "货品转移单详情实体")
@Data
public class TransferRespVO {

    @Schema(description = "转移单ID")
    private Long id;

    @Schema(description = "转移单号")
    private String transferCode;

    @Schema(description = "转移日期")
    private LocalDateTime transferDate;

    @Schema(description = "转移数量")
    private Integer totalQuantity;

    @Schema(description = "转移零散数量")
    private Integer totalScatteredQuantity;

    @Schema(description = "状态（0待转移，1转移成功，2已作废）")
    private Integer status;

    @Schema(description = "转移人员名称")
    private String transferUserName;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime transferTime;

    @Schema(description = "转移单明细")
    private List<TransferDetailRespVO> transferDetails;
}