package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 托盘管理 DO
 *
 * <AUTHOR>
 */
@TableName("tray")
@KeySequence("tray_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrayDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 托盘编码
     */
    private String code;
    /**
     * 所属仓库ID（关联仓库表）
     */
    private Long warehouseId;
    /**
     * 所属库区ID（关联库区表）
     */
    private Long areaId;
    /**
     * 托盘名称
     */
    private String name;
    /**
     * 托盘类型
     */
    private String type;
    /**
     * 托盘容量(吨)
     */
    private BigDecimal capacity;
    /**
     * 条码号
     */
    private String barcode;
    /**
     * 状态(1启用/0停用)
     */
    private Integer status;

}