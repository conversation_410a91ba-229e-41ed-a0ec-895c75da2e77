package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception_detail;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 入库异常处理单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InboundExceptionDetailPageReqVO extends PageParam {

    @Schema(description = "异常类型（0入库异常，1上架异常）", example = "1")
    private Integer exceptionType;

    @Schema(description = "关联的明细表ID", example = "5262")
    private Long relatedDetailId;

    @Schema(description = "业务主表id", example = "5058")
    private Long businessId;

    @Schema(description = "处理方案")
    private String handlingScenarios;

    @Schema(description = "处理结果")
    private String handlingResult;

    @Schema(description = "处理状态（0未处理，1已处理）", example = "2")
    private Integer handlingStatus;

    @Schema(description = "处理人ID")
    private Long handlingUser;

    @Schema(description = "处理时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] handlingTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}