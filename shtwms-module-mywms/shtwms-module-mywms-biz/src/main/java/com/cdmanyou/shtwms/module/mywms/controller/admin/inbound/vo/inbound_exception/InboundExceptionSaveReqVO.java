package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 入库异常处理单主新增/修改 Request VO")
@Data
public class InboundExceptionSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19659")
    private Long id;

    @Schema(description = "关联单据号（收货登记单号/上架单号）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "关联单据号（收货登记单号/上架单号）不能为空")
    private String relatedOrderCode;

    @Schema(description = "异常类型", example = "2")
    private String exceptionType;

    @Schema(description = "处理方案（非必填）")
    private String solution;

    @Schema(description = "处理结果（非必填）")
    private String result;

    @Schema(description = "状态", example = "1")
    private String status;

    @Schema(description = "处理人（非必填）")
    private String handler;

    @Schema(description = "处理时间（非必填）")
    private LocalDateTime handleTime;

    @Schema(description = "入库异常处理单明细列表")
    private List<InboundExceptionDetailDO> exceptionDetails;

}