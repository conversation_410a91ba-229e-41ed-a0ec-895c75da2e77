package com.cdmanyou.shtwms.module.mywms.service.inventory;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;
import com.cdmanyou.shtwms.framework.common.exception.ServiceException;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.security.core.util.SecurityFrameworkUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferInputVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailInputVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 货品转移 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransferServiceImpl implements TransferService {

    @Resource
    private TransferMapper transferMapper;

    @Resource
    private TransferDetailMapper transferDetailMapper;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Resource
    private ProductWareHouseLocationService productWareHouseLocationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransfer(TransferInputVO inputVO) {
        // 插入
        TransferDO transfer = BeanUtils.toBean(inputVO, TransferDO.class);
        // 生成转移单号
        transfer.setTransferCode(generateTransferCode());
        transferMapper.insert(transfer);
        // 插入子表
        createTransferDetailList(transfer.getId(), inputVO.getTransferDetails());
        // 返回
        return transfer.getId();
    }

    private String generateTransferCode() {
        // 生成入库单日期号
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成序列号
        Long maxSequence = transferMapper.getMaxSequence(dateStr);
        Long newMaxSequence = (maxSequence == null) ? 1L : maxSequence + 1;
        // 格式化序列号
        String sequence = String.format("%06d", newMaxSequence);
        return "ZY" + dateStr + sequence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransfer(TransferInputVO inputVO) {
        // 校验存在
        validateTransferExists(inputVO.getId());
        // 更新
        TransferDO updateObj = BeanUtils.toBean(inputVO, TransferDO.class);
        transferMapper.updateById(updateObj);
        // 更新子表
        updateTransferDetailList(inputVO.getId(), inputVO.getTransferDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void voidByCode(String code) {
        // 校验存在性
        TransferDO transferDO = validateTransferExists(code);
        if (transferDO.getStatus() != null && transferDO.getStatus() != 0) {
            throw exception(TRANSFER_VOID_ERROR_BY_STATUS, transferDO.getTransferCode());
        }
        // 作废
        TransferDO updateObj = new TransferDO();
        updateObj.setId(transferDO.getId())
                .setStatus(2);
        transferMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTransfer(TransferInputVO inputVO) {
        // 校验存在性
        TransferDO transferDO = validateTransferExists(inputVO.getId());
        if (transferDO.getStatus() != null && transferDO.getStatus() != 0) {
            throw exception(TRANSFER_COMPLETE_ERROR_BY_STATUS);
        }
        List<TransferDetailInputVO> transferDetails = inputVO.getTransferDetails();
        // 校验必填字段
        validateTransferDetailsForComplete(transferDetails);

        TransferDO updateObj = new TransferDO();
        updateObj.setId(transferDO.getId())
                .setTransferDate(inputVO.getTransferDate())
                .setStatus(1)
                .setTransferUser(SecurityFrameworkUtils.getLoginUserId())
                .setTransferTime(LocalDateTime.now());

        int i = 1;
        for (TransferDetailInputVO detail : transferDetails) {
            String detailInfo = String.format("第%d条", i);
            // 校验转出货位与所属仓类别是否匹配
            List<ProductWareHouseLocationDO> wareHouseLocationDOS = productWareHouseLocationMapper.selectListByLocationId(detail.getFromWarehouseLocationId());
            if (CollUtil.isNotEmpty(wareHouseLocationDOS)) {
                for (ProductWareHouseLocationDO wareHouseLocationDO : wareHouseLocationDOS) {
                    if (!wareHouseLocationDO.getHouseType().equals(detail.getFromWarehouseLocationHouseType())) {
                        if (wareHouseLocationDO.getCount() == 0) {
                            productWareHouseLocationMapper.deleteById(wareHouseLocationDO.getId());
                        }else {
                            throw exception(TRANSFER_COMPLETE_ERROR_BY_FROM_WAREHOUSE_LOCATION_HOUSE_TYPE_NOT_MATCH, detailInfo);
                        }
                    }
                }
            }
            // 校验转入货位与所属仓类别是否匹配
            wareHouseLocationDOS = productWareHouseLocationMapper.selectListByLocationId(detail.getToWarehouseLocationId());
            if (CollUtil.isNotEmpty(wareHouseLocationDOS)) {
                for (ProductWareHouseLocationDO wareHouseLocationDO : wareHouseLocationDOS) {
                    if (!wareHouseLocationDO.getHouseType().equals(detail.getToWarehouseLocationHouseType())) {
                        if (wareHouseLocationDO.getCount() == 0) {
                            productWareHouseLocationMapper.deleteById(wareHouseLocationDO.getId());
                        }else {
                            throw exception(TRANSFER_COMPLETE_ERROR_BY_TO_WAREHOUSE_LOCATION_HOUSE_TYPE_NOT_MATCH, detailInfo);
                        }
                    }
                }
            }
            // 更新库存
            productWareHouseLocationService.updateProductWarehouseLocationByTransferSubmit(detail, detailInfo);
            i ++;
        }

        // 更新货品转移单
        transferMapper.updateById(updateObj);
        // 更新货品转移明细
        updateTransferDetailList(transferDO.getId(), transferDetails);
    }

    private void validateTransferDetailsForComplete(List<TransferDetailInputVO> transferDetails) {
        for (int i = 0; i < transferDetails.size(); i++) {
            TransferDetailInputVO detailInputVO = transferDetails.get(i);
            String detailInfo = String.format("第%d条", i + 1);
            if (detailInputVO.getProductionDate() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_PRODUCTION_DATE_EMPTY, detailInfo);
            }
            if (detailInputVO.getProductBatchNumber() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_PRODUCT_BATCH_NUMBER_EMPTY, detailInfo);
            }
            if (detailInputVO.getFromWarehouseLocationHouseType() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_FROM_WAREHOUSE_LOCATION_HOUSE_TYPE_EMPTY, detailInfo);
            }
            if (detailInputVO.getFromWarehouseLocationId() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_FROM_WAREHOUSE_LOCATION_ID_EMPTY, detailInfo);
            }
            if (detailInputVO.getToWarehouseLocationHouseType() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_TO_WAREHOUSE_LOCATION_HOUSE_TYPE_EMPTY, detailInfo);
            }
            if (detailInputVO.getToWarehouseLocationId() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_TO_WAREHOUSE_LOCATION_ID_EMPTY, detailInfo);
            }
            if (detailInputVO.getQuantity() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_QUANTITY_EMPTY, detailInfo);
            }
            if (detailInputVO.getScatteredQuantity() == null) {
                throw exception(TRANSFER_COMPLETE_ERROR_BY_SCATTERED_QUANTITY_EMPTY, detailInfo);
            }
        }
    }


    private TransferDO validateTransferExists(Long id) {
        TransferDO transferDO = transferMapper.selectById(id);
        if (transferDO== null) {
            throw exception(TRANSFER_NOT_EXISTS);
        }
        return transferDO;
    }

    private TransferDO validateTransferExists(String code) {
        TransferDO transferDO = transferMapper.selectByCode(code);
        if (transferDO== null) {
            throw exception(TRANSFER_NOT_EXISTS);
        }
        return transferDO;
    }

    @Override
    public TransferRespVO getTransferByCode(String code) {
        return transferMapper.getTransferByCode(code);
    }

    @Override
    public PageResult<TransferPageRespVO> getTransferPage(TransferPageReqVO pageReqVO) {
        Page<TransferPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = transferMapper.selectPage(page, pageReqVO);
        Long total = transferMapper.selectPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

    // ==================== 子表（货品转移明细） ====================

    @Override
    public List<TransferDetailDO> getTransferDetailListByTransferId(Long transferId) {
        return transferDetailMapper.selectListByTransferId(transferId);
    }


    @Transactional(rollbackFor = Exception.class)
    public void createTransferDetailList(Long transferId, List<TransferDetailInputVO> list) {
        List<TransferDetailDO> createList = new ArrayList<>();
        list.forEach(detail -> {
            TransferDetailDO createBean = BeanUtils.toBean(detail, TransferDetailDO.class);
            createBean.setId(null).setTransferId(transferId);
            createList.add(createBean);
        });
        transferDetailMapper.insertBatch(createList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteTransferDetailByTransferId(Long transferId) {
        transferDetailMapper.deleteByTransferId(transferId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateTransferDetailList(Long transferId, List<TransferDetailInputVO> list) {
        deleteTransferDetailByTransferId(transferId);
		createTransferDetailList(transferId, list);
    }


}