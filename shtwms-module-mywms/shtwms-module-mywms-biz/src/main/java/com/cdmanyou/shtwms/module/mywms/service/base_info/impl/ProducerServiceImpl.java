package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProducerDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProducerMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProducerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 货主管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProducerServiceImpl implements ProducerService {

    @Resource
    private ProducerMapper producerMapper;

    @Override
    public Long create(ProducerSaveReqVO createReqVO) {
        // 自动生成货主编码: HZ + 毫秒级时间戳
        String generatedCode = "HZ" + System.currentTimeMillis();
        createReqVO.setCode(generatedCode); // 覆盖前端传入的编码

        // 插入
        ProducerDO producerDO = BeanUtils.toBean(createReqVO, ProducerDO.class);
        producerMapper.insert(producerDO);
        // 返回
        return producerDO.getId();
    }

    @Override
    public void update(ProducerSaveReqVO updateReqVO) {
        // 校验存在
        validateExists(updateReqVO.getId());
        // 更新
        ProducerDO updateObj = BeanUtils.toBean(updateReqVO, ProducerDO.class);
        producerMapper.updateById(updateObj);
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        validateExists(id);
        // 删除
        producerMapper.deleteById(id);
    }

    private ProducerDO validateExists(Long id) {
        ProducerDO producer = producerMapper.selectById(id);
        if (producer == null) {
            throw exception(PRODUCER_NOT_EXISTS);
        }
        return producer; // 返回查询到的对象
    }

    @Override
    public ProducerDO get(Long id) {
        return producerMapper.selectById(id);
    }



    @Override
    public PageResult<ProducerDO> getPage(ProducerPageReqVO pageReqVO) {
        return producerMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableProducer(Long id) {
        ProducerDO producer = validateExists(id);
        // 幂等性检查：当前已启用则无需操作
        if (producer.getStatus() != null && producer.getStatus().equals("1")) {
            throw exception(PRODUCER_ALREADY_ENABLED); // 需要定义错误码常量
        }
        // 更新状态为启用(1)
        ProducerDO updateObj = new ProducerDO();
        updateObj.setId(id);
        updateObj.setStatus("1");
        producerMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableProducer(Long id) {
        ProducerDO producer = validateExists(id);
        // 幂等性检查：当前已停用则无需操作
        if (producer.getStatus() != null && producer.getStatus().equals("0")) {
            throw exception(PRODUCER_ALREADY_DISABLED); // 需要定义错误码常量
        }
        // 更新状态为停用(0)
        ProducerDO updateObj = new ProducerDO();
        updateObj.setId(id);
        updateObj.setStatus("0");
        producerMapper.updateById(updateObj);
    }
}