package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHousePageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHouseSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.WareHouseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 仓库管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WareHouseServiceImpl implements WareHouseService {

    @Resource
    private WareHouseMapper wareHouseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createWareHouse(WareHouseSaveReqVO createReqVO) {

        if (wareHouseMapper.existsByCode(createReqVO.getCode())) {
            throw exception(WARE_HOUSE_CODE_DUPLICATE);
        }

        // 校验名称唯一性
        if (wareHouseMapper.existsByName(createReqVO.getName())) {
            throw exception(WARE_HOUSE_NAME_DUPLICATE);
        }

        // 插入主表
        WareHouseDO wareHouse = BeanUtils.toBean(createReqVO, WareHouseDO.class);
        wareHouseMapper.insert(wareHouse);
        return wareHouse.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWareHouse(WareHouseSaveReqVO updateReqVO) {
        // 校验存在
        validateWareHouseExists(updateReqVO.getId());

        if (wareHouseMapper.existsByCodeAndIdNot(updateReqVO.getCode(), updateReqVO.getId())) {
            throw exception(WARE_HOUSE_CODE_DUPLICATE);
        }

        // 校验名称唯一性（排除自身）
        if (wareHouseMapper.existsByNameAndIdNot(updateReqVO.getName(), updateReqVO.getId())) {
            throw exception(WARE_HOUSE_NAME_DUPLICATE);
        }
        // 更新主表
        WareHouseDO updateObj = BeanUtils.toBean(updateReqVO, WareHouseDO.class);
        wareHouseMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWareHouse(Long id) {
        // 校验存在
        validateWareHouseExists(id);
        // 删除主表
        wareHouseMapper.deleteById(id);
    }

    private WareHouseDO validateWareHouseExists(Long id) {
        WareHouseDO wareHouse = wareHouseMapper.selectById(id);
        if (wareHouse == null) {
            throw exception(WARE_HOUSE_NOT_EXISTS);
        }
        return wareHouse;
    }

    @Override
    public WareHouseDO getWareHouse(Long id) {
        return wareHouseMapper.selectById(id);
    }

    @Override
    public PageResult<WareHouseDO> getWareHousePage(WareHousePageReqVO pageReqVO) {
        return wareHouseMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWareHouseStatus(Long id, Integer status) {
        // 校验仓库是否存在
        WareHouseDO wareHouse = validateWareHouseExists(id);

        // 更新状态
        WareHouseDO updateObj = new WareHouseDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        wareHouseMapper.updateById(updateObj);
    }

    // 重构校验方法，返回实体对象


    @Override
    public List<WareHouseDO> getWareHouseList() {
        // 使用 MyBatis Plus 的 selectList 方法查询所有仓库
        return wareHouseMapper.selectList();
    }

}

