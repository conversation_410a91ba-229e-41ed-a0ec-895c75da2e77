package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;

import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ShelvingManagementDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ShelvingManagementDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 上架管理单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShelvingManagementDetailServiceImpl implements ShelvingManagementDetailService {

    @Resource
    private ShelvingManagementDetailMapper shelvingManagementDetailMapper;

    @Override
    public Long createShelvingManagementDetail(ShelvingManagementDetailSaveReqVO createReqVO) {
        // 插入
        ShelvingManagementDetailDO shelvingManagementDetail = BeanUtils.toBean(createReqVO, ShelvingManagementDetailDO.class);
        shelvingManagementDetailMapper.insert(shelvingManagementDetail);
        // 返回
        return shelvingManagementDetail.getId();
    }

    @Override
    public void updateShelvingManagementDetail(ShelvingManagementDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateShelvingManagementDetailExists(updateReqVO.getId());
        // 更新
        ShelvingManagementDetailDO updateObj = BeanUtils.toBean(updateReqVO, ShelvingManagementDetailDO.class);
        shelvingManagementDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteShelvingManagementDetail(Long id) {
        // 校验存在
        validateShelvingManagementDetailExists(id);
        // 删除
        shelvingManagementDetailMapper.deleteById(id);
    }

    private void validateShelvingManagementDetailExists(Long id) {
        if (shelvingManagementDetailMapper.selectById(id) == null) {
            throw exception(new ErrorCode(500, "对象不存在"));
        }
    }

    @Override
    public ShelvingManagementDetailDO getShelvingManagementDetail(Long id) {
        return shelvingManagementDetailMapper.selectById(id);
    }

    @Override
    public PageResult<ShelvingManagementDetailDO> getShelvingManagementDetailPage(ShelvingManagementDetailPageReqVO pageReqVO) {
        return shelvingManagementDetailMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDetailsByShelvingManagementSubmit(Long shelvingManagementId, List<ShelvingManagementDetailSaveVO> list) {
        List<ShelvingManagementDetailDO> detailDOS = shelvingManagementDetailMapper.selectListByShelvingId(shelvingManagementId);
        List<Long> detailIds = detailDOS.stream().map(ShelvingManagementDetailDO::getId).collect(Collectors.toList());
        for (ShelvingManagementDetailSaveVO detailSaveVO : list) {
            // 新增
            if (detailSaveVO.getId() == null) {
                ShelvingManagementDetailDO createObj = BeanUtils.toBean(detailSaveVO, ShelvingManagementDetailDO.class);
                shelvingManagementDetailMapper.insert(createObj);
            }
            // 编辑
            if (detailIds.contains(detailSaveVO.getId())) {
                ShelvingManagementDetailDO updateObj = new ShelvingManagementDetailDO();
                updateObj.setId(detailSaveVO.getId())
                        .setPendingQuantity(detailSaveVO.getPendingQuantity())
                        .setPendingScatteredQuantity(detailSaveVO.getPendingScatteredQuantity())
                        .setReceivedQuantity(detailSaveVO.getReceivedQuantity())
                        .setReceivedScatteredQuantity(detailSaveVO.getReceivedScatteredQuantity())
                        .setShelvedQuantity(detailSaveVO.getShelvedQuantity())
                        .setShelvedScatteredQuantity(detailSaveVO.getShelvedScatteredQuantity())
                        .setReceivedWeight(detailSaveVO.getReceivedWeight())
                        .setShelvedWeight(detailSaveVO.getShelvedWeight())
                        .setWarehouseLocationId(detailSaveVO.getWarehouseLocationId());
                shelvingManagementDetailMapper.updateById(updateObj);
                // 移除
                detailIds.remove(detailSaveVO.getId());
            }
        }
        // 删除
        for (Long detailId : detailIds) {
            shelvingManagementDetailMapper.deleteById(detailId);
        }
    }

}