package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;

import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 收货登记单主 Response VO")
@Data
public class ReceiptRegistrationPageRespVO {

    @Schema(description = "收货单ID")
    private Long id;

    @Schema(description = "收货单号")
    private String receiptCode;

    @Schema(description = "收货时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate receiptTime;

    @Schema(description = "入库单ID")
    private Long inboundOrderId;

    @Schema(description = "入库单单号")
    private String inboundOrderCode;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "收货状态（0草稿，1待上架，2已上架）")
    private Integer receiptStatus;

    @Schema(description = "待收货品总量 - 入库总数")
    private Integer pendingQuantityTotal;

    @Schema(description = "待收货品零散总量")
    private Integer pendingScatteredQuantityTotal;

    @Schema(description = "实收货品总量 - 实收数量")
    private Integer receivedQuantityTotal;

    @Schema(description = "实收货品零散总量")
    private Integer receivedScatteredQuantityTotal;

    @Schema(description = "商品总数差值")
    private Integer diffQuantity;

    @Schema(description = "商品零散总数差值")
    private Integer diffScatteredQuantity;

    @Schema(description = "收货员ID")
    private Long receiptOperator;

    @Schema(description = "收货员名称")
    private String receiptOperatorName;

}