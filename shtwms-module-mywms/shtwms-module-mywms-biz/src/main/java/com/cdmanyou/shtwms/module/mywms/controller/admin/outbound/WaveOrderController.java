package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.WaveOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 波次单主")
@RestController
@RequestMapping("/mywms/wave-order")
@Validated
public class WaveOrderController {

    @Resource
    private WaveOrderService waveOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建波次单主")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order:create')")
    public CommonResult<Long> createWaveOrder(@RequestBody List<Long> outboundIds) {
        return success(waveOrderService.createWaveOrder(outboundIds));
    }

    @PutMapping("/update")
    @Operation(summary = "更新波次单主")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order:update')")
    public CommonResult<Boolean> updateWaveOrder(@Valid @RequestBody WaveOrderSaveReqVO updateReqVO) {
        waveOrderService.updateWaveOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除波次单主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:wave-order:delete')")
    public CommonResult<Boolean> deleteWaveOrder(@RequestParam("id") Long id) {
        waveOrderService.deleteWaveOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得波次单主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order:query')")
    public CommonResult<WaveOrderRespVO> getWaveOrder(@RequestParam("id") Long id) {
        WaveOrderDO waveOrder = waveOrderService.getWaveOrder(id);
        return success(BeanUtils.toBean(waveOrder, WaveOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得波次单主分页")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order:query')")
    public CommonResult<PageResult<WaveOrderRespVO>> getWaveOrderPage(@Valid WaveOrderPageReqVO pageReqVO) {
        PageResult<WaveOrderDO> pageResult = waveOrderService.getWaveOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WaveOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出波次单主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWaveOrderExcel(@Valid WaveOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WaveOrderDO> list = waveOrderService.getWaveOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "波次单主.xls", "数据", WaveOrderRespVO.class,
                        BeanUtils.toBean(list, WaveOrderRespVO.class));
    }

    // ==================== 子表（波次单明细） ====================

    @GetMapping("/wave-order-detail/list-by-wave-id")
    @Operation(summary = "获得波次单明细列表")
    @Parameter(name = "waveId", description = "波次单ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order:query')")
    public CommonResult<List<WaveOrderDetailDO>> getWaveOrderDetailListByWaveId(@RequestParam("waveId") Long waveId) {
        return success(waveOrderService.getWaveOrderDetailListByWaveId(waveId));
    }

}