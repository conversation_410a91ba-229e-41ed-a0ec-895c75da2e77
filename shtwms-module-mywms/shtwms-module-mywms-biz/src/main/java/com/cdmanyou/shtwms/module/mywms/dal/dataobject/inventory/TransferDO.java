package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory;

import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 货品转移主 DO
 *
 * <AUTHOR>
 */
@TableName("goods_transfer")
@KeySequence("goods_transfer_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 转移单号
     */
    private String transferCode;
    /**
     * 转移日期
     */
    private LocalDateTime transferDate;
    /**
     * 转移数量总数
     */
    private Integer totalQuantity;
    /**
     * 转移零散数量总数
     */
    private Integer totalScatteredQuantity;
    /**
     * 状态（0待转移，1转移成功，2转移失败）
     */
    private Integer status;
    /**
     * 操作人员ID
     */
    private String transferUser;
    /**
     * 操作时间
     */
    private LocalDateTime transferTime;

}