package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 盘点计划明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryPlanDetailPageReqVO extends PageParam {

    @Schema(description = "盘点计划ID（关联主表）", example = "8868")
    private Long inventoryPlanId;

    @Schema(description = "商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", example = "王五")
    private String productName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}