package com.cdmanyou.shtwms.module.mywms.service.inventoryplan;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan.InventoryPlanDetailMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;


import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 盘点计划明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryPlanDetailServiceImpl implements InventoryPlanDetailService {

    @Resource
    private InventoryPlanDetailMapper planDetailMapper;

    @Override
    public Long createPlanDetail(InventoryPlanDetailSaveReqVO createReqVO) {
        // 插入
        InventoryPlanDetailDO planDetail = BeanUtils.toBean(createReqVO, InventoryPlanDetailDO.class);
        planDetailMapper.insert(planDetail);
        // 返回
        return planDetail.getId();
    }

    @Override
    public void updatePlanDetail(InventoryPlanDetailSaveReqVO updateReqVO) {
        // 校验存在
        // 更新
        InventoryPlanDetailDO updateObj = BeanUtils.toBean(updateReqVO, InventoryPlanDetailDO.class);
        planDetailMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanDetail(Long id) {
        // 校验存在
        // 删除
        planDetailMapper.deleteById(id);
    }


    @Override
    public InventoryPlanDetailDO getPlanDetail(Long id) {
        return planDetailMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryPlanDetailDO> getPlanDetailPage(InventoryPlanDetailPageReqVO pageReqVO) {
        return planDetailMapper.selectPage(pageReqVO);
    }

}