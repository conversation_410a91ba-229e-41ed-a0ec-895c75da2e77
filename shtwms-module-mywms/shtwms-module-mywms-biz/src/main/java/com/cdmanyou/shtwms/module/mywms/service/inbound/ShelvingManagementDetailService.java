package com.cdmanyou.shtwms.module.mywms.service.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 上架管理单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ShelvingManagementDetailService {

    /**
     * 创建上架管理单明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createShelvingManagementDetail(@Valid ShelvingManagementDetailSaveReqVO createReqVO);

    /**
     * 更新上架管理单明细
     *
     * @param updateReqVO 更新信息
     */
    void updateShelvingManagementDetail(@Valid ShelvingManagementDetailSaveReqVO updateReqVO);

    /**
     * 删除上架管理单明细
     *
     * @param id 编号
     */
    void deleteShelvingManagementDetail(Long id);

    /**
     * 获得上架管理单明细
     *
     * @param id 编号
     * @return 上架管理单明细
     */
    ShelvingManagementDetailDO getShelvingManagementDetail(Long id);

    /**
     * 获得上架管理单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 上架管理单明细分页
     */
    PageResult<ShelvingManagementDetailDO> getShelvingManagementDetailPage(ShelvingManagementDetailPageReqVO pageReqVO);

    /**
     * 更新上架单明细 - 上架单保存/提交
     *
     * @param shelvingManagementId 上架单ID
     * @param list 上架单明细列表
     */
    void updateDetailsByShelvingManagementSubmit(Long shelvingManagementId, List<ShelvingManagementDetailSaveVO> list);
}