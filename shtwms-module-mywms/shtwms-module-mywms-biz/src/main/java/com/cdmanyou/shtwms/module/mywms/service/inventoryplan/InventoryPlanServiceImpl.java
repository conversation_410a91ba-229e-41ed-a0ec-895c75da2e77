package com.cdmanyou.shtwms.module.mywms.service.inventoryplan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan.InventoryPlanDetailMapper;
import com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;

import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan.InventoryPlanMapper;

import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WarehouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WarehouseLocationDO;
/**
 * 盘点计划主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryPlanServiceImpl implements InventoryPlanService {

    @Resource
    private InventoryPlanMapper inventoryPlanMapper;
    @Resource
    private InventoryPlanDetailMapper planDetailMapper;

    @Autowired
    private NumberFieldUtil numberFieldUtil;

    @Resource
    private WareHouseMapper wareHouseMapper;
    @Resource
    private WarehouseLocationMapper wareHouseLocationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createInventoryPlan(InventoryPlanSaveReqVO createReqVO) {
        // 插入
        InventoryPlanDO inventoryPlan = BeanUtils.toBean(createReqVO, InventoryPlanDO.class);
        inventoryPlan.setInventoryPlanCode(numberFieldUtil.createWaveOrderCode("InventoryPlan","PD" ));
        inventoryPlanMapper.insert(inventoryPlan);

        // 插入子表
//        createPlanDetailList(inventoryPlan.getId(), createReqVO.getPlanDetails());
        // 返回
        return inventoryPlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInventoryPlan(InventoryPlanSaveReqVO updateReqVO) {
        // 更新
        InventoryPlanDO updateObj = BeanUtils.toBean(updateReqVO, InventoryPlanDO.class);
        inventoryPlanMapper.updateById(updateObj);

        // 更新子表
//        updatePlanDetailList(updateReqVO.getId(), updateReqVO.getPlanDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInventoryPlan(Long id) {
        // 校验存在
        // 删除
        inventoryPlanMapper.deleteById(id);

        // 删除子表
        deletePlanDetailByInventoryPlanId(id);
    }


    @Override
    public InventoryPlanDO getInventoryPlan(Long id) {
        return inventoryPlanMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryPlanDO> getInventoryPlanPage(InventoryPlanPageReqVO pageReqVO) {
        return inventoryPlanMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（盘点计划明细） ====================

    @Override
    public List<InventoryPlanDetailDO> getPlanDetailListByInventoryPlanId(Long inventoryPlanId) {
        return planDetailMapper.selectListByInventoryPlanId(inventoryPlanId);
    }

    @Override
    public void createInventory(InventoryPlanSaveReqVO updateReqVO) throws Exception {
        List<InventoryPlanDetailDO> inventoryPlanDetailDOS = new ArrayList<>();
        Long planId = updateReqVO.getId();
        Long warehouseId = updateReqVO.getWarehouseId();
        WareHouseDO wareHouseDO = wareHouseMapper.selectById(warehouseId);
        List<WarehouseLocationDO> warehouseId1 = wareHouseLocationMapper.selectList(new QueryWrapper<WarehouseLocationDO>().eq("warehouse_id", warehouseId));
        if (warehouseId1 != null && !warehouseId1.isEmpty()) {
            throw new Exception("仓库没有货物");
        }else {
            for (WarehouseLocationDO wareHouseLocationDO : warehouseId1) {
                InventoryPlanDetailDO inventoryPlanDetailDO = new InventoryPlanDetailDO();
                inventoryPlanDetailDO.setInventoryPlanId(planId);

                inventoryPlanDetailDOS.add(inventoryPlanDetailDO);
            }
        }

    }

    private void createPlanDetailList(Long inventoryPlanId, List<InventoryPlanDetailDO> list) {
        list.forEach(o -> o.setInventoryPlanId(inventoryPlanId));
        planDetailMapper.insertBatch(list);
    }

    private void updatePlanDetailList(Long inventoryPlanId, List<InventoryPlanDetailDO> list) {
        deletePlanDetailByInventoryPlanId(inventoryPlanId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPlanDetailList(inventoryPlanId, list);
    }

    private void deletePlanDetailByInventoryPlanId(Long inventoryPlanId) {
        planDetailMapper.deleteByInventoryPlanId(inventoryPlanId);
    }

}