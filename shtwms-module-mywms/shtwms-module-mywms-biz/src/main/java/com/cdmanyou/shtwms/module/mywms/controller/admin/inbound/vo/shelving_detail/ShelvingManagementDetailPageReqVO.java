package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 上架管理单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShelvingManagementDetailPageReqVO extends PageParam {

    @Schema(description = "上架单ID", example = "6136")
    private Long shelvingId;

    @Schema(description = "收货单ID", example = "13575")
    private Long receiptId;

    @Schema(description = "入库单ID", example = "29764")
    private Long inboundOrderId;

    @Schema(description = "商品ID", example = "23939")
    private Long productId;

    @Schema(description = "商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", example = "赵六")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "系统批次号")
    private String systemBatchNumber;

    @Schema(description = "生产日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] productionDate;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "收货数量（待上架数量）")
    private Integer receivedQuantity;

    @Schema(description = "上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "托盘ID（关联托盘表，提交并上架时必填）", example = "5672")
    private Long trayId;

    @Schema(description = "仓库ID（关联仓库表，提交并上架时必填）", example = "27451")
    private Long warehouseId;

    @Schema(description = "库区ID（关联库区表，提交并上架时必填）", example = "16657")
    private Long areaId;

    @Schema(description = "库位ID（关联货位表，提交并上架时必填）", example = "22563")
    private Long locationId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}