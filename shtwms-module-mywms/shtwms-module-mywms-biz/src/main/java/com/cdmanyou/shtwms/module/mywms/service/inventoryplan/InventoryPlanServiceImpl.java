package com.cdmanyou.shtwms.module.mywms.service.inventoryplan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;
import com.cdmanyou.shtwms.framework.common.exception.ServiceException;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OrderDetailProductWarehouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ShelvingManagementDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ShelvingManagementMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan.InventoryPlanDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OrderDetailProductWarehouseMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderMapper;
import com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants;
import com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;

import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan.InventoryPlanMapper;

import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WarehouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WarehouseLocationDO;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 盘点计划主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryPlanServiceImpl implements InventoryPlanService {

    @Resource
    private InventoryPlanMapper inventoryPlanMapper;
    @Resource
    private InventoryPlanDetailMapper planDetailMapper;

    @Autowired
    private NumberFieldUtil numberFieldUtil;

    @Resource
    private WareHouseMapper wareHouseMapper;
    @Resource
    private WarehouseLocationMapper wareHouseLocationMapper;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Resource
    private ProductMapper productMapper;
    @Autowired
    private InventoryPlanDetailMapper inventoryPlanDetailMapper;

    @Resource
    private OutboundOrderMapper outboundOrderMapper;

    @Resource
    private OutboundOrderDetailMapper outboundOrderDetailMapper;

    @Resource
    private OrderDetailProductWarehouseMapper orderDetailProductWarehouseMapper;

    @Resource
    private ShelvingManagementMapper shelvingManagementMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createInventoryPlan(InventoryPlanSaveReqVO createReqVO) {
        // 创建盘点计划判定

        // 插入
        InventoryPlanDO inventoryPlan = BeanUtils.toBean(createReqVO, InventoryPlanDO.class);
        inventoryPlan.setInventoryPlanCode(numberFieldUtil.createWaveOrderCode("InventoryPlan","PD" ));
        inventoryPlanMapper.insert(inventoryPlan);
        // 返回
        return inventoryPlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInventoryPlan(InventoryPlanSaveReqVO updateReqVO) {
        // 更新
        InventoryPlanDO updateObj = BeanUtils.toBean(updateReqVO, InventoryPlanDO.class);
        if (updateObj.getStatus().equals("3")){
            // 将商品转为启用
            List<InventoryPlanDetailDO> inventoryPlanDetailDOS = inventoryPlanDetailMapper.selectList(new QueryWrapper<InventoryPlanDetailDO>().eq("inventory_plan_id", updateObj.getId()));
            // 更新库存
            for (InventoryPlanDetailDO inventoryPlanDetailDO : inventoryPlanDetailDOS) {
                    ProductWareHouseLocationDO productWareHouseLocationDO = productWareHouseLocationMapper.selectOne(new QueryWrapper<ProductWareHouseLocationDO>().eq("product_id", inventoryPlanDetailDO.getProductId())
                            .eq("warehouse_location_id", inventoryPlanDetailDO.getLocationId()));
                    productWareHouseLocationMapper.update(new UpdateWrapper<ProductWareHouseLocationDO>()
                            .eq("product_id",inventoryPlanDetailDO.getProductId())
                            .eq("warehouse_location_id",inventoryPlanDetailDO.getLocationId())
                            .set("count",inventoryPlanDetailDO.getActualQuantity())
                            .set("available",inventoryPlanDetailDO.getActualQuantity()-productWareHouseLocationDO.getOccupation())
                            .set("scattered_count",inventoryPlanDetailDO.getScatteredActualQuantity())
                            .set("scattered_available",
                                    inventoryPlanDetailDO.getScatteredActualQuantity()
                                            -productWareHouseLocationDO.getScatteredOccupation()));
                }
            }
        inventoryPlanMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInventoryPlan(Long id) {
        // 校验存在
        // 删除
        inventoryPlanMapper.deleteById(id);

        // 删除子表
        deletePlanDetailByInventoryPlanId(id);
    }


    @Override
    public InventoryPlanDO getInventoryPlan(Long id) {
        return inventoryPlanMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryPlanDO> getInventoryPlanPage(InventoryPlanPageReqVO pageReqVO) {
        return inventoryPlanMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（盘点计划明细） ====================

    @Override
    public List<InventoryPlanDetailDO> getPlanDetailListByInventoryPlanId(Long inventoryPlanId) {
        return planDetailMapper.selectListByInventoryPlanId(inventoryPlanId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createInventory(InventoryPlanSaveReqVO updateReqVO) throws Exception {
        List<InventoryPlanDetailDO> inventoryPlanDetailDOS = new ArrayList<>();
        List<Long> longs = new ArrayList<>();
        Long planId = updateReqVO.getId();
        if (updateReqVO.getInventoryType() == 0){
            // 判定现在是不是有未发运的出库单，如果有，不能进行全盘操作
            List<OutboundOrderDO> outboundOrderDOS = outboundOrderMapper.selectList(new QueryWrapper<OutboundOrderDO>().eq("warehouse_id", updateReqVO.getWarehouseId()).eq("send_status", 0).eq("picking_status", 2));
            if (!outboundOrderDOS.isEmpty()) {
                throw exception(ErrorCodeConstants.ORDER_HAVE);
            }
            // 如果有待上架的上架单，也不能进行全盘
            List<ShelvingManagementDO> shelvingManagementDOS = shelvingManagementMapper.selectList(new QueryWrapper<ShelvingManagementDO>()
                    .eq("warehouse_id", updateReqVO.getWarehouseId())
                    .eq("shelving_status", 0));
            if (!CollectionUtils.isEmpty(shelvingManagementDOS)){
                throw exception(ErrorCodeConstants.SHELVING_HAVE);
            }
            Long warehouseId = updateReqVO.getWarehouseId();
            List<WarehouseLocationDO> warehouseLocationDOS = wareHouseLocationMapper.selectList(new QueryWrapper<WarehouseLocationDO>().eq("warehouse_id", warehouseId));
            if (warehouseLocationDOS.isEmpty()) {
                throw exception(ErrorCodeConstants.ON_GOOD);
            }else {
                List<Long> collect = warehouseLocationDOS.stream().map(WarehouseLocationDO::getId).collect(Collectors.toList());
                List<ProductWareHouseLocationDO> productWareHouseLocationDOS1 = productWareHouseLocationMapper.selectList(new QueryWrapper<ProductWareHouseLocationDO>().in("warehouse_location_id", collect));
                int count = productWareHouseLocationDOS1.stream()
                        .map(ProductWareHouseLocationDO::getOccupation)
                        .filter(Objects::nonNull)  // 过滤掉 null 值
                        .mapToInt(Integer::intValue)  // 转换为 IntStream
                        .sum();
                int count1 = productWareHouseLocationDOS1.stream()
                        .map(ProductWareHouseLocationDO::getScatteredOccupation)
                        .filter(Objects::nonNull)  // 过滤掉 null 值
                        .mapToInt(Integer::intValue)  // 转换为 IntStream
                        .sum();
                if (count>0 || count1>0) {
                    throw exception(ErrorCodeConstants.GOOD_USE);
                }
                for (WarehouseLocationDO wareHouseLocationDO : warehouseLocationDOS) {
                    // 去查询货位上的商品数量以及商品信息
                    List<ProductWareHouseLocationDO> productWareHouseLocationDOS = productWareHouseLocationMapper.
                            selectList(new QueryWrapper<ProductWareHouseLocationDO>()
                                    .in("warehouse_location_id", wareHouseLocationDO.getId())
                                    .eq("deleted", 0));

                    if (productWareHouseLocationDOS == null && productWareHouseLocationDOS.isEmpty()) {
                    }else {
                        for (ProductWareHouseLocationDO productWareHouseLocationDO : productWareHouseLocationDOS) {
                            InventoryPlanDetailDO inventoryPlanDetailDO = new InventoryPlanDetailDO();
                            inventoryPlanDetailDO.setInventoryPlanId(planId);
                            ProductDO productDO = productMapper.selectById(productWareHouseLocationDO.getProductId());
                            WarehouseLocationDO warehouseLocationDO = wareHouseLocationMapper.selectById(productWareHouseLocationDO.getWarehouseLocationId());
                            inventoryPlanDetailDO.setProductCode(productDO.getCustomerCode());
                            inventoryPlanDetailDO.setProductName(productDO.getName());
                            inventoryPlanDetailDO.setSpecification(productDO.getSpecification());
                            inventoryPlanDetailDO.setManufactureDate(productWareHouseLocationDO.getProductionDate());
                            inventoryPlanDetailDO.setLocationId(productWareHouseLocationDO.getWarehouseLocationId());
                            inventoryPlanDetailDO.setEffectiveDate(productWareHouseLocationDO.getProductionDate().plusDays(productDO.getShelfLife()));
                            inventoryPlanDetailDO.setBookQuantity(productWareHouseLocationDO.getCount());
                            inventoryPlanDetailDO.setActualQuantity(productWareHouseLocationDO.getCount());
                            inventoryPlanDetailDO.setDifferenceQuantity(0);
                            inventoryPlanDetailDO.setScatteredBookQuantity(productWareHouseLocationDO.getScatteredCount());
                            inventoryPlanDetailDO.setScatteredActualQuantity(productWareHouseLocationDO.getScatteredCount());
                            inventoryPlanDetailDO.setScatteredDifferenceQuantity(0);
                            inventoryPlanDetailDO.setLocationCode(warehouseLocationDO.getCode());
                            inventoryPlanDetailDO.setProductBatchNumber(productWareHouseLocationDO.getProductBatchNumber());
                            inventoryPlanDetailDO.setDeputyMeasure(productDO.getDeputyMeasure());
                            inventoryPlanDetailDO.setMasterMeasure(productDO.getMasterMeasure());
                            inventoryPlanDetailDO.setProductId(productDO.getId());
                            inventoryPlanDetailDOS.add(inventoryPlanDetailDO);
                            longs.add(productWareHouseLocationDO.getProductId());
                        }
                    }

                }
            }
            planDetailMapper.insertBatch(inventoryPlanDetailDOS);
            // 修改状态为盘点中
            inventoryPlanMapper.update(new UpdateWrapper<InventoryPlanDO>().set("status",2).in("id",updateReqVO.getId()));
        }else {
             // 判定是否能生成动盘 1.不能有库存占用；2.要有未发运的出库单
            List<OutboundOrderDO> outboundOrderDOS = outboundOrderMapper.selectList(new QueryWrapper<OutboundOrderDO>().eq("warehouse_id", updateReqVO.getWarehouseId()).eq("send_status", 0).eq("picking_status", 2));
            if (outboundOrderDOS.isEmpty()) {
                throw exception(ErrorCodeConstants.ORDER_NO);
            }
            // 获取到出库单的id
            List<Long> collect = outboundOrderDOS.stream().map(OutboundOrderDO::getId).collect(Collectors.toList());
            // 使用出库单id去查询 所有的出库单详情
            List<OutboundOrderDetailDO> outboundId = outboundOrderDetailMapper.selectList(new QueryWrapper<OutboundOrderDetailDO>().in("outbound_id", collect));
            // 获取到出库单详情的id集合
            List<Long> collect1 = outboundId.stream().map(OutboundOrderDetailDO::getId).collect(Collectors.toList());
            List<OrderDetailProductWarehouseDO> outboundDetailId = orderDetailProductWarehouseMapper.selectList(new QueryWrapper<OrderDetailProductWarehouseDO>().in("outbound_detail_id", collect1));
            // 获取到库存表的id
            List<Long> collect2 = outboundDetailId.stream().map(OrderDetailProductWarehouseDO::getProductWarehouseId).collect(Collectors.toList());
            List<ProductWareHouseLocationDO> productWareHouseLocationDOS = productWareHouseLocationMapper.selectByIds(collect2);
            int count = productWareHouseLocationDOS.stream()
                    .map(ProductWareHouseLocationDO::getOccupation)
                    .filter(Objects::nonNull)  // 过滤掉 null 值
                    .mapToInt(Integer::intValue)  // 转换为 IntStream
                    .sum();
            int count1 = productWareHouseLocationDOS.stream()
                    .map(ProductWareHouseLocationDO::getScatteredOccupation)
                    .filter(Objects::nonNull)  // 过滤掉 null 值
                    .mapToInt(Integer::intValue)  // 转换为 IntStream
                    .sum();
            if (count>0 || count1>0) {
                throw exception(ErrorCodeConstants.GOOD_USE);
            }

            for (ProductWareHouseLocationDO productWareHouseLocationDO : productWareHouseLocationDOS) {
                InventoryPlanDetailDO inventoryPlanDetailDO = new InventoryPlanDetailDO();
                inventoryPlanDetailDO.setInventoryPlanId(planId);
                ProductDO productDO = productMapper.selectById(productWareHouseLocationDO.getProductId());
                WarehouseLocationDO warehouseLocationDO = wareHouseLocationMapper.selectById(productWareHouseLocationDO.getWarehouseLocationId());
                inventoryPlanDetailDO.setProductCode(productDO.getCustomerCode());
                inventoryPlanDetailDO.setProductName(productDO.getName());
                inventoryPlanDetailDO.setSpecification(productDO.getSpecification());
                inventoryPlanDetailDO.setManufactureDate(productWareHouseLocationDO.getProductionDate());
                inventoryPlanDetailDO.setLocationId(productWareHouseLocationDO.getWarehouseLocationId());
                inventoryPlanDetailDO.setEffectiveDate(productWareHouseLocationDO.getProductionDate().plusDays(productDO.getShelfLife()));
                inventoryPlanDetailDO.setBookQuantity(productWareHouseLocationDO.getCount());
                inventoryPlanDetailDO.setActualQuantity(productWareHouseLocationDO.getCount());
                inventoryPlanDetailDO.setDifferenceQuantity(0);
                inventoryPlanDetailDO.setScatteredBookQuantity(productWareHouseLocationDO.getScatteredCount());
                inventoryPlanDetailDO.setScatteredActualQuantity(productWareHouseLocationDO.getScatteredCount());
                inventoryPlanDetailDO.setScatteredDifferenceQuantity(0);
                inventoryPlanDetailDO.setLocationCode(warehouseLocationDO.getCode());
                inventoryPlanDetailDO.setProductBatchNumber(productWareHouseLocationDO.getProductBatchNumber());
                inventoryPlanDetailDO.setDeputyMeasure(productDO.getDeputyMeasure());
                inventoryPlanDetailDO.setMasterMeasure(productDO.getMasterMeasure());
                inventoryPlanDetailDO.setProductId(productDO.getId());
                inventoryPlanDetailDOS.add(inventoryPlanDetailDO);
                longs.add(productWareHouseLocationDO.getProductId());
            }
            // 去拿到现在仓库中所有的为发运的出货单
            planDetailMapper.insertBatch(inventoryPlanDetailDOS);
            // 修改状态为盘点中
            inventoryPlanMapper.update(new UpdateWrapper<InventoryPlanDO>().set("status",2).in("id",updateReqVO.getId()));
        }
    }

    @Override
    public PageResult<InventoryPlanDetailRespVO> getInventoryDetail(InventoryPlanDetailPageReqVO updateReqVO) {
        PageResult<InventoryPlanDetailRespVO> listPageResult = new PageResult<>();
        QueryWrapper<InventoryPlanDetailDO> inventoryPlanDetailDOQueryWrapper = new QueryWrapper<>();
        inventoryPlanDetailDOQueryWrapper.eq("inventory_plan_id", updateReqVO.getInventoryPlanId());
        inventoryPlanDetailDOQueryWrapper.eq("deleted", 0);
        if (updateReqVO.getProductName() != null && !updateReqVO.getProductName().isEmpty()) {
            inventoryPlanDetailDOQueryWrapper.like("product_name",updateReqVO.getProductName());
        }
        if (updateReqVO.getLocationCode() != null && !updateReqVO.getLocationCode().isEmpty()) {
            inventoryPlanDetailDOQueryWrapper.like("location_code",updateReqVO.getLocationCode());
        }
        Long count = planDetailMapper.selectCount(inventoryPlanDetailDOQueryWrapper);
        inventoryPlanDetailDOQueryWrapper.last("limit "+(updateReqVO.getPageNo()-1)*updateReqVO.getPageSize()+","+updateReqVO.getPageSize());
        QueryWrapper<InventoryPlanDetailDO> inventoryPlanDetailDOQueryWrapper1 = new QueryWrapper<>();
        List<InventoryPlanDetailDO> inventoryPlanDetailDOS = planDetailMapper.selectList(inventoryPlanDetailDOQueryWrapper);
        List<InventoryPlanDetailRespVO> bean = BeanUtils.toBean(inventoryPlanDetailDOS, InventoryPlanDetailRespVO.class);
        for (InventoryPlanDetailRespVO inventoryPlanDetailRespVO : bean) {
            WarehouseLocationDO warehouseLocationDO = wareHouseLocationMapper.selectById(inventoryPlanDetailRespVO.getLocationId());
            inventoryPlanDetailRespVO.setLocationCode(warehouseLocationDO.getCode());
            Long inventoryPlanId = updateReqVO.getInventoryPlanId();
            InventoryPlanDO inventoryPlanDO = inventoryPlanMapper.selectById(inventoryPlanId);
            WareHouseDO wareHouseDO = wareHouseMapper.selectById(inventoryPlanDO.getWarehouseId());
            inventoryPlanDetailRespVO.setWarehouseName(wareHouseDO.getName());
        }
        listPageResult.setList(bean);
        listPageResult.setTotal(count);
        return listPageResult;
    }

    @Override
    public void batchUpdateInventoryPlan(List<InventoryPlanSaveReqVO> updateReqVO) {
        // 更新
        List<InventoryPlanDO> updateObj = BeanUtils.toBean(updateReqVO, InventoryPlanDO.class);
        for (InventoryPlanDO inventoryPlanDO : updateObj) {
            // 将商品转为启用
            List<InventoryPlanDetailDO> inventoryPlanDetailDOS = inventoryPlanDetailMapper.selectList(new QueryWrapper<InventoryPlanDetailDO>().eq("inventory_plan_id", inventoryPlanDO.getId()));
            if (!inventoryPlanDetailDOS.isEmpty()){
                List<Long> collect = inventoryPlanDetailDOS.stream().map(InventoryPlanDetailDO::getProductId).collect(Collectors.toList());
                productMapper.update(new UpdateWrapper<ProductDO>().set("status", 0).in("id", collect));
            }
            inventoryPlanMapper.updateById(inventoryPlanDO);
        }
    }

    @Override
    public List<InventoryPlanDO> selectStatusByDate(LocalDate time,Integer inventoryType) {
        return inventoryPlanMapper.selectStatusByDate(time,inventoryType);
    }

    private void createPlanDetailList(Long inventoryPlanId, List<InventoryPlanDetailDO> list) {
        list.forEach(o -> o.setInventoryPlanId(inventoryPlanId));
        planDetailMapper.insertBatch(list);
    }

    private void updatePlanDetailList(Long inventoryPlanId, List<InventoryPlanDetailDO> list) {
        deletePlanDetailByInventoryPlanId(inventoryPlanId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPlanDetailList(inventoryPlanId, list);
    }

    private void deletePlanDetailByInventoryPlanId(Long inventoryPlanId) {
        planDetailMapper.deleteByInventoryPlanId(inventoryPlanId);
    }

}