package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ShelfDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ShelfMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ShelfService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.SHELF_NOT_EXISTS;

/**
 * 货架管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShelfServiceImpl implements ShelfService {

    @Resource
    private ShelfMapper shelfMapper;

    @Override
    public Long createShelf(ShelfSaveReqVO createReqVO) {
        // 插入
        ShelfDO shelf = BeanUtils.toBean(createReqVO, ShelfDO.class);
        shelfMapper.insert(shelf);
        // 返回
        return shelf.getId();
    }

    @Override
    public void updateShelf(ShelfSaveReqVO updateReqVO) {
        // 校验存在
        validateShelfExists(updateReqVO.getId());
        // 更新
        ShelfDO updateObj = BeanUtils.toBean(updateReqVO, ShelfDO.class);
        shelfMapper.updateById(updateObj);
    }

    @Override
    public void deleteShelf(Long id) {
        // 校验存在
        validateShelfExists(id);
        // 删除
        shelfMapper.deleteById(id);
    }

    private void validateShelfExists(Long id) {
        if (shelfMapper.selectById(id) == null) {
            throw exception(SHELF_NOT_EXISTS);
        }
    }

    @Override
    public ShelfDO getShelf(Long id) {
        return shelfMapper.selectById(id);
    }

    @Override
    public PageResult<ShelfDO> getShelfPage(ShelfPageReqVO pageReqVO) {
        return shelfMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShelfStatus(Long id, Integer status) {
        // 校验存在
        validateShelfExists(id);

        // 校验状态值合法性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("状态值只能是0或1");
        }

        // 更新状态
        ShelfDO updateObj = new ShelfDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        shelfMapper.updateById(updateObj);
    }

    @Override
    public List<ShelfDO> getShelfListByAreaId(Long areaId) {
        return shelfMapper.selectListByAreaId(areaId);
    }
}