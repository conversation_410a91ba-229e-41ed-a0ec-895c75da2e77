<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderMapper">


    <select id="getOutboundOrderPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.OutboundOrderPageRespVO">
        select oo.id
             , oo.outbound_code
             , oo.outbound_type
             , oo.order_status
             , oo.outbound_way
             , oo.price             as outbound_price
             , wo.wave_code
             , p2.name              as producer_name
             , p2.code              as producer_code
             , oo.producer_order_code
             , s.name               as supplier_name
             , s.code               as supplier_code
             , c.name               as customer_name
             , c.code               as customer_code
             , p.name               as product_name
             , p.code               as product_code
             , p.specification      as product_specification
             , p.temperature_zone
             , ood.pending_quantity as outbound_count
             , p.loose_quantity     as conversion_rate
             , ood.simple_count
             , ood.gross_weight     as outbound_weight
             , ood.volume           as outbound_volume
             , ood.product_batch_number
             , ood.production_date
             , ood.expiration_date
             , ood.straddle_warehouse
             , oo.booking_delivery_time
             , oo.send_status       as sendProductStatus
             , oo.send_time         as sendProductTime
             , oo.sorting_complete_time
             , oo.create_time
             , oo.creator
             , oo.remark
        from shtwms.outbound_order_detail ood
                 inner join shtwms.outbound_order oo on ood.outbound_id = oo.id and oo.deleted = 0
                 left join shtwms.customer c on oo.customer_id = c.id
                 left join shtwms.producer p2 on oo.producer_id = p2.id
                 left join shtwms.supplier s on ood.supplier_id = s.id
                 left join shtwms.wave_outbound_order woo on woo.outbound_order_id = oo.id
                 left join shtwms.wave_order wo on wo.id = woo.wellen_id
                 left join shtwms.product p on ood.product_id = p.id
        where ood.deleted = 0
    </select>
</mapper>