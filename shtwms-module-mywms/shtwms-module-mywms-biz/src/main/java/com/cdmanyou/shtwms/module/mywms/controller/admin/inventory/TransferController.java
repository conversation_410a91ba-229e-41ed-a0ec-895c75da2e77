package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.inventory.TransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 货品转移主")
@RestController
@RequestMapping("/mywms/transfer")
@Validated
public class TransferController {

    @Resource
    private TransferService transferService;

    @PostMapping("/create")
    @Operation(summary = "创建货品转移主")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:create')")
    public CommonResult<Long> createTransfer(@Valid @RequestBody TransferSaveReqVO createReqVO) {
        return success(transferService.createTransfer(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货品转移主")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:update')")
    public CommonResult<Boolean> updateTransfer(@Valid @RequestBody TransferSaveReqVO updateReqVO) {
        transferService.updateTransfer(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除货品转移主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:transfer:delete')")
    public CommonResult<Boolean> deleteTransfer(@RequestParam("id") Long id) {
        transferService.deleteTransfer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得货品转移主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:query')")
    public CommonResult<TransferRespVO> getTransfer(@RequestParam("id") Long id) {
        TransferDO transfer = transferService.getTransfer(id);
        return success(BeanUtils.toBean(transfer, TransferRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货品转移主分页")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:query')")
    public CommonResult<PageResult<TransferRespVO>> getTransferPage(@Valid TransferPageReqVO pageReqVO) {
        PageResult<TransferDO> pageResult = transferService.getTransferPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransferRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出货品转移主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransferExcel(@Valid TransferPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransferDO> list = transferService.getTransferPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "货品转移主.xls", "数据", TransferRespVO.class,
                        BeanUtils.toBean(list, TransferRespVO.class));
    }

    // ==================== 子表（货品转移明细） ====================

    @GetMapping("/transfer-detail/list-by-transfer-id")
    @Operation(summary = "获得货品转移明细列表")
    @Parameter(name = "transferId", description = "转移单ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('mywms:transfer:query')")
    public CommonResult<List<TransferDetailDO>> getTransferDetailListByTransferId(@RequestParam("transferId") Long transferId) {
        return success(transferService.getTransferDetailListByTransferId(transferId));
    }

}