package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ShelvingManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 上架管理单")
@RestController
@RequestMapping("/shelving/management")
@Validated
public class ShelvingManagementController { 

    @Resource
    private ShelvingManagementService managementService;

    @PutMapping("/update")
    @Operation(summary = "更新上架管理单")
    @PreAuthorize("@ss.hasPermission('shelving:management:update')")
    public CommonResult<Boolean> updateManagement(@Valid @RequestBody ShelvingManagementSaveVO updateReqVO) {
        managementService.updateManagement(updateReqVO);
        return success(true);
    }

    @PostMapping("save-submit")
    @Operation(summary = "保存/提交上架单")
    @PreAuthorize("@ss.hasPermission('shelving:management:update')")
    public CommonResult<Boolean> saveShelvingManagement(@Valid @RequestBody ShelvingManagementSaveVO saveReqVO) {
        managementService.saveShelvingManagement(saveReqVO);
        return success(true);
    }

    @GetMapping("/getByCode")
    @Operation(summary = "通过上架单号获取上架单详情")
    @Parameter(name = "code", description = "上架单号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('shelving:management:query')")
    public CommonResult<ShelvingManagementInfoVO> getShelvingManagementByCode(@RequestParam("code") String code) {
        return success(managementService.getShelvingManagementByCode(code));
    }

    @PostMapping("/page")
    @Operation(summary = "获得上架管理单分页")
    @PreAuthorize("@ss.hasPermission('shelving:management:query')")
    public CommonResult<PageResult<ShelvingManagementPageRespVO>> getManagementPage(@Valid @RequestBody ShelvingManagementPageReqVO pageReqVO) {
        return success(managementService.getManagementPage(pageReqVO));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出上架管理单 Excel")
//    @PreAuthorize("@ss.hasPermission('shelving:management:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportManagementExcel(@Valid ShelvingManagementPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<ShelvingManagementDO> list = managementService.getManagementPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "上架管理单.xls", "数据", ShelvingManagementPageRespVO.class,
//                        BeanUtils.toBean(list, ShelvingManagementPageRespVO.class));
//    }

    // ==================== 子表（上架管理单明细） ====================

    @GetMapping("/shelving-management-detail/list-by-shelving-id")
    @Operation(summary = "获得上架管理单明细列表")
    @Parameter(name = "shelvingId", description = "上架单ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('shelving:management:query')")
    public CommonResult<List<ShelvingManagementDetailDO>> getShelvingManagementDetailListByShelvingId(@RequestParam("shelvingId") Long shelvingId) {
        return success(managementService.getShelvingManagementDetailListByShelvingId(shelvingId));
    }

}