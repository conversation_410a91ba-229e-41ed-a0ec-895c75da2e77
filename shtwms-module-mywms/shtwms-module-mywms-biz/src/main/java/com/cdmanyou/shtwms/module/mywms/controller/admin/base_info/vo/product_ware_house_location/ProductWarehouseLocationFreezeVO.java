package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "库存管理冻结/解冻实体")
@Data
public class ProductWarehouseLocationFreezeVO {

    @Schema(description = "库存ID")
    private Long productWarehouseLocationId;

    @Schema(description = "操作类型（0解冻，1冻结）")
    private Integer type;

    @Schema(description = "操作数量")
    private Integer count;

    @Schema(description = "操作零散数量")
    private Integer scatteredCount;

    @Schema(description = "备注")
    private String remark;
}
