package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 仓库管理新增/修改 Request VO")
@Data
public class WareHouseSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14757")
    private Long id;

    @Schema(description = "仓库编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "仓库编码不能为空")
    private String code;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "仓库名称不能为空")
    private String name;

    @Schema(description = "仓库类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "仓库类型不能为空")
    private String type;

    @Schema(description = "仓库面积(㎡)")
    private BigDecimal area;

    @Schema(description = "仓库容量(吨)")
    private BigDecimal capacity;

    @Schema(description = "仓库状态(1启用/0停用)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "仓库状态(1启用/0停用)不能为空")
    private Integer status;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "仓库描述", example = "随便")
    private String description;

    @Schema(description = "库区管理列表")
    private List<WareHouseAreaDO> wareHouseAreas;

}