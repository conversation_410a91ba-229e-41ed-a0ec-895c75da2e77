-- 客户管理表
CREATE TABLE `customer` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '客户编码',
  `name` VARCHAR(255) NOT NULL COMMENT '客户名称',
  `short_name` VARCHAR(255) COMMENT '客户简称',
  `type` ENUM('内部','外部','其它') COMMENT '客户类型',
  `english_name` VARCHAR(255) COMMENT '客户英文名称',
  `business_license` VARCHAR(255) COMMENT '营业执照号',
  `description` VARCHAR(500) COMMENT '客户描述',
  `contact_person` VARCHAR(255) NOT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(255) NOT NULL COMMENT '联系电话',
  `contact_email` VARCHAR(255) COMMENT '联系邮箱',
  `region` VARCHAR(255) COMMENT '所在地区(省市区)',
  `credit_limit` DECIMAL(16,4) COMMENT '信用额度',
  `settlement_method` VARCHAR(255) COMMENT '结算方式',
  `cooperation_status` VARCHAR(255) COMMENT '合作状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户管理表';

-- 供应商管理表
CREATE TABLE `supplier` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '供应商编码',
  `name` VARCHAR(255) NOT NULL COMMENT '供应商名称',
  `short_name` VARCHAR(255) COMMENT '供应商简称',
  `type` ENUM('内部','外部','其它') COMMENT '供应商类型',
  `english_name` VARCHAR(255) COMMENT '供应商英文名称',
  `level` ENUM('A','B','C') COMMENT '供应商级别',
  `industry` VARCHAR(255) COMMENT '所属行业',
  `business_license` VARCHAR(255) COMMENT '营业执照号',
  `description` VARCHAR(500) COMMENT '供应商描述',
  `contact_person` VARCHAR(255) NOT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(255) NOT NULL COMMENT '联系电话',
  `contact_email` VARCHAR(255) COMMENT '联系邮箱',
  `region` VARCHAR(255) COMMENT '所在地区(省市区)',
  `credit_limit` DECIMAL(16,4) COMMENT '信用额度',
  `settlement_method` VARCHAR(255) COMMENT '结算方式',
  `cooperation_status` VARCHAR(255) COMMENT '合作状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商管理表';

-- 商品类别管理表
CREATE TABLE `product_category` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '类别编码',
  `name` VARCHAR(255) NOT NULL COMMENT '类别名称',
  `description` VARCHAR(500) COMMENT '类别描述',
  `parent_id` BIGINT COMMENT '父类别ID',
  `status` TINYINT DEFAULT 1 COMMENT '是否启用(1启用/0停用)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品类别管理表';

-- 商品管理表
CREATE TABLE `product` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '商品编码',
  `customer_code` VARCHAR(255) COMMENT '客户编码',
  `name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `brand` VARCHAR(255) COMMENT '品牌',
  `sku` VARCHAR(255) COMMENT 'SKU',
  `barcode` VARCHAR(255) COMMENT '条形码',
  `unit` VARCHAR(255) COMMENT '计量单位',
  `shelf_life` INT COMMENT '保质期(天)',
  `customer_id` BIGINT COMMENT '客户ID（关联客户表）',
  `category_id` BIGINT COMMENT '商品类别ID（关联商品类别表）',
  `main_image` VARCHAR(255) COMMENT '主图',
  `sub_images` VARCHAR(1000) COMMENT '附图',
  `description` VARCHAR(500) COMMENT '商品描述',
  `usage_instruction` VARCHAR(500) COMMENT '使用说明',
  `packing_instruction` VARCHAR(500) COMMENT '包装说明',
  `precautions` VARCHAR(500) COMMENT '注意事项',
  `safety_stock` INT COMMENT '安全库存',
  `max_stock` INT COMMENT '最大库存',
  `stock_warning_threshold` INT COMMENT '库存预警阈值',
  `pallet_layer_qty` INT COMMENT '码盘单层数量',
  `pallet_layer_height` DECIMAL(10,2) COMMENT '码盘层高',
  `status` TINYINT DEFAULT 1 COMMENT '状态(1启用/0停用)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品管理表';

-- 仓库管理表
CREATE TABLE `warehouse` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '仓库编码',
  `name` VARCHAR(255) NOT NULL COMMENT '仓库名称',
  `type` ENUM('常温','冷藏','冷冻') COMMENT '仓库类型',
  `area` DECIMAL(16,4) COMMENT '仓库面积(㎡)',
  `capacity` DECIMAL(16,4) COMMENT '仓库容量(吨)',
  `status` TINYINT DEFAULT 1 COMMENT '仓库状态(1启用/0停用)',
  `address` VARCHAR(500) COMMENT '仓库地址',
  `contact_person` VARCHAR(255) NOT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(255) NOT NULL COMMENT '联系电话',
  `contact_email` VARCHAR(255) COMMENT '联系邮箱',
  `description` VARCHAR(500) COMMENT '仓库描述',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库管理表';

-- 库区管理表
CREATE TABLE `warehouse_area` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '库区编码',
  `name` VARCHAR(255) NOT NULL COMMENT '库区名称',
  `warehouse_id` BIGINT NOT NULL COMMENT '所属仓库ID（关联仓库表）',
  `type` ENUM('常温','冷藏','冷冻') COMMENT '库区类型',
  `area` DECIMAL(16,4) COMMENT '库区面积(㎡)',
  `status` TINYINT DEFAULT 1 COMMENT '库区状态(1启用/0停用)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库区管理表';

-- 货架管理表
CREATE TABLE `shelf` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '货架编码',
  `name` VARCHAR(255) NOT NULL COMMENT '货架名称',
  `warehouse_id` BIGINT NOT NULL COMMENT '所属仓库ID（关联仓库表）',
  `area_id` BIGINT NOT NULL COMMENT '所属库区ID（关联库区表）',
  `type` VARCHAR(255) COMMENT '货架类型',
  `area` DECIMAL(16,4) COMMENT '货架面积(㎡)',
  `status` TINYINT DEFAULT 1 COMMENT '货架状态(1启用/0停用)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货架管理表';

-- 货位管理表
CREATE TABLE `warehouse_location` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '货位编码',
  `name` VARCHAR(255) NOT NULL COMMENT '货位名称',
  `warehouse_id` BIGINT NOT NULL COMMENT '所属仓库ID（关联仓库表）',
  `area_id` BIGINT NOT NULL COMMENT '所属库区ID（关联库区表）',
  `shelf_id` BIGINT NOT NULL COMMENT '所属货架ID（关联货架表）',
  `remark` VARCHAR(500) COMMENT '备注',
  `length` DECIMAL(10,2) COMMENT '长度(米)',
  `width` DECIMAL(10,2) COMMENT '宽度(米)',
  `height` DECIMAL(10,2) COMMENT '高度(米)',
  `weight` DECIMAL(10,2) COMMENT '承重(吨)',
  `volume` DECIMAL(10,2) COMMENT '体积(立方米)',
  `status` TINYINT DEFAULT 1 COMMENT '货位状态(1启用/0停用)',
  `unloading_method` ENUM('人工卸货','叉车卸货','其它方式') COMMENT '卸货方式',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货位管理表';

-- 托盘管理表
CREATE TABLE `tray` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `code` VARCHAR(255) NOT NULL COMMENT '托盘编码',
  `warehouse_id` BIGINT NOT NULL COMMENT '所属仓库ID（关联仓库表）',
  `area_id` BIGINT NOT NULL COMMENT '所属库区ID（关联库区表）',
  `name` VARCHAR(255) NOT NULL COMMENT '托盘名称',
  `type` VARCHAR(255) COMMENT '托盘类型',
  `capacity` DECIMAL(16,4) COMMENT '托盘容量(吨)',
  `barcode` VARCHAR(255) COMMENT '条码号',
  `status` TINYINT DEFAULT 1 COMMENT '状态(1启用/0停用)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='托盘管理表';

-- 入库单主表
CREATE TABLE `inbound_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_code` VARCHAR(255) NOT NULL COMMENT '入库单号',
  `expected_arrival_date` DATETIME(6) COMMENT '预计到货日期',
  `customer_id` BIGINT COMMENT '客户ID（关联客户表）',
  `customer_order_code` VARCHAR(255) COMMENT '客户订单号',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `inbound_type` VARCHAR(255) COMMENT '入库类型（关联单据类型表）',
  `unloading_method` VARCHAR(255) COMMENT '卸货方式',
  `unloading_cost` DECIMAL(16,4) COMMENT '卸货费用',
  `inbound_operator` VARCHAR(255) COMMENT '入库员',
  `audit_operator` VARCHAR(255) COMMENT '审核人',
  `audit_time` DATETIME(6) COMMENT '审核时间',
  `remark` VARCHAR(500) COMMENT '备注',
  `order_status` ENUM('草稿','已提交','已作废') DEFAULT '草稿' COMMENT '入库单状态',
  `receipt_status` ENUM('待收货','全部收货','部份收货') DEFAULT '待收货' COMMENT '收货状态',
  `shelving_status` ENUM('待上架','已上架','部份上架') DEFAULT '待上架' COMMENT '上架状态',
  `total_amount` DECIMAL(16,4) COMMENT '总金额(含税额)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_code` (`order_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库单主表';

-- 入库单明细表
CREATE TABLE `inbound_order_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` BIGINT NOT NULL COMMENT '入库单ID（关联inbound_order表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格',
  `unit` VARCHAR(255) COMMENT '单位',
  `pending_quantity` INT COMMENT '待入库数量',
  `received_quantity` INT COMMENT '收货数量',
  `shelved_quantity` INT COMMENT '上架数量',
  `batch_number` VARCHAR(255) COMMENT '批次号',
  `production_date` DATE COMMENT '生产日期',
  `inbound_price` DECIMAL(16,4) COMMENT '入库价格',
  `inbound_amount` DECIMAL(16,4) COMMENT '入库金额(含税额)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库单明细表';

-- 收货登记单主表
CREATE TABLE `receipt_registration` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `receipt_code` VARCHAR(255) NOT NULL COMMENT '收货单号',
  `receipt_date` DATETIME(6) COMMENT '收货日期',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `pending_quantity` INT COMMENT '待收货品总量',
  `received_quantity` INT COMMENT '实收货品总量',
  `receipt_operator` VARCHAR(255) COMMENT '收货员',
  `audit_operator` VARCHAR(255) COMMENT '审核人',
  `audit_time` DATETIME(6) COMMENT '审核时间',
  `remark` VARCHAR(500) COMMENT '备注（非必填）',
  `registration_status` ENUM('草稿','已提交','已完成') DEFAULT '草稿' COMMENT '收货登记单状态',
  `receipt_status` ENUM('待收货','全部收货','部份收货') DEFAULT '待收货' COMMENT '收货状态',
  `shelving_status` ENUM('待上架','已上架','部份上架') DEFAULT '待上架' COMMENT '上架状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_receipt_code` (`receipt_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货登记单主表';

-- 收货登记单明细表
CREATE TABLE `receipt_registration_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `receipt_id` BIGINT NOT NULL COMMENT '收货登记单ID（关联主表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `inbound_order_code` VARCHAR(255) NOT NULL COMMENT '入库单号（关联入库单表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `pending_quantity` INT COMMENT '待入数量',
  `received_quantity` INT COMMENT '实收数量',
  `shelved_quantity` INT COMMENT '上架数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `tray_code` VARCHAR(255) COMMENT '托盘号（非必填）',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表，非必填）',
  `area_id` BIGINT COMMENT '库区ID（关联库区表，非必填）',
  `location_id` BIGINT COMMENT '库位ID（关联货位表，非必填）',
  `difference_remark` VARCHAR(500) COMMENT '差异备注（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货登记单明细表';

-- 收货明细表
CREATE TABLE `receipt_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `receipt_code` VARCHAR(255) NOT NULL COMMENT '收货单号（关联收货登记单主表）',
  `inbound_order_code` VARCHAR(255) NOT NULL COMMENT '入库单号（关联入库单表）',
  `customer_name` VARCHAR(255) COMMENT '客户名称',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `pending_quantity` INT COMMENT '待入库数量',
  `received_quantity` INT COMMENT '验收数量',
  `shelved_quantity` INT COMMENT '上架数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `tray_code` VARCHAR(255) COMMENT '托盘号（非必填）',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `area_id` BIGINT COMMENT '库区ID（关联库区表）',
  `location_id` BIGINT COMMENT '库位ID（关联货位表）',
  `difference_remark` VARCHAR(500) COMMENT '差异备注（非必填）',
  `receipt_operator` VARCHAR(255) COMMENT '收货员',
  `audit_operator` VARCHAR(255) COMMENT '审核人',
  `audit_time` DATETIME(6) COMMENT '审核时间',
  `remark` VARCHAR(500) COMMENT '备注（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货明细表';

-- 上架管理单主表
CREATE TABLE `shelving_management` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `shelving_code` VARCHAR(255) NOT NULL COMMENT '上架单号',
  `shelving_date` DATETIME(6) COMMENT '上架日期',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `area_id` BIGINT COMMENT '库区ID（关联库区表）',
  `location_id` BIGINT COMMENT '库位ID（关联货位表）',
  `shelving_operator` VARCHAR(255) COMMENT '上架员',
  `shelving_time` DATETIME(6) COMMENT '上架时间',
  `remark` VARCHAR(500) COMMENT '备注（非必填）',
  `shelving_order_status` ENUM('草稿','已提交') DEFAULT '草稿' COMMENT '上架单状态',
  `shelving_status` ENUM('待上架','已上架','部份上架') DEFAULT '待上架' COMMENT '上架状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shelving_code` (`shelving_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上架管理单主表';

-- 上架管理单明细表
CREATE TABLE `shelving_management_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `shelving_id` BIGINT NOT NULL COMMENT '上架单ID（关联主表）',
  `receipt_code` VARCHAR(255) NOT NULL COMMENT '收货登记单号（关联收货登记单主表）',
  `inbound_order_code` VARCHAR(255) NOT NULL COMMENT '入库订单号（关联入库单表）',
  `customer_name` VARCHAR(255) COMMENT '客户名称',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `received_quantity` INT COMMENT '收货数量',
  `shelved_quantity` INT COMMENT '上架数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上架管理单明细表';

-- 上架明细管理表
CREATE TABLE `shelving_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `shelving_code` VARCHAR(255) NOT NULL COMMENT '上架单号（关联上架管理单主表）',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `area_id` BIGINT COMMENT '库区ID（关联库区表）',
  `location_id` BIGINT COMMENT '库位ID（关联货位表）',
  `shelving_operator` VARCHAR(255) COMMENT '上架员',
  `shelving_time` DATETIME(6) COMMENT '上架时间',
  `remark` VARCHAR(500) COMMENT '备注（非必填）',
  `receipt_code` VARCHAR(255) NOT NULL COMMENT '收货登记单号（关联收货登记单主表）',
  `inbound_order_code` VARCHAR(255) NOT NULL COMMENT '入库订单号（关联入库单表）',
  `customer_name` VARCHAR(255) COMMENT '客户名称',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `received_quantity` INT COMMENT '收货数量',
  `shelved_quantity` INT COMMENT '上架数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shelving_code_product` (`shelving_code`,`product_code`)
)
  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上架明细管理表';

-- 费用明细表
CREATE TABLE `expense_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `expense_code` VARCHAR(255) NOT NULL COMMENT '费用单号（系统自动生成）',
  `customer_name` VARCHAR(255) NOT NULL COMMENT '客户名称',
  `expense_type` VARCHAR(255) NOT NULL COMMENT '费用类型',
  `amount_without_tax` DECIMAL(16,4) NOT NULL COMMENT '费用金额（不含税）',
  `amount_with_tax` DECIMAL(16,4) NOT NULL COMMENT '费用金额（含税）',
  `expense_date` DATETIME(6) NOT NULL COMMENT '费用日期',
  `expense_status` VARCHAR(50) NOT NULL COMMENT '费用状态',
  `description` VARCHAR(255) COMMENT '费用说明',
  `attachment` VARCHAR(255) COMMENT '费用附件（存储文件路径）',
  `settlement_status` ENUM('已结','部份已结','未结','作废') DEFAULT '未结' COMMENT '费用结算状态',
  `creator` VARCHAR(255) NOT NULL COMMENT '填写人',
  `create_time` DATETIME(6) COMMENT '填写时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_expense_code` (`expense_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='费用明细表';

-- 收款管理表
CREATE TABLE `receipt_management` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `receipt_code` VARCHAR(255) NOT NULL COMMENT '收款单号（系统自动生成）',
  `customer_name` VARCHAR(255) NOT NULL COMMENT '客户名称',
  `receipt_type` ENUM('现金','网银','支付宝','微信') NOT NULL COMMENT '收款类型',
  `receipt_account` VARCHAR(255) COMMENT '收款账号（非必填）',
  `amount` DECIMAL(16,4) NOT NULL COMMENT '收款金额',
  `receipt_date` DATETIME(6) NOT NULL COMMENT '收款日期',
  `description` VARCHAR(255) COMMENT '收款说明（非必填）',
  `attachment` VARCHAR(255) COMMENT '收款附件（存储文件路径，非必填）',
  `status` ENUM('已收','未收','作废') DEFAULT '未收' COMMENT '收款状态',
  `creator` VARCHAR(255) NOT NULL COMMENT '收款人',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_receipt_code` (`receipt_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收款管理表';

-- 付款管理表
CREATE TABLE `payment_management` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `payment_code` VARCHAR(255) NOT NULL COMMENT '付款单号（系统自动生成）',
  `supplier_name` VARCHAR(255) NOT NULL COMMENT '供应商名称',
  `payment_type` ENUM('现金','网银','支付宝','微信') NOT NULL COMMENT '付款类型',
  `payment_account` VARCHAR(255) COMMENT '付款账号（非必填）',
  `amount` DECIMAL(16,4) NOT NULL COMMENT '付款金额',
  `payment_date` DATETIME(6) NOT NULL COMMENT '付款日期',
  `description` VARCHAR(255) COMMENT '付款说明（非必填）',
  `attachment` VARCHAR(255) COMMENT '付款附件（存储文件路径，非必填）',
  `status` ENUM('已付','未付','作废') DEFAULT '未付' COMMENT '付款状态',
  `payer` VARCHAR(255) NOT NULL COMMENT '付款人',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_code` (`payment_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付款管理表';

-- 行业信息管理表
CREATE TABLE `industry_info` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `industry_code` VARCHAR(255) NOT NULL COMMENT '行业编码（唯一键）',
  `industry_name` VARCHAR(255) NOT NULL COMMENT '行业名称',
  `description` VARCHAR(255) COMMENT '行业描述（非必填）',
  `status` ENUM('启用','禁用') DEFAULT '启用' COMMENT '行业状态',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  `is_deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_industry_code` (`industry_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='行业信息管理表';

-- 入库异常处理单主表
CREATE TABLE `inbound_exception` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `related_order_code` VARCHAR(255) NOT NULL COMMENT '关联单据号（收货登记单号/上架单号）',
  `exception_type` ENUM('收货异常','上架异常') COMMENT '异常类型',
  `solution` VARCHAR(500) COMMENT '处理方案（非必填）',
  `result` VARCHAR(500) COMMENT '处理结果（非必填）',
  `status` ENUM('待处理','已处理') DEFAULT '待处理' COMMENT '状态',
  `handler` VARCHAR(255) COMMENT '处理人（非必填）',
  `handle_time` DATETIME(6) COMMENT '处理时间（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库异常处理单主表';

-- 入库异常处理单明细表
CREATE TABLE `inbound_exception_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `exception_id` BIGINT NOT NULL COMMENT '异常处理单ID（关联主表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `pending_quantity` INT COMMENT '待入库数量',
  `received_quantity` INT COMMENT '收货数量',
  `shelved_quantity` INT COMMENT '上架数量',
  `difference_quantity` INT COMMENT '差异数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `inbound_price` DECIMAL(16,4) COMMENT '入库价格',
  `inbound_amount` DECIMAL(16,4) COMMENT '入库金额(含税额)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库异常处理单明细表';

-- 出库订单主表
CREATE TABLE `outbound_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `outbound_code` VARCHAR(255) NOT NULL COMMENT '出库单号',
  `required_delivery_date` DATETIME(6) COMMENT '要求交货日期',
  `outbound_type` ENUM('销售出库','调拨出库','其他出库') COMMENT '出库类型',
  `customer_id` BIGINT COMMENT '客户ID（关联客户表）',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `total_pending` INT COMMENT '出库总量',
  `total_received` INT COMMENT '实质出库总量',
  `consignee` VARCHAR(255) COMMENT '收货人',
  `consignee_phone` VARCHAR(20) COMMENT '收货人电话',
  `order_status` ENUM('草稿','已提交','已完成') DEFAULT '草稿' COMMENT '出库单状态',
  `picking_status` ENUM('待拣货','已拣货','部份拣货') DEFAULT '待拣货' COMMENT '拣货状态',
  `sorting_status` ENUM('待分拣','已完成') DEFAULT '待分拣' COMMENT '分拣状态',
  `carrier` VARCHAR(255) COMMENT '承运商（非必填）',
  `freight_cost` DECIMAL(16,4) COMMENT '运费（非必填）',
  `delivery_method` VARCHAR(255) COMMENT '配送方式（非必填）',
  `license_plate` VARCHAR(50) COMMENT '车牌号（非必填）',
  `logistics_number` VARCHAR(255) COMMENT '物流单号（非必填）',
  `outbound_operator` VARCHAR(255) COMMENT '出库员',
  `audit_operator` VARCHAR(255) COMMENT '审核人',
  `audit_time` DATETIME(6) COMMENT '审核时间',
  `remark` VARCHAR(500) COMMENT '备注（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_outbound_code` (`outbound_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库订单主表';

-- 出库订单明细表
CREATE TABLE `outbound_order_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `outbound_id` BIGINT NOT NULL COMMENT '出库单ID（关联主表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `pending_quantity` INT COMMENT '待出库数量',
  `received_quantity` INT COMMENT '已出库数量',
  `difference_quantity` INT COMMENT '差异数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `outbound_price` DECIMAL(16,4) COMMENT '出库价格',
  `outbound_amount` DECIMAL(16,4) COMMENT '出库金额(含税额)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库订单明细表';

-- 波次单主表
CREATE TABLE `wave_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `wave_code` VARCHAR(255) NOT NULL COMMENT '波次单号',
  `wave_type` ENUM('普通波次','紧急波次') COMMENT '波次类型',
  `order_count` INT COMMENT '订单数量',
  `product_count` INT COMMENT '货品数量',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `picker` VARCHAR(255) COMMENT '拣货员',
  `picking_status` ENUM('待拣货','拣货中','已完成') DEFAULT '待拣货' COMMENT '拣货状态',
  `priority` INT COMMENT '优先级',
  `wave_status` ENUM('正常','作废') DEFAULT '正常' COMMENT '波次单状态',
  `picking_time` DATETIME(6) COMMENT '拣货时间',
  `remark` VARCHAR(500) COMMENT '备注（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_wave_code` (`wave_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='波次单主表';

-- 波次单明细表
CREATE TABLE `wave_order_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `wave_id` BIGINT NOT NULL COMMENT '波次单ID（关联主表）',
  `outbound_code` VARCHAR(255) NOT NULL COMMENT '出库单号（关联出库订单主表）',
  `customer_name` VARCHAR(255) COMMENT '客户名称',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `pending_picking` INT COMMENT '待拣货数量',
  `picked_quantity` INT COMMENT '已拣货数量',
  `difference_quantity` INT COMMENT '差异数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `area_id` BIGINT COMMENT '库区ID（关联库区表）',
  `location_id` BIGINT COMMENT '库位ID（关联货位表）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='波次单明细表';

-- 总拣单主表
CREATE TABLE `total_picking_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `picking_order_code` VARCHAR(255) NOT NULL COMMENT '拣货单号',
  `wave_code` VARCHAR(255) NOT NULL COMMENT '波次单编号（关联波次单主表）',
  `total_pending` INT COMMENT '待拣货总数',
  `total_picked` INT COMMENT '已拣货总数',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `picker` VARCHAR(255) COMMENT '拣货员',
  `tray_code` VARCHAR(255) COMMENT '托盘号（非必填）',
  `container_type` VARCHAR(255) COMMENT '拣货容器（非必填）',
  `container_code` VARCHAR(255) COMMENT '拣货容器号（非必填）',
  `status` ENUM('待拣货','已完成') DEFAULT '待拣货' COMMENT '状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_picking_order_code` (`picking_order_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='总拣单主表';

-- 总拣单明细表
CREATE TABLE `total_picking_order_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `picking_order_id` BIGINT NOT NULL COMMENT '拣货单ID（关联主表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `pending_quantity` INT COMMENT '待拣货数量',
  `picked_quantity` INT COMMENT '已拣货数量',
  `difference_quantity` INT COMMENT '差异数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `area_id` BIGINT COMMENT '拣货库区ID（关联库区表，非必填）',
  `location_id` BIGINT COMMENT '拣货库位ID（关联货位表，非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='总拣单明细表';

-- 分拣任务单主表
CREATE TABLE `sorting_task_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `sorting_order_code` VARCHAR(255) NOT NULL COMMENT '分拣单号',
  `outbound_order_code` VARCHAR(255) NOT NULL COMMENT '出库订单号（关联出库订单主表）',
  `customer_name` VARCHAR(255) COMMENT '客户名称',
  `total_product_quantity` INT COMMENT '商品总数量',
  `warehouse_id` BIGINT COMMENT '仓库ID（关联仓库表）',
  `sorter` VARCHAR(255) COMMENT '分拣员',
  `container_type` VARCHAR(255) COMMENT '分拣容器（非必填）',
  `container_code` VARCHAR(255) COMMENT '分拣容器号（非必填）',
  `status` ENUM('待分拣','已完成') DEFAULT '待分拣' COMMENT '状态',
  `sorting_time` DATETIME(6) COMMENT '分拣时间',
  `remark` VARCHAR(255) COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sorting_order_code` (`sorting_order_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分拣任务单主表';

-- 分拣任务单明细表
CREATE TABLE `sorting_task_order_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `sorting_order_id` BIGINT NOT NULL COMMENT '分拣任务单ID（关联主表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `unit` VARCHAR(255) COMMENT '单位（非必填）',
  `pending_sorting_quantity` INT COMMENT '待分拣数量',
  `sorted_quantity` INT COMMENT '已分拣数量',
  `difference_quantity` INT COMMENT '差异数量',
  `batch_number` VARCHAR(255) COMMENT '批次号（非必填）',
  `production_date` DATE COMMENT '生产日期（非必填）',
  `picking_container_type` VARCHAR(255) COMMENT '拣货容器',
  `picking_container_code` VARCHAR(255) COMMENT '拣货容器号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分拣任务单明细表';

-- 盘点计划主表
CREATE TABLE `inventory_plan` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `inventory_plan_code` VARCHAR(255) NOT NULL COMMENT '盘点计划单号',
  `plan_name` VARCHAR(255) NOT NULL COMMENT '计划名称',
  `inventory_type` ENUM('全盘','抽盘') NOT NULL COMMENT '盘点类型',
  `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID（关联仓库表）',
  `inventory_date` DATE NOT NULL COMMENT '盘点日期',
  `status` ENUM('草稿','计划','盘点中','盘点完成','已审核','已完成') DEFAULT '草稿' COMMENT '状态',
  `creator` VARCHAR(255) NOT NULL COMMENT '创建人', 
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inventory_plan_code` (`inventory_plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点计划主表';

-- 盘点计划明细表
CREATE TABLE `inventory_plan_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `inventory_plan_id` BIGINT NOT NULL COMMENT '盘点计划ID（关联主表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格（非必填）',
  `book_quantity` INT NOT NULL COMMENT '账面数量',
  `actual_quantity` INT COMMENT '实盘数量（非必填）',
  `difference_quantity` INT COMMENT '差异数量（非必填）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点计划明细表';

-- 库存管理表
CREATE TABLE `inventory_management` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `inventory_code` VARCHAR(255) NOT NULL COMMENT '库存编码',
  `warehouse_code` VARCHAR(255) NOT NULL COMMENT '仓库编码（关联仓库表）',
  `warehouse_name` VARCHAR(255) NOT NULL COMMENT '仓库名称',
  `area_code` VARCHAR(255) NOT NULL COMMENT '库区编码（关联库区表）',
  `area_name` VARCHAR(255) NOT NULL COMMENT '库区名称',
  `location_code` VARCHAR(255) NOT NULL COMMENT '库位编码（关联库位表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '商品规格',
  `unit` VARCHAR(255) COMMENT '计量单位',
  `batch_number` VARCHAR(255) COMMENT '批次号',
  `production_date` DATE COMMENT '生产日期',
  `quantity` INT NOT NULL COMMENT '库存数量',
  `status` VARCHAR(50) NOT NULL COMMENT '库存状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inventory_code` (`inventory_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存管理表';

-- 货品转移主表
CREATE TABLE `goods_transfer` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `transfer_code` VARCHAR(255) NOT NULL COMMENT '转移单号',
  `transfer_date` DATETIME(6) NOT NULL COMMENT '转移日期',
  `total_quantity` INT NOT NULL COMMENT '转移数量',
  `status` ENUM('草稿','完成','作废') DEFAULT '草稿' COMMENT '状态',
  `transfer_user` VARCHAR(255) NOT NULL COMMENT '转移人员',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transfer_code` (`transfer_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货品转移主表';

-- 货品转移明细表
CREATE TABLE `goods_transfer_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `transfer_id` BIGINT NOT NULL COMMENT '转移单ID（关联主表）',
  `product_code` VARCHAR(255) NOT NULL COMMENT '商品编码（关联商品表）',
  `product_name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `specification` VARCHAR(255) COMMENT '规格',
  `unit` VARCHAR(255) COMMENT '单位',
  `quantity` INT NOT NULL COMMENT '转移数量',
  `from_warehouse` VARCHAR(255) NOT NULL COMMENT '转出仓库（关联仓库表）',
  `from_location` VARCHAR(255) NOT NULL COMMENT '转出储位（关联库位表）',
  `to_warehouse` VARCHAR(255) NOT NULL COMMENT '转入仓库（关联仓库表）',
  `to_location` VARCHAR(255) NOT NULL COMMENT '转入储位（关联库位表）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` TINYINT DEFAULT 0 COMMENT '软删除标记',
  `create_time` DATETIME(6) COMMENT '创建时间',
  `update_time` DATETIME(6) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货品转移明细表';

