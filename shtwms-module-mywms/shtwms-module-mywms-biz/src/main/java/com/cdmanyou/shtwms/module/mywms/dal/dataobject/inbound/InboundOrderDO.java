package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 入库单主 DO
 *
 * <AUTHOR>
 */
@TableName("inbound_order")
@KeySequence("inbound_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InboundOrderDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 入库单号
     */
    private String orderCode;
    /**
     * 入库时间
     */
    private LocalDateTime inboundDate;
    /**
     * 货主ID（关联货主表）
     */
    private Long producerId;
    /**
     * 货主单号
     */
    private String producerOrderCode;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 入库类型（0采购入库，1退货入库，2其他入库）
     */
    private Integer inboundType;
    /**
     * 卸货方式（0叉车卸货，1其他卸货方式）
     */
    private Integer unloadingMethod;
    /**
     * 卸货重量(公斤)
     */
    private BigDecimal unloadingWeight;
    /**
     * 卸货费用
     */
    private BigDecimal unloadingCost;
    /**
     * 入库单状态（0草稿,1已提交,2作废）
     */
    private Integer inboundOrderStatus;
    /**
     * 总金额(含税额)
     */
    private BigDecimal totalAmount;
    /**
     * 提交人ID
     */
    private Long submitOperator;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 到货登记时间
     */
    private LocalDateTime acceptanceCompleteTime;
    /**
     * 验收完成时间
     */
    private LocalDateTime receiptCompleteTime;
    /**
     * 上架完成时间
     */
    private LocalDateTime shelvingCompleteTime;
    /**
     * 创建方式(0录入，1导入)
     */
    private Integer createType;
    /**
     * 备注
     */
    private String remark;

}