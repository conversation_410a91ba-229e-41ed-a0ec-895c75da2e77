package com.cdmanyou.shtwms.module.mywms.service.base_info;

import javax.validation.*;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WarehouseLocationDO;

/**
 * 货位管理 Service 接口
 *
 * <AUTHOR>
 */
public interface WareHouseLocationService {

    /**
     * 创建货位管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWareHouseLocation(@Valid WarehouseLocationSaveReqVO createReqVO);

    /**
     * 更新货位管理
     *
     * @param updateReqVO 更新信息
     */
    void updateWareHouseLocation(@Valid WarehouseLocationSaveReqVO updateReqVO);

    /**
     * 删除货位管理
     *
     * @param id 编号
     */
    void deleteWareHouseLocation(Long id);

    /**
     * 获得货位管理
     *
     * @param id 编号
     * @return 货位管理
     */
    WarehouseLocationDO getWareHouseLocation(Long id);

    /**
     * 获得货位管理分页
     *
     * @param pageReqVO 分页查询
     * @return 货位管理分页
     */
    PageResult<WarehouseLocationDO> getWareHouseLocationPage(WarehouseLocationPageReqVO pageReqVO);

    void updateWareHouseLocationStatus(Long id, Integer status);

}