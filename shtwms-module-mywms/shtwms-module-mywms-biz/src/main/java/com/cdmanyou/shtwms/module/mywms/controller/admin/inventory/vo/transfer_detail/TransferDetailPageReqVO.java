package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 货品转移明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransferDetailPageReqVO extends PageParam {

    @Schema(description = "转移单ID（关联主表）", example = "25002")
    private Long transferId;

    @Schema(description = "商品ID", example = "14627")
    private Long productId;

    @Schema(description = "商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", example = "张三")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "生产日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] productionDate;

    @Schema(description = "转移数量")
    private Integer quantity;

    @Schema(description = "转出库位ID（关联库位表）", example = "27693")
    private Long fromWareHouseLocationId;

    @Schema(description = "转出库位ID（关联库位表）", example = "11086")
    private Long toWareHouseLocationId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}