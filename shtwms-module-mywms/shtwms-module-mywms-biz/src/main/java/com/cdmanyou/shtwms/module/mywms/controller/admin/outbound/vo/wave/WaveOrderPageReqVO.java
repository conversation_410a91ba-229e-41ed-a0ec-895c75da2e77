package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 波次单主分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WaveOrderPageReqVO extends PageParam {

    @Schema(description = "波次单号")
    private String waveCode;

    @Schema(description = "波次类型", example = "2")
    private String waveType;

    @Schema(description = "仓库ID（关联仓库表）", example = "519")
    private Long warehouseId;

    @Schema(description = "拣货员")
    private String picker;

    @Schema(description = "拣货状态", example = "1")
    private String pickingStatus;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "波次单状态", example = "2")
    private String waveStatus;

    @Schema(description = "拣货时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] pickingTime;

    @Schema(description = "备注（非必填）", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}