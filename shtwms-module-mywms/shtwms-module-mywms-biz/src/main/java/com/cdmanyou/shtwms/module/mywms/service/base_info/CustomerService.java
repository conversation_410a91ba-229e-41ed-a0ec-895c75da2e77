package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.CustomerDO;

import javax.validation.Valid;

/**
 * 客户管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CustomerService {

    /**
     * 创建客户管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCustomer(@Valid CustomerSaveReqVO createReqVO);

    /**
     * 更新客户管理
     *
     * @param updateReqVO 更新信息
     */
    void updateCustomer(@Valid CustomerSaveReqVO updateReqVO);

    /**
     * 删除客户管理
     *
     * @param id 编号
     */
    void deleteCustomer(Long id);

    /**
     * 获得客户管理
     *
     * @param id 编号
     * @return 客户管理
     */
    CustomerDO getCustomer(Long id);

    /**
     * 获得客户管理分页
     *
     * @param pageReqVO 分页查询
     * @return 客户管理分页
     */
    PageResult<CustomerDO> getCustomerPage(CustomerPageReqVO pageReqVO);

    void enableCustomer(Long id);
    void disableCustomer(Long id);
}