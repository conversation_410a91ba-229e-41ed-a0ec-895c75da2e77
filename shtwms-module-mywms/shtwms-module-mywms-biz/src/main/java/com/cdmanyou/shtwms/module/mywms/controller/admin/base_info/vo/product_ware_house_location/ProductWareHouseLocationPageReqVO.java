package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商品货位关联表-记录货位上的商品数量分页 Request VO")
@Data
public class ProductWareHouseLocationPageReqVO extends PageParam {

    @Schema(description = "货品名称")
    private String productName;

    @Schema(description = "所属仓类别（0成品，1半成品，2原料）")
    private Integer houseType;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "货品批次号")
    private String productBatchNumber;

}