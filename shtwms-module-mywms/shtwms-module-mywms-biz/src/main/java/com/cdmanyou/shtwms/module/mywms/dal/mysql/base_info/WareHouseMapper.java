package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHousePageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 仓库管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WareHouseMapper extends BaseMapperX<WareHouseDO> {

    default boolean existsByCode(String code) {
        return selectCount(new LambdaQueryWrapper<WareHouseDO>()
                .eq(WareHouseDO::getCode, code)) > 0;
    }

    // 检查名称是否已存在
    default boolean existsByName(String name) {
        return selectCount(new LambdaQueryWrapper<WareHouseDO>()
                .eq(WareHouseDO::getName, name)) > 0;
    }

    // 更新时检查编码唯一性（排除自身）
    default boolean existsByCodeAndIdNot(String code, Long excludeId) {
        return selectCount(new LambdaQueryWrapper<WareHouseDO>()
                .eq(WareHouseDO::getCode, code)
                .ne(WareHouseDO::getId, excludeId)) > 0;
    }

    // 更新时检查名称唯一性（排除自身）
    default boolean existsByNameAndIdNot(String name, Long excludeId) {
        return selectCount(new LambdaQueryWrapper<WareHouseDO>()
                .eq(WareHouseDO::getName, name)
                .ne(WareHouseDO::getId, excludeId)) > 0;
    }
    default PageResult<WareHouseDO> selectPage(WareHousePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WareHouseDO>()
                .eqIfPresent(WareHouseDO::getCode, reqVO.getCode())
                .likeIfPresent(WareHouseDO::getName, reqVO.getName())
                .eqIfPresent(WareHouseDO::getType, reqVO.getType())
                .betweenIfPresent(WareHouseDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WareHouseDO::getId));
    }

    @Select("select distinct wl.warehouse_id from shtwms.warehouse_location wl " +
            "        where wl.id in (\n" +
            "        select pwl.warehouse_location_id from shtwms.producer_warehouse_location pwl" +
            " where pwl.production_date = #{productDate} and pwl.product_id = #{productId}  " +
            "        )")
    List<Long> selectDataByProductIdAndDate(LocalDate productDate, String productId);
}