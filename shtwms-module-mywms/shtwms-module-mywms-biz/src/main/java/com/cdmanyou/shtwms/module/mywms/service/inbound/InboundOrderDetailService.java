package com.cdmanyou.shtwms.module.mywms.service.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 入库单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface InboundOrderDetailService {

    /**
     * 创建入库单商品明细
     *
     * @param orderId 入库单ID
     * @param list 入库单商品明细
     */
    void createInboundOrderDetailList(Long orderId, List<InboundOrderDetailInputVO> list);


    /**
     * 修改入库单商品明细
     *
     * @param orderId 入库单ID
     * @param list 入库单商品明细
     */
    void updateInboundOrderDetailList(Long orderId, List<InboundOrderDetailInputVO> list);

}