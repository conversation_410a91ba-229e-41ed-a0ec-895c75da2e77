package com.cdmanyou.shtwms.module.mywms.service.industryinfo;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.industryinfo.vo.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.industryinfo.IndustryInfoDO;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;

import com.cdmanyou.shtwms.module.mywms.dal.mysql.industryinfo.IndustryInfoMapper;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 行业信息管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndustryInfoServiceImpl implements IndustryInfoService {

    @Resource
    private IndustryInfoMapper industryInfoMapper;

    @Override
    public Long createIndustryInfo(IndustryInfoSaveReqVO createReqVO) {
        // 插入
        IndustryInfoDO industryInfo = BeanUtils.toBean(createReqVO, IndustryInfoDO.class);
        industryInfoMapper.insert(industryInfo);
        // 返回
        return industryInfo.getId();
    }

    @Override
    public void updateIndustryInfo(IndustryInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateIndustryInfoExists(updateReqVO.getId());
        // 更新
        IndustryInfoDO updateObj = BeanUtils.toBean(updateReqVO, IndustryInfoDO.class);
        industryInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteIndustryInfo(Long id) {
        // 校验存在
        validateIndustryInfoExists(id);
        // 删除
        industryInfoMapper.deleteById(id);
    }

    private void validateIndustryInfoExists(Long id) {
        if (industryInfoMapper.selectById(id) == null) {
            throw exception(INDUSTRY_INFO_NOT_EXISTS);
        }
    }

    @Override
    public IndustryInfoDO getIndustryInfo(Long id) {
        return industryInfoMapper.selectById(id);
    }

    @Override
    public PageResult<IndustryInfoDO> getIndustryInfoPage(IndustryInfoPageReqVO pageReqVO) {
        return industryInfoMapper.selectPage(pageReqVO);
    }

}