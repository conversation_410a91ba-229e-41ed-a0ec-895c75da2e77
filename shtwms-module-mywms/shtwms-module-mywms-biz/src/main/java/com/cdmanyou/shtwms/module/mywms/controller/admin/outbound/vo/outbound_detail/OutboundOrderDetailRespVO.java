package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 出库订单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OutboundOrderDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27434")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "出库单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1523")
    @ExcelProperty("出库单ID（关联主表）")
    private Long outboundId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    @ExcelProperty("单位（非必填）")
    private String unit;

    @Schema(description = "待出库数量")
    @ExcelProperty("待出库数量")
    private Integer pendingQuantity;

    @Schema(description = "已出库数量")
    @ExcelProperty("已出库数量")
    private Integer receivedQuantity;

    @Schema(description = "差异数量")
    @ExcelProperty("差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    @ExcelProperty("批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    @ExcelProperty("生产日期（非必填）")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate productionDate;

    @Schema(description = "出库价格", example = "11861")
    @ExcelProperty("出库价格")
    private BigDecimal outboundPrice;

    @Schema(description = "出库金额(含税额)")
    @ExcelProperty("出库金额(含税额)")
    private BigDecimal outboundAmount;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "分拣完成时间")
    @ExcelProperty("分拣完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime sortingCompleteTime;

    @Schema(description = "是否越库(0否，1是)")
    @ExcelProperty("是否越库(0否，1是)")
    private Integer straddleWarehouse;

    @Schema(description = "出库体积")
    @ExcelProperty("出库体积")
    private Long volume;

    @Schema(description = "出库重量")
    @ExcelProperty("出库重量")
    private Long grossWeight;

    @Schema(description = "到期日期")
    @ExcelProperty("到期日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate expirationDate;

    @Schema(description = "商品id", example = "5589")
    @ExcelProperty("商品id")
    private Long productId;

    @Schema(description = "零单位数量", example = "10366")
    @ExcelProperty("零单位数量")
    private Integer simpleCount;

    @Schema(description = "货品批次号")
    @ExcelProperty("货品批次号")
    private String productBatchNumber;

    @Schema(description = "波次单锁定数量")
    @ExcelProperty("波次单锁定数量")
    private Integer waveLockQuantity;

    @Schema(description = "供应商id")
    @ExcelProperty("供应商id")
    private Integer supplierId;

    @Schema(description = "总拣状态")
    @ExcelProperty("总拣状态")
    private Integer pickingStatus;

    @Schema(description = "仓类型")
    @ExcelProperty("仓类型")
    private Integer houseType;

}