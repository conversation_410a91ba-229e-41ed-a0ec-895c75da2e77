package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategoryPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductCategoryDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品类别管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCategoryMapper extends BaseMapperX<ProductCategoryDO> {

    default PageResult<ProductCategoryDO> selectPage(ProductCategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductCategoryDO>()
                .eqIfPresent(ProductCategoryDO::getCode, reqVO.getCode())
                .likeIfPresent(ProductCategoryDO::getName, reqVO.getName())
                .eqIfPresent(ProductCategoryDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProductCategoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductCategoryDO::getId));
    }

}