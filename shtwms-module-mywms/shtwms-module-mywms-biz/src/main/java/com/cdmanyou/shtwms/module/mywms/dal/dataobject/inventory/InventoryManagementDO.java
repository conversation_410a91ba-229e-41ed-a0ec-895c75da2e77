package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 库存管理 DO
 *
 * <AUTHOR>
 */
@TableName("inventory_management")
@KeySequence("inventory_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryManagementDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 库存编码
     */
    private String inventoryCode;
    /**
     * 仓库编码（关联仓库表）
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 库区编码（关联库区表）
     */
    private String areaCode;
    /**
     * 库区名称
     */
    private String areaName;
    /**
     * 库位编码（关联库位表）
     */
    private String locationCode;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品规格
     */
    private String specification;
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 批次号
     */
    private String batchNumber;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 库存数量
     */
    private Integer quantity;
    /**
     * 库存状态
     */
    private String status;

}