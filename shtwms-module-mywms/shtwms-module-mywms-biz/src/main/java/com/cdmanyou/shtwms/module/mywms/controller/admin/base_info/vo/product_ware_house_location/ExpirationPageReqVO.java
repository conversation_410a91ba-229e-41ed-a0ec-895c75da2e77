package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;


import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.swing.*;


@Schema(description = "效期预警查询实体")
@Data
public class ExpirationPageReqVO extends PageParam {

    @Schema(description = "效期状态（0安全效期，1临期货品，2过期货品）")
    private Integer expirationStatus;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "生产日期搜索开始时间")
    private String productionSearchStartTime;

    @Schema(description = "生产日期搜索结束时间")
    private String productionSearchEndTime;

}
