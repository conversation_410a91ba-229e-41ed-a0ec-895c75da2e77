package com.cdmanyou.shtwms.module.mywms.controller.admin.industryinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 行业信息管理新增/修改 Request VO")
@Data
public class IndustryInfoSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12698")
    private Long id;

    @Schema(description = "行业编码（唯一键）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "行业编码（唯一键）不能为空")
    private String industryCode;

    @Schema(description = "行业名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "行业名称不能为空")
    private String industryName;

    @Schema(description = "行业描述（非必填）", example = "你猜")
    private String description;

    @Schema(description = "行业状态", example = "1")
    private String status;

    @Schema(description = "软删除标记")
    private Integer isDeleted;

}