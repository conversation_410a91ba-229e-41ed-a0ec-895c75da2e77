package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商品货位关联表-记录货位上的商品数量分页 Request VO")
@Data
public class ProductWareHouseLocationPageReqVO extends PageParam {

    @Schema(description = "货品名称")
    private String productName;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "货品批次号")
    private String productBatchNumber;

}