package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 上架管理单明细 Response VO")
@Data
public class ShelvingManagementDetailPageRespVO {

    @Schema(description = "上架单明细ID")
    private Long id;

    @Schema(description = "上架单号")
    private String shelvingCode;

    @Schema(description = "入库单号")
    private String inboundOrderCode;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "上架零散数量")
    private Integer shelvedScatteredQuantity;

    @Schema(description = "货位ID")
    private Long warehouseLocationId;

    @Schema(description = "货位编码")
    private String warehouseLocationCode;

    @Schema(description = "上架状态（0未上架，1已上架）")
    private Integer shelvingStatus;

    @Schema(description = "所属仓类别（0成品，1半成品，2原料）")
    private Integer houseType;
}