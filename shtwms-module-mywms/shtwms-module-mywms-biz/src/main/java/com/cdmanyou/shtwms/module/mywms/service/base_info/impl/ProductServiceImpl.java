package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.OutboundStatusReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.ProductSimpleVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutBoundProductDetail;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 商品管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductServiceImpl implements ProductService {

    @Resource
    private ProductMapper productMapper;

    @Resource
    private WareHouseMapper wareHouseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProduct(ProductSaveReqVO createReqVO) {
        // 自动生成商品编码: HP + 毫秒级时间戳
        String generatedCode = "HP" + System.currentTimeMillis();
        createReqVO.setCode(generatedCode); // 覆盖前端传入的编码

        // 唯一性校验
        validateProductCodeUnique(createReqVO.getCode(), null);

        // 插入数据
        ProductDO product = BeanUtils.toBean(createReqVO, ProductDO.class);
        productMapper.insert(product);
        return product.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务注解
    public void updateProduct(ProductSaveReqVO updateReqVO) {
        // 校验存在
        validateProductExists(updateReqVO.getId());
        // 新增唯一性校验(排除自身)
        validateProductCodeUnique(updateReqVO.getCode(), updateReqVO.getId());

        // 更新
        ProductDO updateObj = BeanUtils.toBean(updateReqVO, ProductDO.class);
        productMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务注解
    public void deleteProduct(Long id) {
        // 校验存在
        validateProductExists(id);
        // 删除
        productMapper.deleteById(id);
    }

    private void validateProductCodeUnique(String code, Long excludeId) {
        // 使用QueryWrapper构建动态查询
        QueryWrapper<ProductDO> wrapper = new QueryWrapper<>();
        wrapper.eq("code", code);
        ProductDO product = productMapper.selectOne(wrapper); // 使用MyBatis Plus通用方法

        if (product != null && (excludeId == null || !product.getId().equals(excludeId))) {
            throw exception(PRODUCT_CODE_DUPLICATE);
        }
    }

    private ProductDO validateProductExists(Long id) {
        ProductDO product = productMapper.selectById(id);
        if (product == null) {
            throw exception(PRODUCT_NOT_EXISTS);
        }
        return product;
    }

    @Override
    public ProductDO getProduct(Long id) {
        return productMapper.selectById(id);
    }

    @Override
    public PageResult<ProductDO> getProductPage(ProductPageReqVO pageReqVO) {
        return productMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductSimpleVO> getProductSimpleList(Long producerId) {
        List<ProductDO> productSimpleList = productMapper.getProductSimpleList(producerId);
        return BeanUtils.toBean(productSimpleList, ProductSimpleVO.class);
    }

    @Override
    public OutBoundProductDetail getInfoByProductOutboundInfo(String productId, Integer outboundNumber) {
        OutBoundProductDetail infoByProductOutboundInfo = productMapper.getInfoByProductOutboundInfo(productId, outboundNumber);
        if (Objects.isNull(infoByProductOutboundInfo)) {
            return null;
        }
        List<Long> wareHouseIds = wareHouseMapper.selectDataByProductIdAndDate(infoByProductOutboundInfo.getProductionDate(), productId);
        if (wareHouseIds.size() > 1) {
            infoByProductOutboundInfo.setStraddleWarehouse(1);
        } else {
            infoByProductOutboundInfo.setStraddleWarehouse(0);
        }
        infoByProductOutboundInfo.setWareHouseIds(wareHouseIds);
        return infoByProductOutboundInfo;
    }

    @Override
    public List<ProductDO> selectArrayByIds(Collection<Long> productIds) {
        return productMapper.selectByIds(productIds);
    }

    @Override
    public PageResult<ProductDO> getEnabledProductPage(ProductPageReqVO pageReqVO) {
        // 创建查询条件：状态为启用(1)
        LambdaQueryWrapperX<ProductDO> wrapper = new LambdaQueryWrapperX<ProductDO>()
                .eq(ProductDO::getStatus, 1); // 只查询启用状态的商品

        // 添加其他查询条件（可选）
        if (StringUtils.isNotBlank(pageReqVO.getCode())) {
            wrapper.like(ProductDO::getCode, pageReqVO.getCode());
        }
        if (StringUtils.isNotBlank(pageReqVO.getName())) {
            wrapper.like(ProductDO::getName, pageReqVO.getName());
        }
        if (pageReqVO.getCategoryId() != null) {
            wrapper.eq(ProductDO::getCategoryId, pageReqVO.getCategoryId());
        }
        if (pageReqVO.getProducerId() != null) {
            wrapper.eq(ProductDO::getProducerId, pageReqVO.getProducerId());
        }

        // 执行分页查询
        return productMapper.selectPage(pageReqVO, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableProduct(Long id) {
        // 校验商品存在性
        ProductDO product = validateProductExists(id);

        // 幂等性检查：已启用则抛出异常
        if (product.getStatus() == 1) {
            throw exception(PRODUCT_ALREADY_ENABLED);
        }

        // 轻量更新：只更新状态字段
        ProductDO updateObj = new ProductDO();
        updateObj.setId(id);
        updateObj.setStatus(1); // 1=启用
        productMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableProduct(Long id) {
        // 校验商品存在性
        ProductDO product = validateProductExists(id);

        // 幂等性检查：已停用则抛出异常
        if (product.getStatus() == 0) {
            throw exception(PRODUCT_ALREADY_DISABLED);
        }

        // 轻量更新：只更新状态字段
        ProductDO updateObj = new ProductDO();
        updateObj.setId(id);
        updateObj.setStatus(0); // 0=停用
        productMapper.updateById(updateObj);
    }

    // 重构商品存在性校验方法（复用）


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezeProduct(Long id) {
        // 校验商品存在性
        ProductDO product = validateProductExists(id);

        // 幂等性检查：已冻结则抛出异常
        if (product.getFreezeStatus() == 1) {
            throw exception(PRODUCT_ALREADY_FREEZED);
        }

        // 轻量更新：只更新冻结状态字段
        ProductDO updateObj = new ProductDO();
        updateObj.setId(id);
        updateObj.setFreezeStatus(1); // 1=冻结
        productMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfreezeProduct(Long id) {
        // 校验商品存在性
        ProductDO product = validateProductExists(id);

        // 幂等性检查：未冻结则抛出异常
        if (product.getFreezeStatus() == 0) {
            throw exception(PRODUCT_ALREADY_UNFREEZED);
        }

        // 轻量更新：只更新冻结状态字段
        ProductDO updateObj = new ProductDO();
        updateObj.setId(id);
        updateObj.setFreezeStatus(0); // 0=解冻
        productMapper.updateById(updateObj);
    }


}