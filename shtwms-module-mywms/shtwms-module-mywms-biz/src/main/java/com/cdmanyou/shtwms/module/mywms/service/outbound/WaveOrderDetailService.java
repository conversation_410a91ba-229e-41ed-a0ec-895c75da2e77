package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDetailDO;

import javax.validation.Valid;

/**
 * 波次单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface WaveOrderDetailService {

    /**
     * 创建波次单明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWaveOrderDetail(@Valid WaveOrderDetailSaveReqVO createReqVO);

    /**
     * 更新波次单明细
     *
     * @param updateReqVO 更新信息
     */
    void updateWaveOrderDetail(@Valid WaveOrderDetailSaveReqVO updateReqVO);

    /**
     * 删除波次单明细
     *
     * @param id 编号
     */
    void deleteWaveOrderDetail(Long id);

    /**
     * 获得波次单明细
     *
     * @param id 编号
     * @return 波次单明细
     */
    WaveOrderDetailDO getWaveOrderDetail(Long id);

    /**
     * 获得波次单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 波次单明细分页
     */
    PageResult<WaveOrderDetailDO> getWaveOrderDetailPage(WaveOrderDetailPageReqVO pageReqVO);

}