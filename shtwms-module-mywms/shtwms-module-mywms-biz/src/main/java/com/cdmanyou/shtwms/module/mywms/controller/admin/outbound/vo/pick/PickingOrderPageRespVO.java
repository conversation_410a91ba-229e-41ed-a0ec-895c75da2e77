package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 总拣单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PickingOrderPageRespVO {


    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5718")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "拣货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("拣货单号")
    private String pickingOrderCode;


    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private String status;


    @Schema(description = "出库方式")
    @ExcelProperty("出库方式")
    private Integer pickingType;


    @Schema(description = "出库数量")
    @ExcelProperty("出库数量")
    private Integer totalPending;

    @Schema(description = "出库重量")
    @ExcelProperty("出库重量")
    private Integer totalWeight;


    @Schema(description = "完成时间")
    @ExcelProperty("完成时间")
    private LocalDateTime pickingCompleteTime;


    @Schema(description = "拣货员")
    @ExcelProperty("拣货员")
    private String picker;

}