package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 出库订单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface OutboundOrderDetailService {

    /**
     * 创建出库订单明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOutboundOrderDetail(@Valid OutboundOrderDetailSaveReqVO createReqVO);

    /**
     * 更新出库订单明细
     *
     * @param updateReqVO 更新信息
     */
    void updateOutboundOrderDetail(@Valid OutboundOrderDetailSaveReqVO updateReqVO);

    /**
     * 删除出库订单明细
     *
     * @param id 编号
     */
    void deleteOutboundOrderDetail(Long id);

    /**
     * 获得出库订单明细
     *
     * @param id 编号
     * @return 出库订单明细
     */
    OutboundOrderDetailDO getOutboundOrderDetail(Long id);

    /**
     * 获得出库订单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 出库订单明细分页
     */
    PageResult<OutboundOrderDetailDO> getOutboundOrderDetailPage(OutboundOrderDetailPageReqVO pageReqVO);

    List<OutboundOrderDetailDO> getDetailListByOutboundIds(Collection<Long> outboundIds);

    void updateWaveLockQuantity(Long id, int lockNumber);
}