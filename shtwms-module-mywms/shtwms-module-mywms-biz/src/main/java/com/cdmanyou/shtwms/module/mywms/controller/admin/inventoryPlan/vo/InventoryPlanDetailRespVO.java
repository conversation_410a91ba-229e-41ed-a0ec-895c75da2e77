package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 盘点计划明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryPlanDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11316")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "盘点计划ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "8868")
    @ExcelProperty("盘点计划ID（关联主表）")
    private Long inventoryPlanId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格（非必填）")
    private String specification;

    @Schema(description = "账面数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("账面数量")
    private Integer bookQuantity;

    @Schema(description = "实盘数量（非必填）")
    @ExcelProperty("实盘数量（非必填）")
    private Integer actualQuantity;

    @Schema(description = "差异数量（非必填）")
    @ExcelProperty("差异数量（非必填）")
    private Integer differenceQuantity;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}