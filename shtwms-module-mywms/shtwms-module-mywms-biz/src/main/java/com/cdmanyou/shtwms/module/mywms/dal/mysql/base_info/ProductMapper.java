package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import java.time.LocalDate;
import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.ProductSimpleVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutBoundProductDetail;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 商品管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductMapper extends BaseMapperX<ProductDO> {

    default PageResult<ProductDO> selectPage(ProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductDO>()
                .eqIfPresent(ProductDO::getCode, reqVO.getCode())
                .eqIfPresent(ProductDO::getCustomerCode, reqVO.getCustomerCode())
                .likeIfPresent(ProductDO::getName, reqVO.getName())
                .eqIfPresent(ProductDO::getBrand, reqVO.getBrand())
                .eqIfPresent(ProductDO::getBarcode, reqVO.getBarcode())
                .eqIfPresent(ProductDO::getUnit, reqVO.getUnit())
                .eqIfPresent(ProductDO::getShelfLife, reqVO.getShelfLife())
                .eqIfPresent(ProductDO::getTemperatureZone, reqVO.getTemperatureZone())
                .eqIfPresent(ProductDO::getProducerId, reqVO.getProducerId())
                .eqIfPresent(ProductDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(ProductDO::getMainImage, reqVO.getMainImage())
                .eqIfPresent(ProductDO::getSubImages, reqVO.getSubImages())
                .eqIfPresent(ProductDO::getDescription, reqVO.getDescription())
                .eqIfPresent(ProductDO::getUsageInstruction, reqVO.getUsageInstruction())
                .eqIfPresent(ProductDO::getPackingInstruction, reqVO.getPackingInstruction())
                .eqIfPresent(ProductDO::getPrecautions, reqVO.getPrecautions())
                .eqIfPresent(ProductDO::getSafetyStock, reqVO.getSafetyStock())
                .eqIfPresent(ProductDO::getMaxStock, reqVO.getMaxStock())
                .eqIfPresent(ProductDO::getStockWarningThreshold, reqVO.getStockWarningThreshold())
                .eqIfPresent(ProductDO::getPalletLayerQty, reqVO.getPalletLayerQty())
                .eqIfPresent(ProductDO::getPalletLayerHeight, reqVO.getPalletLayerHeight())
                .eqIfPresent(ProductDO::getMasterMeasure, reqVO.getMasterMeasure())
                .eqIfPresent(ProductDO::getMeasureType, reqVO.getMeasureType())
                .eqIfPresent(ProductDO::getGrossWeight, reqVO.getGrossWeight())
                .eqIfPresent(ProductDO::getDeputyMeasure, reqVO.getDeputyMeasure())
                .eqIfPresent(ProductDO::getLength, reqVO.getLength())
                .eqIfPresent(ProductDO::getWide, reqVO.getWide())
                .eqIfPresent(ProductDO::getHigh, reqVO.getHigh())
                .eqIfPresent(ProductDO::getVolume, reqVO.getVolume())
                .eqIfPresent(ProductDO::getFreezeStatus, reqVO.getFreezeStatus())
                .eqIfPresent(ProductDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductDO::getLooseQuantity, reqVO.getLooseQuantity())
                .eqIfPresent(ProductDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProductDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductDO::getId));
    }

    default List<ProductDO> selectListByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return Collections.emptyList(); // 处理空参数
        }
        LambdaQueryWrapperX<ProductDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ProductDO::getCategoryId, categoryId); // 按类别ID过滤
        return selectList(wrapper);
    }

    default void deleteByCategoryId(Long categoryId) {

    }

    @Select("select * from product p where p.id in (select pwl.product_id from product_warehouse_location pwl where pwl.producer_id = #{producerId}) and p.status = 0")
    List<ProductDO> getProductSimpleList(@Param("producerId") Long producerId);

    OutBoundProductDetail getInfoByProductOutboundInfo(@Param("productId") String productId, @Param("outboundNumber") Integer outboundNumber,@Param("productionDate") LocalDate productionDate);

    @Select("   select pw.production_date from " +
            "   (select pwl.production_date,sum(pwl.available) as product_available from shtwms.product_warehouse_location pwl" +
            "   where pwl.product_id = #{productId}  group by pwl.production_date) as pw where pw. product_available > #{outboundNumber}")
    List<String> getProductionDateByProductOutboundInfo(@Param("productId") String productId,@Param("outboundNumber") Integer outboundNumber);
}