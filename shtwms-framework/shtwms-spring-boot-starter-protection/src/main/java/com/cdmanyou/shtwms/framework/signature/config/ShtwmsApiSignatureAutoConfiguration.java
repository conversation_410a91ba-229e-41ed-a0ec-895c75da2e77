package com.cdmanyou.shtwms.framework.signature.config;

import com.cdmanyou.shtwms.framework.redis.config.ShtwmsRedisAutoConfiguration;
import com.cdmanyou.shtwms.framework.signature.core.aop.ApiSignatureAspect;
import com.cdmanyou.shtwms.framework.signature.core.redis.ApiSignatureRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * HTTP API 签名的自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(after = ShtwmsRedisAutoConfiguration.class)
public class ShtwmsApiSignatureAutoConfiguration {

    @Bean
    public ApiSignatureAspect signatureAspect(ApiSignatureRedisDAO signatureRedisDAO) {
        return new ApiSignatureAspect(signatureRedisDAO);
    }

    @Bean
    public ApiSignatureRedisDAO signatureRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new ApiSignatureRedisDAO(stringRedisTemplate);
    }

}
