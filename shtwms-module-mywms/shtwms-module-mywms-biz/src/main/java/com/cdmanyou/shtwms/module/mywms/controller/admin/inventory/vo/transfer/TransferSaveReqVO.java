package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 货品转移主新增/修改 Request VO")
@Data
public class TransferSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27494")
    private Long id;

    @Schema(description = "转移单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "转移单号不能为空")
    private String transferCode;

    @Schema(description = "转移日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "转移日期不能为空")
    private LocalDateTime transferDate;

    @Schema(description = "转移数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "转移数量不能为空")
    private Integer totalQuantity;

    @Schema(description = "状态（0待转移，1转移成功，2转移失败）", example = "2")
    private Integer status;

    @Schema(description = "操作人员ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "操作人员ID不能为空")
    private String transferUser;

    @Schema(description = "操作时间")
    private LocalDateTime transferTime;

    @Schema(description = "货品转移明细列表")
    private List<TransferDetailDO> transferDetails;

}