<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ReceiptRegistrationDetailMapper">


    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailPageRespVO">
        SELECT
            rrd.id,
            receipt_id AS receipt_registration_id,
            rr.receipt_code AS receipt_registration_code,
            io.order_code AS inbound_order_code,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            rrd.pending_quantity,
            rrd.pending_scattered_quantity,
            rrd.received_quantity,
            rrd.received_scattered_quantity,
            rrd.shelved_quantity,
            rrd.shelved_scattered_quantity,
            rrd.product_batch_number,
            rrd.production_date,
            t.code AS tray_code,
            w.name AS warehouse_name,
            wa.name AS warehouse_area_name,
            wl.code AS warehouse_location_code,
            rrd.difference_remark
        FROM
            receipt_registration_detail rrd
        LEFT JOIN
            receipt_registration rr ON rrd.receipt_id = rr.id
        LEFT JOIN
            system_users su ON rr.receipt_operator = su.id
        LEFT JOIN
            inbound_order io ON rr.inbound_order_id = io.id
        LEFT JOIN
            product p ON rrd.product_id = p.id
        LEFT JOIN
            shelving_management_detail smd ON rrd.shelving_detail_id = smd.id
        LEFT JOIN
            shelving_management sm ON smd.shelving_id = sm.id
        LEFT JOIN
            tray t ON smd.tray_id = t.id
        LEFT JOIN
            warehouse_location wl ON smd.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        LEFT JOIN
            warehouse_area wa ON wl.area_id = wa.id
        WHERE
            rrd.deleted = 0
        <if test="pageReqVO.receiptRegistrationCode != null and pageReqVO.receiptRegistrationCode != ''">
            AND rr.receipt_code LIKE CONCAT('%', #{pageReqVO.receiptRegistrationCode}, '%')
        </if>
        <if test="pageReqVO.inboundOrderCode != null and pageReqVO.inboundOrderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{pageReqVO.inboundOrderCode}, '%')
        </if>
        <if test="pageReqVO.receiptStatus != null">
            AND rr.receipt_status = #{pageReqVO.receiptStatus}
        </if>
        <if test="pageReqVO.receiptOperatorName != null and pageReqVO.receiptOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{pageReqVO.receiptOperatorName}, '%')
        </if>
        <if test="pageReqVO.shelvingStatus != null">
            AND sm.shelving_status = #{pageReqVO.shelvingStatus}
        </if>
        <if test="pageReqVO.productId">
            AND p.id = #{pageReqVO.productId}
        </if>
        <if test="pageReqVO.warehouseId">
            AND rr.warehouse_id = #{pageReqVO.warehouseId}
        </if>
        <if test="pageReqVO.receiptSearchStartTime != null and pageReqVO.receiptSearchStartTime != ''">
            AND rr.receipt_time &gt;= #{pageReqVO.receiptSearchStartTime}
        </if>
        <if test="pageReqVO.receiptSearchEndTime != null and pageReqVO.receiptSearchEndTime != ''">
            AND rr.receipt_time &lt;= #{pageReqVO.receiptSearchEndTime}
        </if>
        ORDER BY
            rrd.create_time DESC
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            COUNT(rrd.id)
        FROM
            receipt_registration_detail rrd
        LEFT JOIN
            receipt_registration rr ON rrd.receipt_id = rr.id
        LEFT JOIN
            system_users su ON rr.receipt_operator = su.id
        LEFT JOIN
            inbound_order io ON rr.inbound_order_id = io.id
        LEFT JOIN
            product p ON rrd.product_id = p.id
        LEFT JOIN
            shelving_management_detail smd ON rrd.shelving_detail_id = smd.id
        LEFT JOIN
            shelving_management sm ON smd.shelving_id = sm.id
        LEFT JOIN
            tray t ON smd.tray_id = t.id
        LEFT JOIN
            warehouse_location wl ON smd.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        LEFT JOIN
            warehouse_area wa ON wl.area_id = wa.id
        WHERE
            rrd.deleted = 0
        <if test="pageReqVO.receiptRegistrationCode != null and pageReqVO.receiptRegistrationCode != ''">
            AND rr.receipt_code LIKE CONCAT('%', #{pageReqVO.receiptRegistrationCode}, '%')
        </if>
        <if test="pageReqVO.inboundOrderCode != null and pageReqVO.inboundOrderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{pageReqVO.inboundOrderCode}, '%')
        </if>
        <if test="pageReqVO.receiptStatus != null">
            AND rr.receipt_status = #{pageReqVO.receiptStatus}
        </if>
        <if test="pageReqVO.receiptOperatorName != null and pageReqVO.receiptOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{pageReqVO.receiptOperatorName}, '%')
        </if>
        <if test="pageReqVO.shelvingStatus != null">
            AND sm.shelving_status = #{pageReqVO.shelvingStatus}
        </if>
        <if test="pageReqVO.productId">
            AND p.id = #{pageReqVO.productId}
        </if>
        <if test="pageReqVO.warehouseId">
            AND rr.warehouse_id = #{pageReqVO.warehouseId}
        </if>
        <if test="pageReqVO.receiptSearchStartTime != null and pageReqVO.receiptSearchStartTime != ''">
            AND rr.receipt_time &gt;= #{pageReqVO.receiptSearchStartTime}
        </if>
        <if test="pageReqVO.receiptSearchEndTime != null and pageReqVO.receiptSearchEndTime != ''">
            AND rr.receipt_time &lt;= #{pageReqVO.receiptSearchEndTime}
        </if>
    </select>
</mapper>