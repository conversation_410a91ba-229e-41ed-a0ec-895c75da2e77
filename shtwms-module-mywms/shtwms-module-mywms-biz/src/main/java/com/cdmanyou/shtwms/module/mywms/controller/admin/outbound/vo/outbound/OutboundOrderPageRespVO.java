package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 出库订单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OutboundOrderPageRespVO {

    @Schema(description = "出库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11422")
    private Long id;

    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单位")
    private String unitName;

    @Schema(description = "单位编码")
    @ExcelProperty("单位编码")
    private String unitCode;

    @Schema(description = "出库单号", example = "2")
    @ExcelProperty("出库单号")
    private String outboundCode;

    @Schema(description = "出库类型）", example = "8164")
    @ExcelProperty("出库类型")
    private Integer outboundType;

    @Schema(description = "出库单状态")
    @ExcelProperty("出库单状态")
    private String orderStatus;

    @Schema(description = "出库方式")
    @ExcelProperty("出库方式")
    private String outboundWay;

    @Schema(description = "出库价格")
    @ExcelProperty("出库价格")
    private Decimal outboundPrice;

    @Schema(description = "波次号")
    @ExcelProperty("波次号")
    private String wavesCode;

    @Schema(description = "货主名称", example = "1")
    private String producerName;

    @Schema(description = "货主编号", example = "1")
    private String producerCode;

    @Schema(description = "供应商名称", example = "1")
    private String supplierName;

    @Schema(description = "供应商编号", example = "1")
    private String supplierCode;

    @Schema(description = "货主单号", example = "1")
    @ExcelProperty("货主单号")
    private String producerOrderCode;

    @Schema(description = "客户名称", example = "2")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "客户编码", example = "2")
    @ExcelProperty("客户编码")
    private String customerCode;

    @Schema(description = "货品名称")
    @ExcelProperty("货品名称")
    private String productName;

    @Schema(description = "货品编码")
    @ExcelProperty("货品编码")
    private String productCode;

    @Schema(description = "货品规格")
    @ExcelProperty("货品规格")
    private String productSpecification;

    @Schema(description = "温区")
    @ExcelProperty("温区")
    private String temperatureZone;

    @Schema(description = "出库数量")
    @ExcelProperty("出库数量")
    private String outboundCount;

    @Schema(description = "换算率")
    @ExcelProperty("换算率")
    private String conversionRate;

    @Schema(description = "零单位数量")
    @ExcelProperty("零单位数量")
    private String simpleCount;

    @Schema(description = "出库重量")
    @ExcelProperty("出库重量")
    private Double outboundWeight;

    @Schema(description = "出库体积", example = "你说的对")
    @ExcelProperty("出库体积")
    private Double outboundVolume;

    @Schema(description = "商品批次号")
    @ExcelProperty("商品批次号")
    private String productBatchNumber;

    @Schema(description = "生产日期")
    @ExcelProperty("商品批次号")
    private LocalDate productionDate;

    @Schema(description = "到期日期")
    @ExcelProperty("到期日期")
    private String expirationDate;

    @Schema(description = "是否越库")
    @ExcelProperty("是否越库")
    private String straddleWarehouse;

    @Schema(description = "预约提货时间")
    @ExcelProperty("预约提货时间")
    private LocalDate bookingDeliveryTime;

    @Schema(description = "发运状态")
    @ExcelProperty("发运状态")
    private String sendProductStatus;

    @Schema(description = "发运时间")
    @ExcelProperty("发运时间")
    private String sendProductTime;

    @Schema(description = "分捡完成时间")
    @ExcelProperty("分捡完成时间")
    private String sortingCompleteTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}