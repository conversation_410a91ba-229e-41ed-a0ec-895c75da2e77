package com.cdmanyou.shtwms.module.system.api.logger;

import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.system.api.logger.dto.OperateLogCreateReqDTO;
import com.cdmanyou.shtwms.module.system.api.logger.dto.OperateLogPageReqDTO;
import com.cdmanyou.shtwms.module.system.api.logger.dto.OperateLogRespDTO;
import com.cdmanyou.shtwms.module.system.dal.dataobject.logger.OperateLogDO;
import com.cdmanyou.shtwms.module.system.service.logger.OperateLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class OperateLogApiImpl implements OperateLogApi {

    @Resource
    private OperateLogService operateLogService;

    @Override
    public CommonResult<Boolean> createOperateLog(OperateLogCreateReqDTO createReqDTO) {
        operateLogService.createOperateLog(createReqDTO);
        return success(true);
    }

    @Override
    public CommonResult<PageResult<OperateLogRespDTO>> getOperateLogPage(OperateLogPageReqDTO pageReqDTO) {
        PageResult<OperateLogDO> operateLogPage = operateLogService.getOperateLogPage(pageReqDTO);
        return success(BeanUtils.toBean(operateLogPage, OperateLogRespDTO.class));
    }

}
