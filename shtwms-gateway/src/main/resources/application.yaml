spring:
  application:
    name: gateway-server

  profiles:
    active: local

  codec:
    max-in-memory-size: 10MB # 调整缓冲区大小https://gitee.com/zhijiantianya/shtwms-cloud/pulls/176

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  config:
    import:
      - optional:classpath:application-${spring.profiles.active}.yaml # 加载【本地】配置
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml # 加载【Nacos】的配置

  cloud:
    # Spring Cloud Gateway 配置项，对应 GatewayProperties 类
    gateway:
      # 路由配置项，对应 RouteDefinition 数组
      routes:
        ## system-server 服务
        - id: system-admin-api # 路由的编号
          uri: grayLb://system-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/system/**
          filters:
              - RewritePath=/admin-api/system/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: system-app-api # 路由的编号
          uri: grayLb://system-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/system/**
          filters:
              - RewritePath=/app-api/system/v3/api-docs, /v3/api-docs
        ## wms-server 服务
        - id: wms-admin-api # 路由的编号
          uri: grayLb://wms-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/wms/**
          filters:
            - RewritePath=/admin-api/wms/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: wms-app-api # 路由的编号
          uri: grayLb://wms-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/wms/**
          filters:
            - RewritePath=/app-api/wms/v3/api-docs, /v3/api-docs
        ## mywms-server 服务
        - id: mywms-admin-api # 路由的编号
          uri: grayLb://mywms-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/mywms/**
          filters:
            - RewritePath=/admin-api/mywms/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v3/api-docs
        - id: mywms-app-api # 路由的编号
          uri: grayLb://mywms-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/mywms/**
          filters:
            - RewritePath=/app-api/mywms/v3/api-docs, /v3/api-docs
        ## infra-server 服务
        - id: infra-admin-api # 路由的编号
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/infra/**
          filters:
              - RewritePath=/admin-api/infra/v3/api-docs, /v3/api-docs
        - id: infra-app-api # 路由的编号
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/infra/**
          filters:
              - RewritePath=/app-api/infra/v3/api-docs, /v3/api-docs
        - id: infra-spring-boot-admin # 路由的编号（Spring Boot Admin）
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin/**
        - id: infra-websocket # 路由的编号（WebSocket）
          uri: grayLb://infra-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/infra/ws/**
      x-forwarded:
        prefix-enabled: false # 避免 Swagger 重复带上额外的 /admin-api/system 前缀

server:
  port: 58080

logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

knife4j:
  # 聚合 Swagger 文档，参考 https://doc.xiaominfo.com/docs/action/springcloud-gateway 文档
  gateway:
    enabled: true
    routes:
      - name: system-server
        service-name: system-server
        url: /admin-api/system/v3/api-docs
      - name: mywms-server
        service-name: mywms-server
        url: /admin-api/mywms/v3/api-docs
      - name: infra-server
        service-name: infra-server
        url: /admin-api/infra/v3/api-docs

shtwms:
  info:
    version: 1.0.0