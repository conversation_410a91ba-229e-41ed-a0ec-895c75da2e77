package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "库存预警查询实体")
@Data
public class InventoryPageReqVO extends PageParam {

    @Schema(description = "库存状态（0安全库存，1库存过高，2库存预警）")
    private Integer inventoryStatus;

    @Schema(description = "货品名称")
    private String productName;

}
