package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategoryPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategorySaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductCategoryDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品类别管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCategoryService {

    /**
     * 创建商品类别管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductCategory(@Valid ProductCategorySaveReqVO createReqVO);

    /**
     * 更新商品类别管理
     *
     * @param updateReqVO 更新信息
     */
    void updateProductCategory(@Valid ProductCategorySaveReqVO updateReqVO);

    /**
     * 删除商品类别管理
     *
     * @param id 编号
     */
    void deleteProductCategory(Long id);

    /**
     * 获得商品类别管理
     *
     * @param id 编号
     * @return 商品类别管理
     */
    ProductCategoryDO getProductCategory(Long id);

    /**
     * 获得商品类别管理分页
     *
     * @param pageReqVO 分页查询
     * @return 商品类别管理分页
     */
    PageResult<ProductCategoryDO> getProductCategoryPage(ProductCategoryPageReqVO pageReqVO);

    // ==================== 子表（商品管理） ====================

    /**
     * 获得商品管理列表
     *
     * @param categoryId 商品类别ID（关联商品类别表）
     * @return 商品管理列表
     */
    List<ProductDO> getProductListByCategoryId(Long categoryId);
    public void enableProductCategory(Long id);
    public void disableProductCategory(Long id);
    List<ProductCategoryDO> getProductCategoryList();
}