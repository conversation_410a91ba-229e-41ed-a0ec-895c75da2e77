package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.inventory.TransferDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 货品转移明细")
@RestController
@RequestMapping("/mywms/transfer-detail")
@Validated
public class TransferDetailController {

    @Resource
    private TransferDetailService transferDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建货品转移明细")
    @PreAuthorize("@ss.hasPermission('mywms:transfer-detail:create')")
    public CommonResult<Long> createTransferDetail(@Valid @RequestBody TransferDetailSaveReqVO createReqVO) {
        return success(transferDetailService.createTransferDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货品转移明细")
    @PreAuthorize("@ss.hasPermission('mywms:transfer-detail:update')")
    public CommonResult<Boolean> updateTransferDetail(@Valid @RequestBody TransferDetailSaveReqVO updateReqVO) {
        transferDetailService.updateTransferDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除货品转移明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:transfer-detail:delete')")
    public CommonResult<Boolean> deleteTransferDetail(@RequestParam("id") Long id) {
        transferDetailService.deleteTransferDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得货品转移明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:transfer-detail:query')")
    public CommonResult<TransferDetailRespVO> getTransferDetail(@RequestParam("id") Long id) {
        TransferDetailDO transferDetail = transferDetailService.getTransferDetail(id);
        return success(BeanUtils.toBean(transferDetail, TransferDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货品转移明细分页")
    @PreAuthorize("@ss.hasPermission('mywms:transfer-detail:query')")
    public CommonResult<PageResult<TransferDetailRespVO>> getTransferDetailPage(@Valid TransferDetailPageReqVO pageReqVO) {
        PageResult<TransferDetailDO> pageResult = transferDetailService.getTransferDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransferDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出货品转移明细 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:transfer-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransferDetailExcel(@Valid TransferDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransferDetailDO> list = transferDetailService.getTransferDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "货品转移明细.xls", "数据", TransferDetailRespVO.class,
                        BeanUtils.toBean(list, TransferDetailRespVO.class));
    }

}