package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail;

import com.cdmanyou.shtwms.framework.common.util.date.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "收货登记明细列表实体")
@Data
public class ReceiptRegistrationDetailPageRespVO {

    @Schema(description = "收货单明细ID")
    private Long id;

    @Schema(description = "收货单ID")
    private Long receiptRegistrationId;

    @Schema(description = "收货单单号")
    private String receiptRegistrationCode;

    @Schema(description = "入库单单号")
    private String inboundOrderCode;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "所属仓类别（0成品，1半成品，2原料）")
    private Integer houseType;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "待入库零散数量")
    private Integer pendingScatteredQuantity;

    @Schema(description = "验收数量")
    private Integer receivedQuantity;

    @Schema(description = "验收零散数量")
    private Integer receivedScatteredQuantity;

    @Schema(description = "上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "上架零散数量")
    private Integer shelvedScatteredQuantity;

    @Schema(description = "待入库重量")
    private BigDecimal inboundWeight;

    @Schema(description = "实收重量")
    private BigDecimal receivedWeight;

    @Schema(description = "上架重量")
    private BigDecimal shelvedWeight;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "托盘号")
    private String trayCode;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "差异备注")
    private String differenceRemark;
}
