package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.InventoryManagementDO;

import javax.validation.Valid;

/**
 * 库存管理 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryManagementService {

    /**
     * 创建库存管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryManagement(@Valid InventoryManagementSaveReqVO createReqVO);

    /**
     * 更新库存管理
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryManagement(@Valid InventoryManagementSaveReqVO updateReqVO);

    /**
     * 删除库存管理
     *
     * @param id 编号
     */
    void deleteInventoryManagement(Long id);

    /**
     * 获得库存管理
     *
     * @param id 编号
     * @return 库存管理
     */
    InventoryManagementDO getInventoryManagement(Long id);

    /**
     * 获得库存管理分页
     *
     * @param pageReqVO 分页查询
     * @return 库存管理分页
     */
    PageResult<InventoryManagementDO> getInventoryManagementPage(InventoryManagementPageReqVO pageReqVO);

}