package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 客户管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustomerPageReqVO extends PageParam {

    @Schema(description = "客户编码")
    private String code;

    @Schema(description = "客户名称", example = "张三")
    private String name;

    @Schema(description = "客户简称", example = "芋艿")
    private String shortName;

    @Schema(description = "客户类型（0内部，1外部，2其他）", example = "2")
    private Integer type;

    @Schema(description = "客户英文名称", example = "张三")
    private String englishName;

    @Schema(description = "营业执照号")
    private String businessLicense;

    @Schema(description = "客户描述", example = "你猜")
    private String description;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "所在地区(省市区)")
    private String region;

    @Schema(description = "信用额度")
    private BigDecimal creditLimit;

    @Schema(description = "结算方式")
    private String settlementMethod;

    @Schema(description = "启用状态（0启用，1禁用）", example = "1")
    private Integer status;

    @Schema(description = "合作状态", example = "2")
    private String cooperationStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}