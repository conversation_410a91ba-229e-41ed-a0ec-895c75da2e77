package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TrayPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.TrayDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 托盘管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TrayMapper extends BaseMapperX<TrayDO> {

    default boolean existsByCode(String code) {
        return selectCount(new LambdaQueryWrapper<TrayDO>()
                .eq(TrayDO::getCode, code)) > 0;
    }

    // 更新时检查托盘码唯一性（排除自身）
    default boolean existsByCodeAndIdNot(String code, Long excludeId) {
        return selectCount(new LambdaQueryWrapper<TrayDO>()
                .eq(TrayDO::getCode, code)
                .ne(TrayDO::getId, excludeId)) > 0;
    }

    default PageResult<TrayDO> selectPage(TrayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrayDO>()
                .likeIfPresent(TrayDO::getCode, reqVO.getCode())
                .eqIfPresent(TrayDO::getWarehouseId, reqVO.getWarehouseId())
                .eqIfPresent(TrayDO::getAreaId, reqVO.getAreaId())
                .likeIfPresent(TrayDO::getName, reqVO.getName())
                .eqIfPresent(TrayDO::getType, reqVO.getType())
                .eqIfPresent(TrayDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TrayDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TrayDO::getId));
    }

}