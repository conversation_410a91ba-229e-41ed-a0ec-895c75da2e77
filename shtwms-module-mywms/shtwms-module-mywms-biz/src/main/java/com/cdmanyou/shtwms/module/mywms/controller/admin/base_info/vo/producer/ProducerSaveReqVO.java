package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 货主管理新增/修改 Request VO")
@Data
public class ProducerSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18362")
    private Long id;

    @Schema(description = "货主编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "货主名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "货主名称不能为空")
    private String name;

    @Schema(description = "货主简称", example = "李四")
    private String shortName;

    @Schema(description = "货主类型（1内部，2外部，3其他）")
    private String cargoOwnerType;

    @Schema(description = "货主英文名称", example = "张三")
    private String englishName;

    @Schema(description = "营业执照号")
    private String businessLicense;

    @Schema(description = "货主描述", example = "随便")
    private String description;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "所在地区(省市区)")
    private String region;

    @Schema(description = "信用额度")
    private BigDecimal creditLimit;

    @Schema(description = "结算方式")
    private String settlementMethod;

    @Schema(description = "启用状态", example = "2")
    private String status;

    @Schema(description = "合作状态", example = "2")
    private String cooperationStatus;

    @Schema(description = "详细地址")
    private String fullAddress;

    @Schema(description = "卸货费用")
    private double unloadingExpenses;

    @Schema(description = "日拣费用")
    private double daytimePickingExpenses;

    @Schema(description = "夜拣费用")
    private double nighttimePickingExpenses;
}