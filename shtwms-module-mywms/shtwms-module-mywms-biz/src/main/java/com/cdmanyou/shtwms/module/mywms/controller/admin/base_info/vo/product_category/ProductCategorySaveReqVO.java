package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Schema(description = "管理后台 - 商品类别管理新增/修改 Request VO")
@Data
public class ProductCategorySaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15616")
    private Long id;

    @Schema(description = "类别编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "类别编码不能为空")
    private String code;

    @Schema(description = "类别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "类别名称不能为空")
    private String name;

    @Schema(description = "类别描述", example = "随便")
    private String description;

    @Schema(description = "父类别ID", example = "18790")
    private Long parentId;

    @Schema(description = "是否启用(1启用/0停用)", example = "2")
    private Integer status;



}