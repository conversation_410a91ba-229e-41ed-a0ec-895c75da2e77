package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "库存统计简略信息")
@Data
public class SimpleInventoryStatisticVO {

    @Schema(description = "货品总类")
    private Integer productCategoryTotalCount;

    @Schema(description = "安全库存货品总数")
    private Integer safetyProductCount;

    @Schema(description = "库存过高货品总数")
    private Integer maxProductCount;

    @Schema(description = "库存预警货品总数")
    private Integer warningProductCount;
}
