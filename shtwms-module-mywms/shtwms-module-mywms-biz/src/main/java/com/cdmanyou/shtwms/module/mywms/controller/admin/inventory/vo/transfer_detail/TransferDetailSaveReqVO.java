package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 货品转移明细新增/修改 Request VO")
@Data
public class TransferDetailSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4651")
    private Long id;

    @Schema(description = "转移单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "25002")
    @NotNull(message = "转移单ID（关联主表）不能为空")
    private Long transferId;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14627")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "生产日期")
    private LocalDate productionDate;

    @Schema(description = "转移数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "转移数量不能为空")
    private Integer quantity;

    @Schema(description = "转出库位ID（关联库位表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "27693")
    @NotNull(message = "转出库位ID（关联库位表）不能为空")
    private Long fromWareHouseLocationId;

    @Schema(description = "转出库位ID（关联库位表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "11086")
    @NotNull(message = "转出库位ID（关联库位表）不能为空")
    private Long toWareHouseLocationId;

}