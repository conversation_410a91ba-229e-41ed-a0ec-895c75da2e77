package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 出库订单明细")
@RestController
@RequestMapping("/mywms/outbound-order-detail")
@Validated
public class OutboundOrderDetailController {

    @Resource
    private OutboundOrderDetailService outboundOrderDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建出库订单明细")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order-detail:create')")
    public CommonResult<Long> createOutboundOrderDetail(@Valid @RequestBody OutboundOrderDetailSaveReqVO createReqVO) {
        return success(outboundOrderDetailService.createOutboundOrderDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新出库订单明细")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order-detail:update')")
    public CommonResult<Boolean> updateOutboundOrderDetail(@Valid @RequestBody OutboundOrderDetailSaveReqVO updateReqVO) {
        outboundOrderDetailService.updateOutboundOrderDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除出库订单明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order-detail:delete')")
    public CommonResult<Boolean> deleteOutboundOrderDetail(@RequestParam("id") Long id) {
        outboundOrderDetailService.deleteOutboundOrderDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得出库订单明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order-detail:query')")
    public CommonResult<OutboundOrderDetailRespVO> getOutboundOrderDetail(@RequestParam("id") Long id) {
        OutboundOrderDetailDO outboundOrderDetail = outboundOrderDetailService.getOutboundOrderDetail(id);
        return success(BeanUtils.toBean(outboundOrderDetail, OutboundOrderDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得出库订单明细分页")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order-detail:query')")
    public CommonResult<PageResult<OutboundOrderDetailRespVO>> getOutboundOrderDetailPage(@Valid OutboundOrderDetailPageReqVO pageReqVO) {
        PageResult<OutboundOrderDetailDO> pageResult = outboundOrderDetailService.getOutboundOrderDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OutboundOrderDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出出库订单明细 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOutboundOrderDetailExcel(@Valid OutboundOrderDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OutboundOrderDetailDO> list = outboundOrderDetailService.getOutboundOrderDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "出库订单明细.xls", "数据", OutboundOrderDetailRespVO.class,
                        BeanUtils.toBean(list, OutboundOrderDetailRespVO.class));
    }

}