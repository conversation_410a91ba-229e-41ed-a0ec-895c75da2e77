package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 托盘管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrayPageReqVO extends PageParam {

    @Schema(description = "托盘编码")
    private String code;

    @Schema(description = "所属仓库ID（关联仓库表）", example = "8756")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", example = "27569")
    private Long areaId;

    @Schema(description = "托盘名称", example = "王五")
    private String name;

    @Schema(description = "托盘类型", example = "2")
    private String type;

    @Schema(description = "状态(1启用/0停用)", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}