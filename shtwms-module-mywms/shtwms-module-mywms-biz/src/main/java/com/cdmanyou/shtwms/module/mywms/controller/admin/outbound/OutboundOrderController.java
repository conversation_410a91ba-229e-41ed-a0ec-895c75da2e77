package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import cn.hutool.core.util.StrUtil;
import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.collection.CollectionUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutBoundProductDetail;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProducerService;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductService;
import com.cdmanyou.shtwms.module.mywms.service.inventoryplan.InventoryPlanService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 出库订单主")
@RestController
@RequestMapping("/mywms/outbound-order")
@Validated
public class OutboundOrderController {

    @Resource
    private OutboundOrderService outboundOrderService;

    @Resource
    private ProductService productService;

    @Resource
    private InventoryPlanService inventoryPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建出库订单主")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:create')")
    public CommonResult<Long> createOutboundOrder(@Valid @RequestBody OutboundOrderSaveReqVO createReqVO) {
        return success(outboundOrderService.createOutboundOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新出库订单主")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:update')")
    public CommonResult<Boolean> updateOutboundOrder(@Valid @RequestBody OutboundOrderSaveReqVO updateReqVO) {
        outboundOrderService.updateOutboundOrder(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-determine")
    @Operation(summary = "出库单提交")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:update-determine')")
    public CommonResult<String> outboundStatusUpdateDetermine(@Valid @RequestBody OutboundStatusReqVO outboundStatusReqVO) {
        //查询当天的盘点状态
        List<InventoryPlanDO> inventoryPlanDOS = inventoryPlanService.selectStatusByDate(LocalDate.now(), null);
        if (!CollectionUtils.isAnyEmpty(inventoryPlanDOS)) {
            throw new RuntimeException("今日存在正在进行的盘点,出库单提交失败");
        }
        outboundStatusReqVO.setOrderStatus(1);
        outboundStatusReqVO.setSendStatus(null);
        outboundOrderService.outboundStatusUpdate(outboundStatusReqVO);
        return CommonResult.success("出库单提交成功");
    }

    @PutMapping("/update-quash")
    @Operation(summary = "出库单作废")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:update-quash')")
    public CommonResult<String> outboundStatusUpdateQuash(@Valid @RequestBody OutboundStatusReqVO outboundStatusReqVO) {
        outboundStatusReqVO.setOrderStatus(3);
        outboundStatusReqVO.setSendStatus(null);
        outboundOrderService.outboundStatusUpdate(outboundStatusReqVO);
        return CommonResult.success("出库单作废成功");
    }

    @PutMapping("/update-send")
    @Operation(summary = "出库单发运")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:update-send')")
    public CommonResult<String> outboundStatusUpdateSend(@Valid @RequestBody OutboundStatusReqVO outboundStatusReqVO) {
        //查询当天的盘点状态
        List<InventoryPlanDO> inventoryPlanDOS = inventoryPlanService.selectStatusByDate(LocalDate.now(), 1);
        if (!CollectionUtils.isAnyEmpty(inventoryPlanDOS)) {
            throw new RuntimeException("今日存在正在进行的盘点,出库单发运失败");
        }
        outboundStatusReqVO.setSendStatus(1);
        outboundOrderService.outboundStatusUpdate(outboundStatusReqVO);
        return CommonResult.success("发运成功");
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除出库订单主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:delete')")
    public CommonResult<Boolean> deleteOutboundOrder(@RequestParam("id") Long id) {
        outboundOrderService.deleteOutboundOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得出库订单主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:query')")
    public CommonResult<OutboundOrderRespVO> getOutboundOrder(@RequestParam("id") Long id) {
        OutboundOrderRespVO outboundOrder = outboundOrderService.getOutboundOrder(id);
        return CommonResult.success(outboundOrder);
    }

    @GetMapping("/page")
    @Operation(summary = "获得出库订单主分页")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:query')")
    public CommonResult<PageResult<OutboundOrderPageRespVO>> getOutboundOrderPage(@Valid OutboundOrderPageReqVO pageReqVO) {
        PageResult<OutboundOrderPageRespVO> pageResult = outboundOrderService.getOutboundOrderPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出出库订单主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOutboundOrderExcel(@Valid OutboundOrderPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OutboundOrderPageRespVO> list = outboundOrderService.getOutboundOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "出库订单主.xls", "数据", OutboundOrderPageRespVO.class,
                list);
    }

    // ==================== 子表（出库订单明细） ====================

    @GetMapping("/outbound-order-detail/list-by-outbound-id")
    @Operation(summary = "获得出库订单明细列表")
    @Parameter(name = "outboundId", description = "出库单ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:query')")
    public CommonResult<List<OutboundOrderDetailRespVO>> getOutboundOrderDetailListByOutboundId(@RequestParam("outboundId") Long outboundId) {
        return success(outboundOrderService.getOutboundOrderDetailListByOutboundId(outboundId));
    }


    @GetMapping("/product-type")
    @Operation(summary = "出货时获取货品列表")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:product-type')")
    public CommonResult<List<ProductDO>> getProductSimpleList(@RequestParam("producerId") Long producerId) {
        List<ProductDO> res = productService.getProductSimpleList(producerId);
        return CommonResult.success(res);
    }


    @GetMapping("/info-product-code")
    @Operation(summary = "出货时根据产品id，数量，生产日期获取货品详情")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:info-product-code')")
    public CommonResult<OutBoundProductDetail> getInfoByProductOutboundInfo(@RequestParam("productId") String productId,
                                                                            @RequestParam("outboundNumber") Integer outboundNumber,
                                                                            @RequestParam(value = "productionDate", required = false) String productionDate,
                                                                            @RequestParam(value = "houseType", required = false) Integer houseType,
                                                                            @RequestParam("warehouseId") Long warehouseId
    ) {
        LocalDate productionDateStr = null;
        if (StrUtil.isNotBlank(productionDate)) {
            productionDateStr = LocalDate.parse(productionDate);
        }
        OutBoundProductDetail res = productService.getInfoByProductOutboundInfo(productId, outboundNumber, productionDateStr, warehouseId, houseType);
        return CommonResult.success(res);
    }


    @GetMapping("/product-production-date")
    @Operation(summary = "出货时根据产品id，数量获取生产日期")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:product-production-date')")
    public CommonResult<List<String>> getProductionDateByProductOutboundInfo(@RequestParam("productId") String productId, @RequestParam("outboundNumber") Integer outboundNumber) {
        List<String> res = productService.getProductionDateByProductOutboundInfo(productId, outboundNumber);
        return CommonResult.success(res);
    }


}