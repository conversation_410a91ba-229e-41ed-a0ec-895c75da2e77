package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 波次单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WaveOrderDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3796")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "波次单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "13302")
    @ExcelProperty("波次单ID（关联主表）")
    private Long waveId;

    @Schema(description = "出库单号（关联出库订单主表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出库单号（关联出库订单主表）")
    private String outboundCode;

    @Schema(description = "客户名称", example = "赵六")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    @ExcelProperty("单位（非必填）")
    private String unit;

    @Schema(description = "待拣货数量")
    @ExcelProperty("待拣货数量")
    private Integer pendingPicking;

    @Schema(description = "已拣货数量")
    @ExcelProperty("已拣货数量")
    private Integer pickedQuantity;

    @Schema(description = "差异数量")
    @ExcelProperty("差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    @ExcelProperty("批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    @ExcelProperty("生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "库区ID（关联库区表）", example = "26669")
    @ExcelProperty("库区ID（关联库区表）")
    private Long areaId;

    @Schema(description = "库位ID（关联货位表）", example = "15581")
    @ExcelProperty("库位ID（关联货位表）")
    private Long locationId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}