package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 分拣任务单明细新增/修改 Request VO")
@Data
public class SortingTaskOrderDetailSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31550")
    private Long id;

    @Schema(description = "分拣任务单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2883")
    @NotNull(message = "分拣任务单ID（关联主表）不能为空")
    private Long sortingOrderId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    private String unit;

    @Schema(description = "待分拣数量")
    private Integer pendingSortingQuantity;

    @Schema(description = "已分拣数量")
    private Integer sortedQuantity;

    @Schema(description = "差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "拣货容器", example = "2")
    private String pickingContainerType;

    @Schema(description = "拣货容器号")
    private String pickingContainerCode;

}