package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo;

import com.cdmanyou.shtwms.module.mywms.converter.InventoryStatusConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.cdmanyou.shtwms.module.mywms.converter.InventoryTypeConverter;

@Schema(description = "管理后台 - 盘点计划主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryPlanRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27061")
    private Long id;

    @Schema(description = "盘点计划单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("盘点计划单号")
    private String inventoryPlanCode;

    @Schema(description = "计划名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("计划名称")
    private String planName;

    @Schema(description = "盘点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "盘点类型",converter = InventoryTypeConverter.class)
    private Integer inventoryType;

    @Schema(description = "仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "17776")
    private Long warehouseId;

    @Schema(description = "仓库名称")
    @ExcelProperty("仓库名称")
    private String warehouseName;

    @Schema(description = "盘点日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("盘点日期")
    private LocalDate inventoryDate;

    @Schema(description = "状态", example = "1")
    @ExcelProperty(value = "状态",converter = InventoryStatusConverter.class)
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人名称")
    @ExcelProperty("创建人名称")
    private String creatorName;

    @Schema(description = "创建人")
    private Long creator;

}