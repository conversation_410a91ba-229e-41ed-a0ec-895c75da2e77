package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailSubmitVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ReceiptRegistrationDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ReceiptRegistrationDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ReceiptRegistrationDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 收货登记单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReceiptRegistrationDetailServiceImpl implements ReceiptRegistrationDetailService {

    @Resource
    private ReceiptRegistrationDetailMapper receiptRegistrationDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDetailsByInboundOrderSubmit(Long receiptId, List<InboundOrderDetailSubmitVO> list) {
        List<ReceiptRegistrationDetailDO> detailDOList = new ArrayList<>();
        list.forEach(detail -> {
            ReceiptRegistrationDetailDO bean = BeanUtils.toBean(detail, ReceiptRegistrationDetailDO.class);
            bean.setId(null).setReceiptId(receiptId).setInboundOrderDetailId(detail.getId()).setReceivedWeight(detail.getInboundWeight());
            detailDOList.add(bean);
        });
        receiptRegistrationDetailMapper.insertBatch(detailDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDetailsByReceiptRegistrationSubmit(Long receiptId, List<ReceiptRegistrationDetailSaveVO> list) {
        receiptRegistrationDetailMapper.deleteByReceiptId(receiptId);
        // 必传 InboundOrderDetailId
        List<ReceiptRegistrationDetailDO> detailDOList = new ArrayList<>();
        list.forEach(detail -> {
            ReceiptRegistrationDetailDO bean = BeanUtils.toBean(detail, ReceiptRegistrationDetailDO.class);
            bean.setId(null).setReceiptId(receiptId).setReceivedWeight(detail.getInboundWeight());
            detailDOList.add(bean);
        });
        receiptRegistrationDetailMapper.insertBatch(detailDOList);
    }

    @Override
    public PageResult<ReceiptRegistrationDetailPageRespVO> getReceiptRegistrationPage(ReceiptRegistrationDetailPageReqVO pageReqVO) {
        Page<ReceiptRegistrationDetailPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = receiptRegistrationDetailMapper.selectPage(page, pageReqVO);
        Long total = receiptRegistrationDetailMapper.selectPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }
}