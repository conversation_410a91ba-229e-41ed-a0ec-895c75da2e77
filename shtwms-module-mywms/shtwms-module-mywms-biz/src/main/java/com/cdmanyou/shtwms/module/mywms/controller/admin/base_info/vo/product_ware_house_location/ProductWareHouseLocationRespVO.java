package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品货位关联表-记录货位上的商品数量 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductWareHouseLocationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10961")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12637")
    @ExcelProperty("入库单ID")
    private Long inboundOrderId;

    @Schema(description = "货主ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27126")
    @ExcelProperty("货主ID")
    private Long producerId;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9107")
    @ExcelProperty("商品ID")
    private Long productId;

    @Schema(description = "货位ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30806")
    @ExcelProperty("货位ID")
    private Long warehouseLocationId;

    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate productionDate;

    @Schema(description = "商品总数", example = "5930")
    @ExcelProperty("商品总数")
    private Integer count;

    @Schema(description = "占用数量")
    @ExcelProperty("占用数量")
    private Integer occupation;

    @Schema(description = "可用数量")
    @ExcelProperty("可用数量")
    private Integer available;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "零散总量", example = "30895")
    @ExcelProperty("零散总量")
    private Integer scatteredCount;

    @Schema(description = "零散占用量")
    @ExcelProperty("零散占用量")
    private Integer scatteredOccupation;

    @Schema(description = "零散可用量")
    @ExcelProperty("零散可用量")
    private Integer scatteredAvailable;

}