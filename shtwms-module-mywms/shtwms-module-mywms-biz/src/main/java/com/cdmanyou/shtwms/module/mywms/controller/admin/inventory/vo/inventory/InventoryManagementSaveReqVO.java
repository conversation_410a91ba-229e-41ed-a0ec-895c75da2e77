package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 库存管理新增/修改 Request VO")
@Data
public class InventoryManagementSaveReqVO {

    private Long id;

    @Schema(description = "仓库编码（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "仓库编码（关联仓库表）不能为空")
    private String warehouseCode;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "仓库名称不能为空")
    private String warehouseName;

    @Schema(description = "库区编码（关联库区表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "库区编码（关联库区表）不能为空")
    private String areaCode;

    @Schema(description = "库区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "库区名称不能为空")
    private String areaName;

    @Schema(description = "库位编码（关联库位表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "库位编码（关联库位表）不能为空")
    private String locationCode;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "商品规格")
    private String specification;

    @Schema(description = "计量单位")
    private String unit;

    @Schema(description = "批次号")
    private String batchNumber;

    @Schema(description = "生产日期")
    private LocalDate productionDate;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存数量不能为空")
    private Integer quantity;

    @Schema(description = "库存状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "库存状态不能为空")
    private String status;

}