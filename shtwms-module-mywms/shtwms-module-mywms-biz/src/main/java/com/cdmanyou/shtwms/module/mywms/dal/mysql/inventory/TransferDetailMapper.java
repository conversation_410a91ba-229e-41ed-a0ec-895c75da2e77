package com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 货品转移明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransferDetailMapper extends BaseMapperX<TransferDetailDO> {

    default List<TransferDetailDO> selectListByTransferId(Long transferId) {
        return selectList(TransferDetailDO::getTransferId, transferId);
    }

    default int deleteByTransferId(Long transferId) {
        return delete(TransferDetailDO::getTransferId, transferId);
    }

    default PageResult<TransferDetailDO> selectPage(TransferDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TransferDetailDO>()
                .eqIfPresent(TransferDetailDO::getTransferId, reqVO.getTransferId())
                .eqIfPresent(TransferDetailDO::getProductId, reqVO.getProductId())
                .eqIfPresent(TransferDetailDO::getProductCode, reqVO.getProductCode())
                .likeIfPresent(TransferDetailDO::getProductName, reqVO.getProductName())
                .eqIfPresent(TransferDetailDO::getSpecification, reqVO.getSpecification())
                .eqIfPresent(TransferDetailDO::getUnit, reqVO.getUnit())
                .betweenIfPresent(TransferDetailDO::getProductionDate, reqVO.getProductionDate())
                .eqIfPresent(TransferDetailDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(TransferDetailDO::getFromWareHouseLocationId, reqVO.getFromWareHouseLocationId())
                .eqIfPresent(TransferDetailDO::getToWareHouseLocationId, reqVO.getToWareHouseLocationId())
                .betweenIfPresent(TransferDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TransferDetailDO::getId));
    }
}