package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 出库订单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OutboundOrderDetailPageReqVO extends PageParam {

    @Schema(description = "出库单ID（关联主表）", example = "32711")
    private Long outboundId;

    @Schema(description = "商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", example = "王五")
    private String productName;

    @Schema(description = "规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    private String unit;

    @Schema(description = "待出库数量")
    private Integer pendingQuantity;

    @Schema(description = "已出库数量")
    private Integer receivedQuantity;

    @Schema(description = "差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] productionDate;

    @Schema(description = "出库价格", example = "15796")
    private BigDecimal outboundPrice;

    @Schema(description = "出库金额(含税额)")
    private BigDecimal outboundAmount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "分拣完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sortingCompleteTime;

    @Schema(description = "是否越库(0否，1是)")
    private Integer straddleWarehouse;

    @Schema(description = "出库体积")
    private Long volume;

    @Schema(description = "出库重量")
    private Long grossWeight;
}