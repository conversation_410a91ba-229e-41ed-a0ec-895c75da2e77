package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仓库管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WareHouseRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14757")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "仓库编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("仓库编码")
    private String code;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("仓库名称")
    private String name;

    @Schema(description = "仓库类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("仓库类型")
    private String type;

    @Schema(description = "仓库面积(㎡)")
    @ExcelProperty("仓库面积(㎡)")
    private BigDecimal area;

    @Schema(description = "仓库容量(吨)")
    @ExcelProperty("仓库容量(吨)")
    private BigDecimal capacity;

    @Schema(description = "仓库状态(1启用/0停用)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("仓库状态(1启用/0停用)")
    private Integer status;

    @Schema(description = "仓库地址")
    @ExcelProperty("仓库地址")
    private String address;

    @Schema(description = "联系人")
    @ExcelProperty("联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    @ExcelProperty("联系邮箱")
    private String contactEmail;

    @Schema(description = "仓库描述", example = "随便")
    @ExcelProperty("仓库描述")
    private String description;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}