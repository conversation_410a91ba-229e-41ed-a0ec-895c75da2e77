package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 上架管理单主 DO
 *
 * <AUTHOR>
 */
@TableName("shelving_management")
@KeySequence("shelving_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShelvingManagementDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 上架单号
     */
    private String shelvingCode;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 上架单状态（0未上架，1已上架）
     */
    private Integer shelvingStatus;
    /**
     * 待上架总数（实收货品总数）
     */
    private Integer receivedQuantityTotal;
    /**
     * 待上架零散总数（实收货品零散总数）
     */
    private Integer receivedScatteredQuantityTotal;
    /**
     * 实际上架总数
     */
    private Integer shelvedQuantityTotal;
    /**
     * 实际上架零散总数
     */
    private Integer shelvedScatteredQuantityTotal;
    /**
     * 待上架总重量（实收货品总重量）
     */
    private BigDecimal receivedWeightTotal;
    /**
     * 实际上架总重量
     */
    private BigDecimal shelvedWeightTotal;
    /**
     * 上架员ID
     */
    private Long shelvingOperator;
    /**
     * 上架时间
     */
    private LocalDateTime shelvingTime;
    /**
     * 提交人ID
     */
    private Long submitOperator;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 备注（非必填）
     */
    private String remark;

}