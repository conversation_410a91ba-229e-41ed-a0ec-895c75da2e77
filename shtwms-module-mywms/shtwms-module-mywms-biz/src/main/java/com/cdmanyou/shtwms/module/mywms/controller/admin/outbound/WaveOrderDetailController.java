package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.WaveOrderDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 波次单明细")
@RestController
@RequestMapping("/mywms/wave-order-detail")
@Validated
public class WaveOrderDetailController {

    @Resource
    private WaveOrderDetailService waveOrderDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建波次单明细")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order-detail:create')")
    public CommonResult<Long> createWaveOrderDetail(@Valid @RequestBody WaveOrderDetailSaveReqVO createReqVO) {
        return success(waveOrderDetailService.createWaveOrderDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新波次单明细")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order-detail:update')")
    public CommonResult<Boolean> updateWaveOrderDetail(@Valid @RequestBody WaveOrderDetailSaveReqVO updateReqVO) {
        waveOrderDetailService.updateWaveOrderDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除波次单明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:wave-order-detail:delete')")
    public CommonResult<Boolean> deleteWaveOrderDetail(@RequestParam("id") Long id) {
        waveOrderDetailService.deleteWaveOrderDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得波次单明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order-detail:query')")
    public CommonResult<WaveOrderDetailRespVO> getWaveOrderDetail(@RequestParam("id") Long id) {
        WaveOrderDetailDO waveOrderDetail = waveOrderDetailService.getWaveOrderDetail(id);
        return success(BeanUtils.toBean(waveOrderDetail, WaveOrderDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得波次单明细分页")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order-detail:query')")
    public CommonResult<PageResult<WaveOrderDetailRespVO>> getWaveOrderDetailPage(@Valid WaveOrderDetailPageReqVO pageReqVO) {
        PageResult<WaveOrderDetailDO> pageResult = waveOrderDetailService.getWaveOrderDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WaveOrderDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出波次单明细 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:wave-order-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWaveOrderDetailExcel(@Valid WaveOrderDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WaveOrderDetailDO> list = waveOrderDetailService.getWaveOrderDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "波次单明细.xls", "数据", WaveOrderDetailRespVO.class,
                        BeanUtils.toBean(list, WaveOrderDetailRespVO.class));
    }

}