package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 波次单明细 DO
 *
 * <AUTHOR>
 */
@TableName("wave_order_detail")
@KeySequence("wave_order_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WaveOrderDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 波次单ID（关联主表）
     */
    private Long waveId;
    /**
     * 出库单号（关联出库订单主表）
     */
    private String outboundCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格（非必填）
     */
    private String specification;
    /**
     * 单位（非必填）
     */
    private String unit;
    /**
     * 待拣货数量
     */
    private Integer pendingPicking;
    /**
     * 已拣货数量
     */
    private Integer pickedQuantity;
    /**
     * 差异数量
     */
    private Integer differenceQuantity;
    /**
     * 批次号（非必填）
     */
    private String batchNumber;
    /**
     * 生产日期（非必填）
     */
    private LocalDate productionDate;
    /**
     * 库区ID（关联库区表）
     */
    private Long areaId;
    /**
     * 库位ID（关联货位表）
     */
    private Long locationId;

    /**
     * 产品id
     */
    private Long productId;
    /**
     * 客户ID
     */
    private Long customerId;
}