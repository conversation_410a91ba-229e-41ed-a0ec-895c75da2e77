package com.cdmanyou.shtwms.module.mywms.dal.mysql.inventoryplan;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 盘点计划主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryPlanMapper extends BaseMapperX<InventoryPlanDO> {

    default PageResult<InventoryPlanDO> selectPage(InventoryPlanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryPlanDO>()
                .eqIfPresent(InventoryPlanDO::getInventoryPlanCode, reqVO.getInventoryPlanCode())
                .eqIfPresent(InventoryPlanDO::getInventoryType, reqVO.getInventoryType())
                .eqIfPresent(InventoryPlanDO::getWarehouseId, reqVO.getWarehouseId())
//                .betweenIfPresent(InventoryPlanDO::getInventoryDate, reqVO.getInventoryDate())
                .gtIfPresent(InventoryPlanDO::getInventoryDate,reqVO.getStartTime())
                .ltIfPresent(InventoryPlanDO::getInventoryDate,reqVO.getEndTime())
                .eqIfPresent(InventoryPlanDO::getStatus, reqVO.getStatus())
                .orderByDesc(InventoryPlanDO::getId));
    }

}