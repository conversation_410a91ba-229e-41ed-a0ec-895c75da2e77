package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 波次单主 Service 接口
 *
 * <AUTHOR>
 */
public interface WaveOrderService {

    /**
     * 创建波次单主
     *
     * @return 编号
     */
    Long createWaveOrder(List<Long> outboundIds);

    /**
     * 更新波次单主
     *
     * @param updateReqVO 更新信息
     */
    void updateWaveOrder(@Valid WaveOrderSaveReqVO updateReqVO);

    /**
     * 删除波次单主
     *
     * @param id 编号
     */
    void deleteWaveOrder(Long id);

    /**
     * 获得波次单主
     *
     * @param id 编号
     * @return 波次单主
     */
    WaveOrderDO getWaveOrder(Long id);

    /**
     * 获得波次单主分页
     *
     * @param pageReqVO 分页查询
     * @return 波次单主分页
     */
    PageResult<WaveOrderDO> getWaveOrderPage(WaveOrderPageReqVO pageReqVO);

    // ==================== 子表（波次单明细） ====================

    /**
     * 获得波次单明细列表
     *
     * @param waveId 波次单ID（关联主表）
     * @return 波次单明细列表
     */
    List<WaveOrderDetailDO> getWaveOrderDetailListByWaveId(Long waveId);

}