package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.exception.ServiceException;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.security.core.util.SecurityFrameworkUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferInputVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailInputVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 货品转移 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransferServiceImpl implements TransferService {

    @Resource
    private TransferMapper transferMapper;

    @Resource
    private TransferDetailMapper transferDetailMapper;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransfer(TransferInputVO inputVO) {
        // 插入
        TransferDO transfer = BeanUtils.toBean(inputVO, TransferDO.class);
        // 生成转移单号
        transfer.setTransferCode(generateTransferCode());
        transferMapper.insert(transfer);
        // 插入子表
        createTransferDetailList(transfer.getId(), inputVO.getTransferDetails());
        // 返回
        return transfer.getId();
    }

    private String generateTransferCode() {
        // 生成入库单日期号
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成序列号
        Long maxSequence = transferMapper.getMaxSequence(dateStr);
        Long newMaxSequence = (maxSequence == null) ? 1L : maxSequence + 1;
        // 格式化序列号
        String sequence = String.format("%06d", newMaxSequence);
        return "ZY" + dateStr + sequence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransfer(TransferInputVO inputVO) {
        // 校验存在
        validateTransferExists(inputVO.getId());
        // 更新
        TransferDO updateObj = BeanUtils.toBean(inputVO, TransferDO.class);
        transferMapper.updateById(updateObj);
        // 更新子表
        updateTransferDetailList(inputVO.getId(), inputVO.getTransferDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void voidByCode(String code) {
        // 校验存在性
        TransferDO transferDO = validateTransferExists(code);
        if (transferDO.getStatus() != 0) {
            throw exception(TRANSFER_VOID_ERROR_BY_STATUS);
        }
        // 作废
        transferMapper.voidByCode(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeByCode(String code) {
        // 校验存在性
        TransferDO transferDO = validateTransferExists(code);
        if (transferDO.getStatus() != 0) {
            throw exception(TRANSFER_COMPLETE_ERROR_BY_STATUS);
        }

        TransferDO updateObj = new TransferDO();
        updateObj.setId(transferDO.getId())
                .setTransferDate(LocalDateTime.now())
                .setStatus(1)
                .setTransferUser(SecurityFrameworkUtils.getLoginUserId().toString())
                .setTransferTime(LocalDateTime.now());

        List<TransferDetailDO> detailDOS = transferDetailMapper.selectListByTransferId(transferDO.getId());
        for (TransferDetailDO detailDO : detailDOS) {
            Integer quantity = detailDO.getQuantity();
            Integer scatteredQuantity = detailDO.getScatteredQuantity();

            ProductWareHouseLocationDO fromProduct = productWareHouseLocationMapper.selectByConditions(detailDO.getProducerId(),
                    detailDO.getProductId(),
                    detailDO.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    detailDO.getFromWareHouseLocationId());
            if (Objects.isNull(fromProduct)) {
                throw new ServiceException(1002_1111, "转出货位不存在");
            }
            ProductWareHouseLocationDO toProduct = productWareHouseLocationMapper.selectByConditions(detailDO.getProducerId(),
                    detailDO.getProductId(),
                    detailDO.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    detailDO.getToWareHouseLocationId());
            if (Objects.isNull(toProduct)) {
                ProductWareHouseLocationDO createObj = new ProductWareHouseLocationDO();
                createObj.setProducerId(fromProduct.getProducerId())
                        .setProductId(fromProduct.getProductId())
                        .setProductionDate(fromProduct.getProductionDate())
                        .setProductBatchNumber(fromProduct.getProductBatchNumber())
                        .setWarehouseLocationId(detailDO.getToWareHouseLocationId())
                        .setCount(detailDO.getQuantity())
                        .setAvailable(detailDO.getQuantity())
                        .setOccupation(0)
                        .setScatteredCount(detailDO.getScatteredQuantity())
                        .setScatteredAvailable(detailDO.getScatteredQuantity())
                        .setScatteredOccupation(0);
                productWareHouseLocationMapper.insert(createObj);
            } else {
                toProduct.setCount(toProduct.getCount() + quantity)
                        .setAvailable(toProduct.getAvailable() + quantity)
                        .setScatteredCount(toProduct.getScatteredCount() + scatteredQuantity)
                        .setScatteredAvailable(toProduct.getScatteredAvailable() + scatteredQuantity);
                int updateRows = productWareHouseLocationMapper.updateById(toProduct);
                if (updateRows == 0) {
                    throw new ServiceException(1001_500, "网络繁忙，请稍候重试");
                }
            }
            fromProduct.setCount(fromProduct.getCount() - quantity)
                    .setAvailable(fromProduct.getAvailable() - quantity)
                    .setScatteredCount(fromProduct.getScatteredCount() - scatteredQuantity)
                    .setScatteredAvailable(fromProduct.getScatteredAvailable() - scatteredQuantity);
            int updateRows = productWareHouseLocationMapper.updateById(fromProduct);
            if (updateRows == 0) {
                throw new ServiceException(1001_500, "网络繁忙，请稍候重试");
            }
        }

        transferMapper.updateById(updateObj);
    }


    private void validateTransferExists(Long id) {
        if (transferMapper.selectById(id) == null) {
            throw exception(TRANSFER_NOT_EXISTS);
        }
    }

    private TransferDO validateTransferExists(String code) {
        TransferDO transferDO = transferMapper.selectIdByCode(code);
        if (Objects.isNull(transferDO)) {
            throw exception(TRANSFER_NOT_EXISTS);
        }
        return transferDO;
    }

    @Override
    public TransferRespVO getTransferByCode(String code) {
        return transferMapper.getTransferByCode(code);
    }

    @Override
    public PageResult<TransferPageRespVO> getTransferPage(TransferPageReqVO pageReqVO) {
        Page<TransferPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = transferMapper.selectPage(page, pageReqVO);
        Long total = transferMapper.selectPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

    // ==================== 子表（货品转移明细） ====================

    @Override
    public List<TransferDetailDO> getTransferDetailListByTransferId(Long transferId) {
        return transferDetailMapper.selectListByTransferId(transferId);
    }


    @Transactional(rollbackFor = Exception.class)
    public void createTransferDetailList(Long transferId, List<TransferDetailInputVO> list) {
        List<TransferDetailDO> createList = new ArrayList<>();
        list.forEach(detail -> {
            if (detail.getToWareHouseLocationId() == null) {
                throw exception(TO_WARE_HOUSE_LOCATION_DOES_NOT_EXIST);
            }
            ProductWareHouseLocationDO productWareHouseLocationDO = validateTransferDetailExists(detail.getProducerId(),
                    detail.getProductId(), detail.getProductionDate(), detail.getFromWareHouseLocationId());
            if (productWareHouseLocationDO.getAvailable() < detail.getQuantity() ||
                    productWareHouseLocationDO.getScatteredAvailable() < detail.getScatteredQuantity()) {
                throw exception(TRANSFER_QUANTITY_EXCEEDS_AVAILABLE_QUANTITY);
            }
            TransferDetailDO createBean = BeanUtils.toBean(detail, TransferDetailDO.class);
            createBean.setId(null).setTransferId(transferId);
            createList.add(createBean);
        });
        transferDetailMapper.insertBatch(createList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteTransferDetailByTransferId(Long transferId) {
        transferDetailMapper.deleteByTransferId(transferId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateTransferDetailList(Long transferId, List<TransferDetailInputVO> list) {
        deleteTransferDetailByTransferId(transferId);
		createTransferDetailList(transferId, list);
    }

    private ProductWareHouseLocationDO validateTransferDetailExists(Long producerId, Long productId, String productionDate, Long fromWareHouseLocationId) {
        ProductWareHouseLocationDO productWareHouseLocationDO = productWareHouseLocationMapper.selectByConditions(producerId, productId, productionDate, fromWareHouseLocationId);
        if (Objects.isNull(productWareHouseLocationDO)) {
            throw exception(FROM_WARE_HOUSE_LOCATION_DOES_NOT_EXIST);
        }
        return productWareHouseLocationDO;
    }

}