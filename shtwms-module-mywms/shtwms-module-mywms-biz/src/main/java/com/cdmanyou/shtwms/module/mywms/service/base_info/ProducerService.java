package com.cdmanyou.shtwms.module.mywms.service.base_info;

import javax.validation.*;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProducerDO;

/**
 * 货主管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ProducerService {

    /**
     * 创建货主管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid ProducerSaveReqVO createReqVO);

    /**
     * 更新货主管理
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid ProducerSaveReqVO updateReqVO);

    /**
     * 删除货主管理
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 获得货主管理
     *
     * @param id 编号
     * @return 货主管理
     */
    ProducerDO get(Long id);

    /**
     * 获得货主管理分页
     *
     * @param pageReqVO 分页查询
     * @return 货主管理分页
     */
    PageResult<ProducerDO> getPage(ProducerPageReqVO pageReqVO);
    // ... 原有方法

    /**
     * 启用货主
     *
     * @param id 货主ID
     */
    void enableProducer(Long id);

    /**
     * 停用货主
     *
     * @param id 货主ID
     */
    void disableProducer(Long id);



}