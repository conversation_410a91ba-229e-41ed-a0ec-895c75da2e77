package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer.ProducerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProducerDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 货主管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProducerMapper extends BaseMapperX<ProducerDO> {

    default PageResult<ProducerDO> selectPage(ProducerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProducerDO>()
                .eqIfPresent(ProducerDO::getCode, reqVO.getCode())
                .likeIfPresent(ProducerDO::getName, reqVO.getName())
                .likeIfPresent(ProducerDO::getShortName, reqVO.getShortName())
                .eqIfPresent(ProducerDO::getType, reqVO.getType())
                .likeIfPresent(ProducerDO::getEnglishName, reqVO.getEnglishName())
                .eqIfPresent(ProducerDO::getBusinessLicense, reqVO.getBusinessLicense())
                .eqIfPresent(ProducerDO::getDescription, reqVO.getDescription())
                .likeIfPresent(ProducerDO::getContactPerson, reqVO.getContactPerson()) // 改为模糊查询                .eqIfPresent(ProducerDO::getContactPhone, reqVO.getContactPhone())
                .eqIfPresent(ProducerDO::getContactEmail, reqVO.getContactEmail())
                .eqIfPresent(ProducerDO::getRegion, reqVO.getRegion())
                .eqIfPresent(ProducerDO::getCreditLimit, reqVO.getCreditLimit())
                .eqIfPresent(ProducerDO::getSettlementMethod, reqVO.getSettlementMethod())
                .eqIfPresent(ProducerDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ProducerDO::getCooperationStatus, reqVO.getCooperationStatus())
                .betweenIfPresent(ProducerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProducerDO::getId));
    }

}