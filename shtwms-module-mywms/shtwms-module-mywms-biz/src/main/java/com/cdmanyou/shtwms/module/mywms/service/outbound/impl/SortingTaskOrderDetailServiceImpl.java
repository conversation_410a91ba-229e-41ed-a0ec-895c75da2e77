package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.SortingTaskOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.outbound.SortingTaskOrderDetailService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.SORTING_TASK_ORDER_DETAIL_NOT_EXISTS;

/**
 * 分拣任务单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SortingTaskOrderDetailServiceImpl implements SortingTaskOrderDetailService {

    @Resource
    private SortingTaskOrderDetailMapper sortingTaskOrderDetailMapper;

    @Override
    public Long createSortingTaskOrderDetail(SortingTaskOrderDetailSaveReqVO createReqVO) {
        // 插入
        SortingTaskOrderDetailDO sortingTaskOrderDetail = BeanUtils.toBean(createReqVO, SortingTaskOrderDetailDO.class);
        sortingTaskOrderDetailMapper.insert(sortingTaskOrderDetail);
        // 返回
        return sortingTaskOrderDetail.getId();
    }

    @Override
    public void updateSortingTaskOrderDetail(SortingTaskOrderDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateSortingTaskOrderDetailExists(updateReqVO.getId());
        // 更新
        SortingTaskOrderDetailDO updateObj = BeanUtils.toBean(updateReqVO, SortingTaskOrderDetailDO.class);
        sortingTaskOrderDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteSortingTaskOrderDetail(Long id) {
        // 校验存在
        validateSortingTaskOrderDetailExists(id);
        // 删除
        sortingTaskOrderDetailMapper.deleteById(id);
    }

    private void validateSortingTaskOrderDetailExists(Long id) {
        if (sortingTaskOrderDetailMapper.selectById(id) == null) {
            throw exception(SORTING_TASK_ORDER_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public SortingTaskOrderDetailDO getSortingTaskOrderDetail(Long id) {
        return sortingTaskOrderDetailMapper.selectById(id);
    }

    @Override
    public PageResult<SortingTaskOrderDetailDO> getSortingTaskOrderDetailPage(SortingTaskOrderDetailPageReqVO pageReqVO) {
        return sortingTaskOrderDetailMapper.selectPage(pageReqVO,null);
    }

}