package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 出库单详情和产品货位关联 DO
 *
 * <AUTHOR>
 */
@TableName("outbound_order_detail_product_warehouse")
@KeySequence("outbound_order_detail_product_warehouse_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetailProductWarehouseDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 出库详情单id
     */
    private Long outboundDetailId;
    /**
     * 产品货位关系表id
     */
    private Long productWarehouseId;
    /**
     * 产品整件数
     */
    private Integer productOverallCount;
    /**
     * 产品零散数
     */
    private Integer productScatteredCount;
    /**
     * 货位id
     */
    private Long warehouseLocationId;
    /**
     * 仓库id
     */
    private Long warehouseId;


    /**
     * 从总体到分散计数
     */
    private Integer overallScatteredCount;

}