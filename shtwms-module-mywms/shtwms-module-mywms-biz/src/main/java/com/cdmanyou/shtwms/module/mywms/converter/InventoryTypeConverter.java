package com.cdmanyou.shtwms.module.mywms.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class InventoryTypeConverter implements Converter<Integer> {
    
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class; // 处理 Integer 类型字段
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING; // 导出为字符串类型
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, 
                                              ExcelContentProperty contentProperty,
                                              GlobalConfiguration globalConfiguration) {
        // 数值转中文
        String cellValue = "未知类型";
        if (value == null) return new WriteCellData<>("");
        
        switch (value) {
            case 0:
                cellValue = "全盘";
                break;
            case 1:
                cellValue = "动盘";
                break;
            default:
                cellValue = "未定义(" + value + ")";
        }
        return new WriteCellData<>(cellValue);
    }
}