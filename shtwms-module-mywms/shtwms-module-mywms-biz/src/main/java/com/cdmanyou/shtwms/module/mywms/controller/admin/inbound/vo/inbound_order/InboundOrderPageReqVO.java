package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 入库单主分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InboundOrderPageReqVO extends PageParam {

    @Schema(description = "入库单号")
    private String orderCode;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "入库单状态（0草稿,1已提交,2作废）", example = "2")
    private Integer inboundOrderStatus;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品名称", example = "芋艿")
    private String productName;

    @Schema(description = "搜索开始时间")
    private String searchStartTime;

    @Schema(description = "搜索结束时间")
    private String searchEndTime;

}