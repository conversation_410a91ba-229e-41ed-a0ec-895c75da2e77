package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 货品转移主分页 Request VO")
@Data
public class TransferPageRespVO {

    @Schema(description = "转移单ID")
    private Long id;

    @Schema(description = "转移日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate transferDate;

    @Schema(description = "转移单号")
    private String transferCode;

    @Schema(description = "转移数量")
    private Integer totalQuantity;

    @Schema(description = "转移零散数量")
    private Integer totalScatteredQuantity;

    @Schema(description = "状态（0待转移，1转移成功，2转移失败）", example = "2")
    private Integer status;

    @Schema(description = "操作人员ID")
    private String transferUser;

    @Schema(description = "转移人员名称")
    private String transferUserName;

}