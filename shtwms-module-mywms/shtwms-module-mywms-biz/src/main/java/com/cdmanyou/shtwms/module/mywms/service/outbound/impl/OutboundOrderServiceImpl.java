package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.collection.CollectionUtils;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OrderDetailProductWarehouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OrderDetailProductWarehouseMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductService;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.bo.ProductAndHouseBO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.bo.ProductHouseLocationBO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.OUTBOUND_ORDER_NOT_EXISTS;

/**
 * 出库订单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutboundOrderServiceImpl implements OutboundOrderService {

    @Resource
    private OutboundOrderMapper outboundOrderMapper;

    @Resource
    private OutboundOrderDetailMapper outboundOrderDetailMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Resource
    private OrderDetailProductWarehouseMapper orderDetailProductWarehouseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOutboundOrder(OutboundOrderSaveReqVO createReqVO) {
        // 插入
        OutboundOrderDO outboundOrder = BeanUtils.toBean(createReqVO, OutboundOrderDO.class);
        outboundOrderMapper.insert(outboundOrder);

        // 插入子表
        createOutboundOrderDetailList(outboundOrder.getId(), createReqVO.getOutboundOrderDetails());
        // 返回
        return outboundOrder.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOutboundOrder(OutboundOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateOutboundOrderExists(updateReqVO.getId());
        // 更新
        OutboundOrderDO updateObj = BeanUtils.toBean(updateReqVO, OutboundOrderDO.class);
        outboundOrderMapper.updateById(updateObj);

        // 更新子表
        updateOutboundOrderDetailList(updateReqVO.getId(), updateReqVO.getOutboundOrderDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOutboundOrder(Long id) {
        // 校验存在
        validateOutboundOrderExists(id);
        // 删除
        outboundOrderMapper.deleteById(id);

        // 删除子表
        deleteOutboundOrderDetailByOutboundId(id);
    }

    private void validateOutboundOrderExists(Long id) {
        if (outboundOrderMapper.selectById(id) == null) {
            throw exception(OUTBOUND_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public OutboundOrderRespVO getOutboundOrder(Long id) {
        OutboundOrderDO outboundOrderDO = outboundOrderMapper.selectById(id);
        OutboundOrderRespVO bean = BeanUtils.toBean(outboundOrderDO, OutboundOrderRespVO.class);
        List<OutboundOrderDetailDO> outboundOrderDetailDOS = outboundOrderDetailMapper.selectListByOutboundId(id);
        bean.setOutboundOrderDetailList(outboundOrderDetailDOS);
        return bean;
    }

    @Override
    public PageResult<OutboundOrderPageRespVO> getOutboundOrderPage(OutboundOrderPageReqVO pageReqVO) {
        Page<OutboundOrderPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = outboundOrderMapper.getOutboundOrderPage(page, pageReqVO);
        page.getRecords().forEach(e -> {
            if (e.getOutboundType() != 1) {
                e.setUnitName(e.getProducerName());
                e.setUnitCode(e.getProducerCode());
            } else {
                e.setUnitName(e.getSupplierName());
                e.setUnitCode(e.getSupplierCode());
            }
        });
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    // ==================== 子表（出库订单明细） ====================

    @Override
    public List<OutboundOrderDetailDO> getOutboundOrderDetailListByOutboundId(Long outboundId) {
        return outboundOrderDetailMapper.selectListByOutboundId(outboundId);
    }

    @Override
    public void outboundStatusUpdate(OutboundStatusReqVO outboundStatusReqVO) {
        LambdaUpdateWrapper<OutboundOrderDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Objects.nonNull(outboundStatusReqVO.getOrderStatus()), OutboundOrderDO::getOrderStatus, outboundStatusReqVO.getOrderStatus());
        updateWrapper.set(Objects.nonNull(outboundStatusReqVO.getPickingStatus()), OutboundOrderDO::getPickingStatus, outboundStatusReqVO.getPickingStatus());
        updateWrapper.set(Objects.nonNull(outboundStatusReqVO.getSortingStatus()), OutboundOrderDO::getSortingStatus, outboundStatusReqVO.getSortingStatus());
        if (Objects.nonNull(outboundStatusReqVO.getSendStatus())) {
            updateWrapper.set(OutboundOrderDO::getSendStatus, outboundStatusReqVO.getSendStatus());
            updateWrapper.set(OutboundOrderDO::getSendTime, LocalDateTime.now());
        }
        updateWrapper.eq(OutboundOrderDO::getId, outboundStatusReqVO.getId());
        outboundOrderMapper.update(null, updateWrapper);
    }

    @Override
    public List<OutboundOrderDO> getOutboundListByIds(Collection<Long> outboundIds) {
        if (CollectionUtils.isAnyEmpty(outboundIds)) {
            return new ArrayList<>();
        }
        return outboundOrderMapper.selectByIds(outboundIds);
    }

    private void createOutboundOrderDetailList(Long outboundId, List<OutboundOrderDetailDO> list) {
        List<Long> productIds = list.stream().map(OutboundOrderDetailDO::getProductId).collect(Collectors.toList());
        List<ProductDO> productDOList = productMapper.selectByIds(productIds);
        Map<Long, List<ProductDO>> listMap = productDOList.stream().collect(Collectors.groupingBy(ProductDO::getId));
        for (OutboundOrderDetailDO detailDO : list) {
            detailDO.setOutboundId(outboundId);
            ProductDO productDO = listMap.get(detailDO.getProductId()).get(0);
            Integer shelfLife = productDO.getShelfLife();
            LocalDate expirationDate = detailDO.getProductionDate().plusDays(shelfLife);
            detailDO.setExpirationDate(expirationDate);
            //先存入数据库得到对象主键id
            outboundOrderDetailMapper.insert(detailDO);
            //找到对应货位，扣减货位可用量
            List<ProductAndHouseBO> locationBOS = productWareHouseLocationMapper.selectListByProductIdAndProductionDate(detailDO.getProductId(), detailDO.getProductionDate());
            //库存零散数剩余总量
            int allAvailable = locationBOS.stream().mapToInt(ProductAndHouseBO::getScatteredAvailable).sum();
            Integer simpleCount = detailDO.getSimpleCount();
            Integer pendingQuantity = detailDO.getPendingQuantity();
            List<OrderDetailProductWarehouseDO> outboundProductArray = new ArrayList<>();
            for (ProductAndHouseBO houseBO : locationBOS) {
                if (houseBO.getScatteredAvailable() > 0 || houseBO.getAvailable() > 0) {
                    OrderDetailProductWarehouseDO productWarehouseDO = new OrderDetailProductWarehouseDO();
                    productWarehouseDO.setOutboundDetailId(detailDO.getId());
                    productWarehouseDO.setProductWarehouseId(houseBO.getId());
                    productWarehouseDO.setWarehouseLocationId(houseBO.getWarehouseLocationId());
                    productWarehouseDO.setWarehouseId(houseBO.getWarehouseId());
                    if (simpleCount > 0) {
                        if (allAvailable < simpleCount && houseBO.getAvailable() > 0) {
                            Integer boAvailable = houseBO.getAvailable();
                            Integer boScatteredAvailable = houseBO.getScatteredAvailable();
                            ProductDO byId = productMapper.selectById(houseBO.getProductId());
                            Integer looseQuantity = byId.getLooseQuantity();
                            int needCount = simpleCount / looseQuantity;
                            if (houseBO.getAvailable() > needCount) {
                                productWarehouseDO.setOverallScatteredCount(needCount + 1);
                                houseBO.setAvailable(boAvailable - needCount - 1);
                                houseBO.setScatteredAvailable(boScatteredAvailable + (looseQuantity * (needCount + 1)));
                            } else {
                                houseBO.setAvailable(0);
                                houseBO.setScatteredAvailable(boScatteredAvailable + (looseQuantity * boAvailable));
                                productWarehouseDO.setOverallScatteredCount(boAvailable);
                            }
                        }
                        if (houseBO.getScatteredAvailable() >= simpleCount) {
                            productWarehouseDO.setProductScatteredCount(simpleCount);
                            houseBO.setScatteredAvailable(houseBO.getScatteredAvailable() - simpleCount);
                            houseBO.setScatteredOccupation(houseBO.getScatteredOccupation() + simpleCount);
                            simpleCount = 0;
                        } else {
                            Integer scatteredAvailable = houseBO.getScatteredAvailable();
                            productWarehouseDO.setProductScatteredCount(scatteredAvailable);
                            houseBO.setScatteredOccupation(houseBO.getScatteredOccupation() + scatteredAvailable);
                            houseBO.setScatteredAvailable(0);
                            simpleCount -= scatteredAvailable;
                        }
                    }
                    if (pendingQuantity > 0 && houseBO.getAvailable() > 0) {
                        if (houseBO.getAvailable() >= pendingQuantity) {
                            productWarehouseDO.setProductOverallCount(pendingQuantity);
                            houseBO.setScatteredAvailable(houseBO.getAvailable() - pendingQuantity);
                            houseBO.setScatteredOccupation(houseBO.getOccupation() + pendingQuantity);
                            pendingQuantity = 0;
                        } else {
                            Integer available = houseBO.getAvailable();
                            productWarehouseDO.setProductOverallCount(available);
                            houseBO.setOccupation(houseBO.getOccupation() + available);
                            houseBO.setAvailable(0);
                            simpleCount -= available;
                        }
                    }
                    outboundProductArray.add(productWarehouseDO);
                }
            }
            List<ProductWareHouseLocationDO> wareHouseLocationDOS = BeanUtils.toBean(locationBOS, ProductWareHouseLocationDO.class);
            if (!productWareHouseLocationMapper.updateBatch(wareHouseLocationDOS)) {
                throw new RuntimeException("网络繁忙，请稍后再试");
            }
            if (!CollectionUtils.isAnyEmpty(outboundProductArray)) {
                orderDetailProductWarehouseMapper.insertBatch(outboundProductArray);
            }
        }
    }

    private void updateOutboundOrderDetailList(Long outboundId, List<OutboundOrderDetailDO> list) {
        deleteOutboundOrderDetailByOutboundId(outboundId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createOutboundOrderDetailList(outboundId, list);
    }

    private void deleteOutboundOrderDetailByOutboundId(Long outboundId) {
        List<OutboundOrderDetailDO> outboundOrderDetailDOS = outboundOrderDetailMapper.selectListByOutboundId(outboundId);
        if (!CollectionUtils.isAnyEmpty(outboundOrderDetailDOS)) {
            List<Long> outboundDetailIds = outboundOrderDetailDOS.stream().map(OutboundOrderDetailDO::getId).collect(Collectors.toList());
            List<OrderDetailProductWarehouseDO> orderDetailProductWarehouseDOS = orderDetailProductWarehouseMapper.selectByOutboundDetailIds(outboundDetailIds);
            // 回滚库存量
            for (OrderDetailProductWarehouseDO productWarehouseDO : orderDetailProductWarehouseDOS) {
                ProductWareHouseLocationDO houseLocationDO = productWareHouseLocationMapper.selectById(productWarehouseDO.getProductWarehouseId());
                ProductDO byId = productMapper.selectById(houseLocationDO.getProductId());
                if (productWarehouseDO.getProductOverallCount() > 0 || productWarehouseDO.getOverallScatteredCount() > 0) {
                    Integer available = houseLocationDO.getAvailable();
                    Integer occupation = houseLocationDO.getOccupation();
                    houseLocationDO.setAvailable(available + productWarehouseDO.getProductOverallCount() + productWarehouseDO.getOverallScatteredCount());
                    houseLocationDO.setOccupation(occupation - productWarehouseDO.getProductOverallCount() - productWarehouseDO.getOverallScatteredCount());
                }
                if (productWarehouseDO.getProductScatteredCount() > 0) {
                    int num = productWarehouseDO.getProductScatteredCount();
                    if (productWarehouseDO.getOverallScatteredCount() > 0) {
                        num = productWarehouseDO.getProductScatteredCount() - (productWarehouseDO.getOverallScatteredCount() * byId.getLooseQuantity());
                    }
                    Integer scatteredAvailable = houseLocationDO.getScatteredAvailable();
                    Integer scatteredOccupation = houseLocationDO.getScatteredOccupation();
                    houseLocationDO.setScatteredAvailable(scatteredAvailable + num);
                    houseLocationDO.setScatteredOccupation(scatteredOccupation - num);
                }
                if (productWareHouseLocationMapper.updateById(houseLocationDO) <= 0) {
                    throw new RuntimeException("网络繁忙，请稍后重试");
                }
            }

            orderDetailProductWarehouseMapper.deleteByIds(orderDetailProductWarehouseDOS);
        }
        outboundOrderDetailMapper.deleteByOutboundId(outboundId);
    }

}