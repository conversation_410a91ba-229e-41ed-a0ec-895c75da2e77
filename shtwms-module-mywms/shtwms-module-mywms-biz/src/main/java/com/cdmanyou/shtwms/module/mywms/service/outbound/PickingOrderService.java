package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 总拣单主 Service 接口
 *
 * <AUTHOR>
 */
public interface PickingOrderService {

    /**
     * 创建总拣单主
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPickingOrder(@Valid PickingOrderSaveReqVO createReqVO);

    /**
     * 更新总拣单主
     *
     * @param updateReqVO 更新信息
     */
    void updatePickingOrder(@Valid PickingOrderSaveReqVO updateReqVO);

    /**
     * 删除总拣单主
     *
     * @param id 编号
     */
    void deletePickingOrder(Long id);

    /**
     * 获得总拣单主
     *
     * @param id 编号
     * @return 总拣单主
     */
    PickingOrderDO getPickingOrder(Long id);

    /**
     * 获得总拣单主分页
     *
     * @param pageReqVO 分页查询
     * @return 总拣单主分页
     */
    PageResult<PickingOrderDO> getPickingOrderPage(PickingOrderPageReqVO pageReqVO);

    // ==================== 子表（总拣单明细） ====================

    /**
     * 获得总拣单明细列表
     *
     * @param pickingOrderId 拣货单ID（关联主表）
     * @return 总拣单明细列表
     */
    List<PickingOrderDetailDO> getPickingOrderDetailListByPickingOrderId(Long pickingOrderId);

}