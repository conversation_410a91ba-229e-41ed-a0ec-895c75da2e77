package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area.WareHouseAreaPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area.WareHouseAreaRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area.WareHouseAreaSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.WareHouseAreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 库区管理")
@RestController
@RequestMapping("/mywms/ware-house-area")
@Validated
public class WareHouseAreaController {

    @Resource
    private WareHouseAreaService wareHouseAreaService;

    @PostMapping("/create")
    @Operation(summary = "创建库区管理")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:create')")
    public CommonResult<Long> createWareHouseArea(@Valid @RequestBody WareHouseAreaSaveReqVO createReqVO) {
        return success(wareHouseAreaService.createWareHouseArea(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新库区管理")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:update')")
    public CommonResult<Boolean> updateWareHouseArea(@Valid @RequestBody WareHouseAreaSaveReqVO updateReqVO) {
        wareHouseAreaService.updateWareHouseArea(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库区管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:delete')")
    public CommonResult<Boolean> deleteWareHouseArea(@RequestParam("id") Long id) {
        wareHouseAreaService.deleteWareHouseArea(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库区管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:query')")
    public CommonResult<WareHouseAreaRespVO> getWareHouseArea(@RequestParam("id") Long id) {
        WareHouseAreaDO wareHouseArea = wareHouseAreaService.getWareHouseArea(id);
        return success(BeanUtils.toBean(wareHouseArea, WareHouseAreaRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库区管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:query')")
    public CommonResult<PageResult<WareHouseAreaRespVO>> getWareHouseAreaPage(@Valid WareHouseAreaPageReqVO pageReqVO) {
        PageResult<WareHouseAreaDO> pageResult = wareHouseAreaService.getWareHouseAreaPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WareHouseAreaRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库区管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWareHouseAreaExcel(@Valid WareHouseAreaPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WareHouseAreaDO> list = wareHouseAreaService.getWareHouseAreaPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "库区管理.xls", "数据", WareHouseAreaRespVO.class,
                        BeanUtils.toBean(list, WareHouseAreaRespVO.class));
    }

    @PutMapping("/updatestatus")
    @Operation(summary = "更新库区状态")
    @Parameters({
            @Parameter(name = "id", description = "库区ID", required = true),
            @Parameter(name = "status", description = "状态(1启用/0停用)", required = true)
    })
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:update')")
    public CommonResult<Boolean> updateWareHouseAreaStatus(
            @RequestParam("id") Long id,
            @RequestParam("status") @Min(0) @Max(1) Integer status) {

        wareHouseAreaService.updateWareHouseAreaStatus(id, status);
        return success(true);
    }

    @GetMapping("/getlistbywarehouseid")
    @Operation(summary = "根据仓库ID获取库区列表")
    @Parameter(name = "warehouseId", description = "仓库ID", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:ware-house-area:query')")
    public CommonResult<List<WareHouseAreaDO>> getWareHouseAreaListByWarehouseId(
            @RequestParam("warehouseId") Long warehouseId) {
        List<WareHouseAreaDO> list = wareHouseAreaService.getWareHouseAreaListByWarehouseId(warehouseId);
        return success(list);
    }

}