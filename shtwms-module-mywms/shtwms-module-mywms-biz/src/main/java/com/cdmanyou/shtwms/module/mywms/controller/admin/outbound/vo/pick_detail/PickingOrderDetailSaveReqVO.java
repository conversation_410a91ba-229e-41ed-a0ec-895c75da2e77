package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 总拣单明细新增/修改 Request VO")
@Data
public class PickingOrderDetailSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7290")
    private Long id;

    @Schema(description = "拣货单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "28493")
    @NotNull(message = "拣货单ID（关联主表）不能为空")
    private Long pickingOrderId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    private String unit;

    @Schema(description = "待拣货数量")
    private Integer pendingQuantity;

    @Schema(description = "已拣货数量")
    private Integer pickedQuantity;

    @Schema(description = "差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "拣货库区ID（关联库区表，非必填）", example = "17763")
    private Long areaId;

    @Schema(description = "拣货库位ID（关联货位表，非必填）", example = "22260")
    private Long locationId;

}