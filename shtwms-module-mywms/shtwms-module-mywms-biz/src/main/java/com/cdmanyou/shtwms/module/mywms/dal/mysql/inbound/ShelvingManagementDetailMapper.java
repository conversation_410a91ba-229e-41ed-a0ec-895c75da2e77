package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 上架管理单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ShelvingManagementDetailMapper extends BaseMapperX<ShelvingManagementDetailDO> {

    default PageResult<ShelvingManagementDetailDO> selectPage(ShelvingManagementDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ShelvingManagementDetailDO>()
                .eqIfPresent(ShelvingManagementDetailDO::getShelvingId, reqVO.getShelvingId())
                .eqIfPresent(ShelvingManagementDetailDO::getProductId, reqVO.getProductId())
                .eqIfPresent(ShelvingManagementDetailDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(ShelvingManagementDetailDO::getProductBatchNumber, reqVO.getProductBatchNumber())
                .eqIfPresent(ShelvingManagementDetailDO::getSystemBatchNumber, reqVO.getSystemBatchNumber())
                .betweenIfPresent(ShelvingManagementDetailDO::getProductionDate, reqVO.getProductionDate())
                .eqIfPresent(ShelvingManagementDetailDO::getPendingQuantity, reqVO.getPendingQuantity())
                .eqIfPresent(ShelvingManagementDetailDO::getReceivedQuantity, reqVO.getReceivedQuantity())
                .eqIfPresent(ShelvingManagementDetailDO::getShelvedQuantity, reqVO.getShelvedQuantity())
                .eqIfPresent(ShelvingManagementDetailDO::getTrayId, reqVO.getTrayId())
                .eqIfPresent(ShelvingManagementDetailDO::getWarehouseLocationId, reqVO.getLocationId())
                .betweenIfPresent(ShelvingManagementDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ShelvingManagementDetailDO::getId));
    }

    default List<ShelvingManagementDetailDO> selectListByShelvingId(Long shelvingId) {
        return selectList(ShelvingManagementDetailDO::getShelvingId, shelvingId);
    }

    default int deleteByShelvingId(Long shelvingId) {
        return delete(ShelvingManagementDetailDO::getShelvingId, shelvingId);
    }
}