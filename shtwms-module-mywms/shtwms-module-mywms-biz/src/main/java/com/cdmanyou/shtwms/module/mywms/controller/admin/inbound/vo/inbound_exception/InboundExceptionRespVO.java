package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 入库异常处理单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InboundExceptionRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19659")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "关联单据号（收货登记单号/上架单号）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("关联单据号（收货登记单号/上架单号）")
    private String relatedOrderCode;

    @Schema(description = "异常类型", example = "2")
    @ExcelProperty("异常类型")
    private String exceptionType;

    @Schema(description = "处理方案（非必填）")
    @ExcelProperty("处理方案（非必填）")
    private String solution;

    @Schema(description = "处理结果（非必填）")
    @ExcelProperty("处理结果（非必填）")
    private String result;

    @Schema(description = "状态", example = "1")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "处理人（非必填）")
    @ExcelProperty("处理人（非必填）")
    private String handler;

    @Schema(description = "处理时间（非必填）")
    @ExcelProperty("处理时间（非必填）")
    private LocalDateTime handleTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}