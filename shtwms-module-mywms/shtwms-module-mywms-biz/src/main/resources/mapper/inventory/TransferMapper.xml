<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferMapper">

    <select id="getMaxSequence" resultType="java.lang.Long">
        SELECT
            MAX(CAST(SUBSTRING(transfer_code, -6) AS UNSIGNED))
        FROM
            goods_transfer
        WHERE
            transfer_code LIKE CONCAT('ZY', #{dateStr}, '%')
    </select>

    <resultMap id="TransferRespMap" type="com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferRespVO">
        <id property="id" column="id"/>
        <result property="transferCode" column="transfer_code"/>
        <result property="transferDate" column="transfer_date"/>
        <result property="totalQuantity" column="total_quantity"/>
        <result property="totalScatteredQuantity" column="total_scattered_quantity"/>
        <result property="status" column="status"/>
        <result property="transferUserName" column="transfer_user_name"/>
        <result property="transferTime" column="transfer_time"/>
        <collection property="transferDetails"
                    ofType="com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailRespVO">
            <id property="id" column="detail_id"/>
            <result property="producerId" column="producer_id"/>
            <result property="producerName" column="producer_name"/>
            <result property="productId" column="product_id"/>
            <result property="productCode" column="product_code"/>
            <result property="productName" column="product_name"/>
            <result property="specification" column="specification"/>
            <result property="unit" column="unit"/>
            <result property="productionDate" column="production_date"/>
            <result property="quantity" column="quantity"/>
            <result property="scatteredQuantity" column="scattered_quantity"/>
            <result property="fromWareHouseLocationId" column="from_ware_house_location_id"/>
            <result property="fromWareHouseLocationCode" column="from_ware_house_location_code"/>
            <result property="toWareHouseLocationId" column="to_ware_house_location_id"/>
            <result property="toWareHouseLocationCode" column="to_ware_house_location_code"/>
        </collection>
    </resultMap>

    <select id="getTransferByCode" resultMap="TransferRespMap">
        SELECT
            t.id,
            t.transfer_code,
            t.transfer_date,
            t.total_quantity,
            t.total_scattered_quantity,
            t.status,
            su.nickname AS transfer_user_name,
            t.transfer_time,
            td.id AS detail_id,
            td.producer_id,
            pr.name AS producer_name,
            td.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            td.production_date,
            td.quantity,
            td.scattered_quantity,
            wl.id AS from_ware_house_location_id,
            wl.code AS from_ware_house_location_code,
            wl2.id AS to_ware_house_location_id,
            wl2.code AS to_ware_house_location_code
        FROM
            goods_transfer t
        LEFT JOIN
            system_users su ON t.transfer_user = su.id
        LEFT JOIN
            goods_transfer_detail td ON td.transfer_id = t.id AND td.deleted = 0
        LEFT JOIN
            producer pr ON td.producer_id = pr.id
        LEFT JOIN
            product p ON td.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON td.from_ware_house_location_id = wl.id
        LEFT JOIN
            warehouse_location wl2 ON td.to_ware_house_location_id = wl2.id
        WHERE
            t.transfer_code = #{code}
    </select>


    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageRespVO">
        SELECT
            t.id,
            t.transfer_date,
            t.transfer_code,
            t.total_quantity,
            t.total_scattered_quantity,
            t.status,
            t.transfer_user,
            su.nickname AS transfer_user_name
        FROM
            goods_transfer t
        LEFT JOIN
            system_users su ON t.transfer_user = su.id
        LEFT JOIN
            goods_transfer_detail td ON td.transfer_id = t.id AND td.deleted = 0
        LEFT JOIN
            warehouse_location wl1 ON wl1.id = td.from_ware_house_location_id
        LEFT JOIN
            warehouse w1 ON wl1.warehouse_id = w1.id
        LEFT JOIN
            warehouse_location wl2 ON wl2.id = td.to_ware_house_location_id
        LEFT JOIN
            warehouse w2 ON wl2.warehouse_id = w2.id
        WHERE
            t.deleted = 0
        <if test="reqVO.transferCode != null and reqVO.transferCode != ''">
            AND t.transfer_code LIKE CONCAT('%', #{reqVO.transferCode}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND (w1.id = #{reqVO.warehouseId} OR w2.id = #{reqVO.warehouseId})
        </if>
        <if test="reqVO.status != null">
            AND t.status = #{reqVO.status}
        </if>
        <if test="reqVO.transferUserName != null and reqVO.transferUserName != ''">
            AND su.nickname LIKE CONCAT('%', #{reqVO.transferUserName}, '%')
        </if>
        <if test="reqVO.transferSearchStartTime != null and reqVO.transferSearchStartTime != ''">
            AND t.transfer_date &gt;= #{reqVO.transferSearchStartTime}
        </if>
        <if test="reqVO.transferSearchEndTime != null and reqVO.transferSearchEndTime != ''">
            AND t.transfer_date &lt;= #{reqVO.transferSearchEndTime}
        </if>
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            COUNT(t.id)
        FROM
            goods_transfer t
        LEFT JOIN
            system_users su ON t.transfer_user = su.id
        LEFT JOIN
            goods_transfer_detail td ON td.transfer_id = t.id AND td.deleted = 0
        LEFT JOIN
            warehouse_location wl1 ON wl1.id = td.from_ware_house_location_id
        LEFT JOIN
            warehouse w1 ON wl1.warehouse_id = w1.id
        LEFT JOIN
            warehouse_location wl2 ON wl2.id = td.to_ware_house_location_id
        LEFT JOIN
            warehouse w2 ON wl2.warehouse_id = w2.id
        WHERE
            t.deleted = 0
        <if test="reqVO.transferCode != null and reqVO.transferCode != ''">
            AND t.transfer_code LIKE CONCAT('%', #{reqVO.transferCode}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND (w1.id = #{reqVO.warehouseId} OR w2.id = #{reqVO.warehouseId})
        </if>
        <if test="reqVO.status != null">
            AND t.status = #{reqVO.status}
        </if>
        <if test="reqVO.transferUserName != null and reqVO.transferUserName != ''">
            AND su.nickname LIKE CONCAT('%', #{reqVO.transferUserName}, '%')
        </if>
        <if test="reqVO.transferSearchStartTime != null and reqVO.transferSearchStartTime != ''">
            AND t.transfer_date &gt;= #{reqVO.transferSearchStartTime}
        </if>
        <if test="reqVO.transferSearchEndTime != null and reqVO.transferSearchEndTime != ''">
            AND t.transfer_date &lt;= #{reqVO.transferSearchEndTime}
        </if>
    </select>

</mapper>