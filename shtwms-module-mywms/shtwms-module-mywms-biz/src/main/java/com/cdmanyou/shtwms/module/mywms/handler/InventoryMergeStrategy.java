package com.cdmanyou.shtwms.module.mywms.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanRespVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

public class InventoryMergeStrategy implements SheetWriteHandler, RowWriteHandler {
    private final InventoryPlanRespVO header;
    private final int detailStartRow;

    public InventoryMergeStrategy(InventoryPlanRespVO header) {
        this.header = header;
        this.detailStartRow = 6; // 头部占6行（0-5），第6行开始明细
    }

    // 修复方法签名 - 添加 WriteTableHolder 参数
    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder,
                                 WriteSheetHolder writeSheetHolder
                                 ) {
        Sheet sheet = writeSheetHolder.getSheet();

        // 写入头部信息（修正行索引错误）
        Row row0 = sheet.createRow(0);
        row0.createCell(0).setCellValue("盘点计划编号");
        row0.createCell(1).setCellValue(header.getInventoryPlanCode());

        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("计划名称");
        row1.createCell(1).setCellValue(header.getPlanName());

        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("盘点类型");
        row2.createCell(1).setCellValue(header.getInventoryType());

        Row row3 = sheet.createRow(3);
        row3.createCell(0).setCellValue("盘点仓库");
        row3.createCell(1).setCellValue(header.getWarehouseName());

        Row row4 = sheet.createRow(4);
        row4.createCell(0).setCellValue("盘点日期");
        row4.createCell(1).setCellValue(header.getInventoryDate());

        Row row5 = sheet.createRow(5);
        row5.createCell(0).setCellValue("盘点状态");
        row5.createCell(1).setCellValue(header.getStatus());

        // 设置头部样式（扩展到6行）
        CellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        for (int i = 0; i <= 5; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (Cell cell : row) {
                    cell.setCellStyle(headerStyle);
                }
            }
        }

        // 合并单元格（示例）
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 3));
    }

    // 修复方法签名 - 保持原样
    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder,
                                WriteTableHolder writeTableHolder,
                                Row row, Integer relativeRowIndex,
                                Boolean isHead) {
        // 为明细数据添加边框（跳过表头行）
        if (relativeRowIndex != null && relativeRowIndex >= detailStartRow && !isHead) {
            CellStyle style = createBorderStyle(writeSheetHolder.getSheet().getWorkbook());
            for (Cell cell : row) {
                cell.setCellStyle(style);
            }
        }
    }

    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    private CellStyle createBorderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        return style;
    }
}