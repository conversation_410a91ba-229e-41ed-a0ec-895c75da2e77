<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ShelvingManagementMapper">

    <resultMap id="ShelvingManagementInfoMap"
               type="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.ShelvingManagementInfoVO">
            <result property="id" column="id"/>
            <result property="warehouseId" column="warehouse_id"/>
            <result property="warehouseName" column="warehouse_name"/>
            <result property="shelvingCode" column="shelving_code"/>
            <result property="shelvingTime" column="shelving_time"/>
            <result property="shelvingStatus" column="shelving_status"/>
            <result property="receivedQuantityTotal" column="received_quantity_total"/>
            <result property="receivedScatteredQuantityTotal" column="received_scattered_quantity_total"/>
            <result property="shelvedQuantityTotal" column="shelved_quantity_total"/>
            <result property="shelvedScatteredQuantityTotal" column="shelved_scattered_quantity_total"/>
            <result property="receivedWeightTotal" column="received_weight_total"/>
            <result property="shelvedWeightTotal" column="shelved_weight_total"/>
            <result property="shelvingOperator" column="shelving_operator"/>
            <result property="shelvingOperatorName" column="shelving_operator_name"/>
            <result property="remark" column="remark"/>
        <collection property="shelvingManagementDetails"
                    ofType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailInfoVO">
            <id property="id" column="detail_id"/>
            <result property="parentId" column="parent_id"/>
            <result property="shelvingId" column="shelving_id"/>
            <result property="producerId" column="producer_id"/>
            <result property="productId" column="product_id"/>
            <result property="productCode" column="product_code"/>
            <result property="productName" column="product_name"/>
            <result property="specification" column="specification"/>
            <result property="unit" column="unit"/>
            <result property="productBatchNumber" column="product_batch_number"/>
            <result property="systemBatchNumber" column="system_batch_number"/>
            <result property="productionDate" column="production_date"/>
            <result property="pendingQuantity" column="pending_quantity"/>
            <result property="pendingScatteredQuantity" column="pending_scattered_quantity"/>
            <result property="receivedQuantity" column="received_quantity"/>
            <result property="receivedScatteredQuantity" column="received_scattered_quantity"/>
            <result property="shelvedQuantity" column="shelved_quantity"/>
            <result property="shelvedScatteredQuantity" column="shelved_scattered_quantity"/>
            <result property="receivedWeight" column="received_weight"/>
            <result property="shelvedWeight" column="shelved_weight"/>
            <result property="trayId" column="tray_id"/>
            <result property="trayName" column="tray_name"/>
            <result property="locationId" column="warehouse_location_id"/>
            <result property="locationCode" column="location_code"/>
        </collection>
    </resultMap>

    <select id="getShelvingManagementByCode" resultMap="ShelvingManagementInfoMap">
        SELECT
            sm.id,
            sm.warehouse_id,
            w.name AS warehouse_name,
            sm.shelving_code,
            sm.shelving_time,
            sm.shelving_status,
            sm.received_quantity_total,
            sm.received_scattered_quantity_total,
            sm.shelved_quantity_total,
            sm.shelved_scattered_quantity_total,
            sm.received_weight_total,
            sm.shelved_weight_total,
            sm.shelving_operator,
            su.nickname AS shelving_operator_name,
            sm.remark,
            smd.id AS detail_id,
            smd.parent_id,
            smd.shelving_id,
            smd.producer_id,
            smd.product_id,
            smd.product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            smd.product_batch_number,
            smd.system_batch_number,
            smd.production_date,
            smd.pending_quantity,
            smd.pending_scattered_quantity,
            smd.received_quantity,
            smd.received_scattered_quantity,
            smd.shelved_quantity,
            smd.shelved_scattered_quantity,
            smd.received_weight,
            smd.shelved_weight,
            smd.tray_id,
            t.name AS tray_name,
            smd.warehouse_location_id,
            wl.code AS location_code
        FROM
            shelving_management sm
        LEFT JOIN
            warehouse w ON sm.warehouse_id = w.id
        LEFT JOIN
            system_users su ON sm.shelving_operator = su.id
        LEFT JOIN
            shelving_management_detail smd ON smd.shelving_id = sm.id AND smd.deleted = 0
        LEFT JOIN
            product p ON smd.product_id = p.id
        LEFT JOIN
            tray t ON smd.tray_id = t.id
        LEFT JOIN
            warehouse_location wl ON smd.warehouse_location_id = wl.id
        WHERE
            sm.deleted = 0
        AND
            sm.shelving_code = #{code}
    </select>

    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.ShelvingManagementPageRespVO">
        SELECT
            sm.id,
            sm.warehouse_id,
            w.name AS warehouse_name,
            sm.shelving_code,
            sm.shelving_status,
            sm.received_quantity_total,
            sm.shelved_quantity_total,
            (sm.received_quantity_total - sm.shelved_quantity_total) AS diff_quantity,
            sm.received_scattered_quantity_total,
            sm.shelved_scattered_quantity_total,
            (sm.received_scattered_quantity_total - sm.shelved_scattered_quantity_total) AS diff_scattered_quantity,
            sm.shelving_operator,
            su.nickname AS shelving_operator_name,
            sm.received_weight_total,
            sm.shelved_weight_total,
            (sm.received_weight_total - sm.shelved_weight_total) AS diff_weight,
            sm.shelving_time,
            sm.remark
        FROM
            shelving_management sm
        LEFT JOIN
            warehouse w ON sm.warehouse_id = w.id
        LEFT JOIN
            system_users su ON sm.shelving_operator = su.id
        LEFT JOIN
            receipt_registration rr ON rr.shelving_id = sm.id
        WHERE
            sm.deleted = 0
        <if test="reqVO.shelvingCode != null and reqVO.shelvingCode != ''">
            AND sm.shelving_code LIKE CONCAT('%', #{reqVO.shelvingCode}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND rr.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.shelvingStatus != null">
            AND sm.shelving_status = #{reqVO.shelvingStatus}
        </if>
        <if test="reqVO.shelvingOperatorName != null and reqVO.shelvingOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{reqVO.shelvingOperatorName}, '%')
        </if>
        <if test="reqVO.shelvingSearchStartTime != null and reqVO.shelvingSearchStartTime != ''">
            AND sm.shelving_time &gt;= #{reqVO.shelvingSearchStartTime}
        </if>
        <if test="reqVO.shelvingSearchEndTime != null and reqVO.shelvingSearchEndTime != ''">
            AND sm.shelving_time &lt;= #{reqVO.shelvingSearchEndTime}
        </if>
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT sm.id)
        FROM
            shelving_management sm
        LEFT JOIN
            warehouse w ON sm.warehouse_id = w.id
        LEFT JOIN
            system_users su ON sm.shelving_operator = su.id
        LEFT JOIN
            receipt_registration rr ON rr.shelving_id = sm.id
        WHERE
            sm.deleted = 0
        <if test="shelvingCode != null and shelvingCode != ''">
            AND sm.shelving_code LIKE CONCAT('%', #{shelvingCode}, '%')
        </if>
        <if test="warehouseId != null">
            AND rr.warehouse_id = #{warehouseId}
        </if>
        <if test="shelvingStatus != null">
            AND sm.shelving_status = #{shelvingStatus}
        </if>
        <if test="shelvingOperatorName != null and shelvingOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{shelvingOperatorName}, '%')
        </if>
        <if test="shelvingSearchStartTime != null and shelvingSearchStartTime != ''">
            AND sm.shelving_time &gt;= #{shelvingSearchStartTime}
        </if>
        <if test="shelvingSearchEndTime != null and shelvingSearchEndTime != ''">
            AND sm.shelving_time &lt;= #{shelvingSearchEndTime}
        </if>
    </select>

    <select id="getMaxSequence" resultType="java.lang.Long">
        SELECT
            MAX(CAST(SUBSTRING(shelving_code, -6) AS UNSIGNED))
        FROM
            shelving_management
        WHERE
            shelving_code LIKE CONCAT('SJ', #{dateStr}, '%')
    </select>

</mapper>