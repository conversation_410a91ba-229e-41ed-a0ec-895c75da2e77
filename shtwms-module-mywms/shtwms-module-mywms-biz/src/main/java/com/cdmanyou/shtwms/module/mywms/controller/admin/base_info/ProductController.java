package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品管理")
@RestController
@RequestMapping("/mywms/product")
@Validated
public class ProductController {

    @Resource
    private ProductService productService;

    @PostMapping("/create")
    @Operation(summary = "创建商品管理")
    @PreAuthorize("@ss.hasPermission('mywms:product:create')")
    public CommonResult<Long> createProduct(@Valid @RequestBody ProductSaveReqVO createReqVO) {
        return success(productService.createProduct(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品管理")
    @PreAuthorize("@ss.hasPermission('mywms:product:update')")
    public CommonResult<Boolean> updateProduct(@Valid @RequestBody ProductSaveReqVO updateReqVO) {
        productService.updateProduct(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:product:delete')")
    public CommonResult<Boolean> deleteProduct(@RequestParam("id") Long id) {
        productService.deleteProduct(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:product:query')")
    public CommonResult<ProductRespVO> getProduct(@RequestParam("id") Long id) {
        ProductDO product = productService.getProduct(id);
        return success(BeanUtils.toBean(product, ProductRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:product:query')")
    public CommonResult<PageResult<ProductRespVO>> getProductPage(@Valid ProductPageReqVO pageReqVO) {
        PageResult<ProductDO> pageResult = productService.getProductPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductRespVO.class));
    }

    @GetMapping("/enabled-page")
    @Operation(summary = "获取启用状态的商品分页")
    @PreAuthorize("@ss.hasPermission('mywms:product:query')")
    public CommonResult<PageResult<ProductDO>> getEnabledProductPage(@Valid ProductPageReqVO pageVO) {
        return success(productService.getEnabledProductPage(pageVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:product:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductExcel(@Valid ProductPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductDO> list = productService.getProductPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品管理.xls", "数据", ProductRespVO.class,
                        BeanUtils.toBean(list, ProductRespVO.class));
    }

    @PutMapping("/updatestatus")
    @Operation(summary = "更新商品状态",
            description = "启用/停用商品状态，status=1启用，status=0停用")
    @PreAuthorize("@ss.hasPermission('mywms:product:update')")
    public CommonResult<Boolean> updateProductStatus(
            @Parameter(description = "商品ID", required = true)
            @RequestParam("id") Long id,

            @Parameter(description = "状态值(0=停用, 1=启用)", required = true)
            @RequestParam("status") Integer status) {

        // 校验状态值合法性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("非法的状态值：" + status + "，只允许0(停用)或1(启用)");
        }

        if (status == 1) {
            productService.enableProduct(id);
        } else {
            productService.disableProduct(id);
        }
        return success(true);
    }

    @PutMapping("/updatefreezestatus")
    @Operation(summary = "更新商品冻结状态",
            description = "冻结或解冻商品，action=freeze表示冻结，action=unfreeze表示解冻")
    @PreAuthorize("@ss.hasPermission('mywms:product:update')")
    public CommonResult<Boolean> updateProductFreezeStatus(
            @Parameter(description = "商品ID", required = true)
            @RequestParam("id") Long id,

            @Parameter(description = "操作类型(freeze=冻结, unfreeze=解冻)", required = true)
            @RequestParam("action") String action) {

        // 参数校验
        if (!"freeze".equalsIgnoreCase(action) && !"unfreeze".equalsIgnoreCase(action)) {
            throw new IllegalArgumentException("非法的操作类型：" + action + "，只允许freeze或unfreeze");
        }

        // 根据操作类型执行相应的方法
        if ("freeze".equalsIgnoreCase(action)) {
            productService.freezeProduct(id);
        } else {
            productService.unfreezeProduct(id);
        }

        return success(true);
    }

}