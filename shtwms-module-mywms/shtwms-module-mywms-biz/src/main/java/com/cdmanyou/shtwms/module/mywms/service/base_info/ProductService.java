package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product.ProductSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.OutboundStatusReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.ProductSimpleVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutBoundProductDetail;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import org.springframework.transaction.annotation.Transactional;


import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 商品管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductService {

    /**
     * 创建商品管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProduct(@Valid ProductSaveReqVO createReqVO);

    /**
     * 更新商品管理
     *
     * @param updateReqVO 更新信息
     */
    void updateProduct(@Valid ProductSaveReqVO updateReqVO);

    /**
     * 删除商品管理
     *
     * @param id 编号
     */
    void deleteProduct(Long id);

    /**
     * 获得商品管理
     *
     * @param id 编号
     * @return 商品管理
     */
    ProductDO getProduct(Long id);

    /**
     * 获得商品管理分页
     *
     * @param pageReqVO 分页查询
     * @return 商品管理分页
     */
    PageResult<ProductDO> getProductPage(ProductPageReqVO pageReqVO);


    List<ProductSimpleVO> getProductSimpleList(Long producerId);

    OutBoundProductDetail getInfoByProductOutboundInfo(String productId, Integer outboundNumber);

    List<ProductDO> selectArrayByIds(Collection<Long> productIds);

    @Transactional(rollbackFor = Exception.class)
    void enableProduct(Long id);

    @Transactional(rollbackFor = Exception.class)
    void disableProduct(Long id);

    PageResult<ProductDO> getEnabledProductPage(ProductPageReqVO pageReqVO);
    /**
     * 冻结商品
     *
     * @param id 商品ID
     */
    void freezeProduct(Long id);

    /**
     * 解冻商品
     *
     * @param id 商品ID
     */
    void unfreezeProduct(Long id);
}