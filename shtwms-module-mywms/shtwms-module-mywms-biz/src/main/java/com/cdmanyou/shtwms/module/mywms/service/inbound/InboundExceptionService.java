package com.cdmanyou.shtwms.module.mywms.service.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 入库异常处理单主 Service 接口
 *
 * <AUTHOR>
 */
public interface InboundExceptionService {

    /**
     * 创建入库异常处理单主
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInboundException(@Valid InboundExceptionSaveReqVO createReqVO);

    /**
     * 更新入库异常处理单主
     *
     * @param updateReqVO 更新信息
     */
    void updateInboundException(@Valid InboundExceptionSaveReqVO updateReqVO);

    /**
     * 删除入库异常处理单主
     *
     * @param id 编号
     */
    void deleteInboundException(Long id);

    /**
     * 获得入库异常处理单主
     *
     * @param id 编号
     * @return 入库异常处理单主
     */
    InboundExceptionDO getInboundException(Long id);

    /**
     * 获得入库异常处理单主分页
     *
     * @param pageReqVO 分页查询
     * @return 入库异常处理单主分页
     */
    PageResult<InboundExceptionDO> getInboundExceptionPage(InboundExceptionPageReqVO pageReqVO);

    // ==================== 子表（入库异常处理单明细） ====================

//    /**
//     * 获得入库异常处理单明细列表
//     *
//     * @param exceptionId 异常处理单ID（关联主表）
//     * @return 入库异常处理单明细列表
//     */
//    List<InboundExceptionDetailDO> getExceptionDetailListByExceptionId(Long exceptionId);

}