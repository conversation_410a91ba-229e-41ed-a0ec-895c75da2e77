package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 出库单波次单关联 DO
 *
 * <AUTHOR>
 */
@TableName("wave_outbound_order")
@KeySequence("wave_outbound_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WaveOutboundOrderDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 波次单id
     */
    private Long wellenId;
    /**
     * 出库单id
     */
    private Long outboundOrderId;

}