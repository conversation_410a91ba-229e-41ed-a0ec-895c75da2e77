package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailSubmitVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "入库单提交实体")
@Data
public class InboundOrderSubmitVO {

    @Schema(description = "入库单ID")
    private Long id;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "待入库商品总数")
    private Integer pendingQuantityTotal;

    @Schema(description = "待入库商品零散总数")
    private Integer pendingScatteredQuantityTotal;

    @Schema(description = "入库类型（0采购入库，1退货入库，2其他入库）", example = "2")
    private Integer inboundType;

    @Schema(description = "单位ID")
    private Long unitId;

    @Schema(description = "货主ID（关联货主表）", example = "13373")
    private Long producerId;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "卸货重量(公斤)")
    private BigDecimal unloadingWeight;

    @Schema(description = "卸货费用")
    private BigDecimal unloadingCost;

    @Schema(description = "是否整单冻结（0否1是）")
    private Integer freezeType;

    @Schema(description = "商品明细")
    private List<InboundOrderDetailSubmitVO> inboundOrderDetails;
}
