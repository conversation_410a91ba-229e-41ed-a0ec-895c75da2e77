package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.OutboundOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.OutboundOrderPageRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 出库订单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OutboundOrderMapper extends BaseMapperX<OutboundOrderDO> {

    Page<OutboundOrderPageRespVO> getOutboundOrderPage(@Param("page") Page<OutboundOrderPageRespVO> page, @Param("pageReqVO") OutboundOrderPageReqVO pageReqVO);


}