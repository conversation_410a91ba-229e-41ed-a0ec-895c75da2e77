package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 库区管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WareHouseAreaMapper extends BaseMapperX<WareHouseAreaDO> {

    default List<WareHouseAreaDO> selectListByWarehouseId(Long warehouseId) {
        return selectList(WareHouseAreaDO::getWarehouseId, warehouseId);
    }


    default int deleteByWarehouseId(Long warehouseId) {
        return delete(WareHouseAreaDO::getWarehouseId, warehouseId);
    }

}