package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailStatusReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.SortingTaskOrderDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 分拣任务单明细")
@RestController
@RequestMapping("/mywms/sorting-task-order-detail")
@Validated
public class SortingTaskOrderDetailController {

    @Resource
    private SortingTaskOrderDetailService sortingTaskOrderDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建分拣任务单明细")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order-detail:create')")
    public CommonResult<Long> createSortingTaskOrderDetail(@Valid @RequestBody SortingTaskOrderDetailSaveReqVO createReqVO) {
        return success(sortingTaskOrderDetailService.createSortingTaskOrderDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新分拣任务单明细")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order-detail:update')")
    public CommonResult<Boolean> updateSortingTaskOrderDetail(@Valid @RequestBody SortingTaskOrderDetailSaveReqVO updateReqVO) {
        sortingTaskOrderDetailService.updateSortingTaskOrderDetail(updateReqVO);
        return success(true);
    }


    @PutMapping("/update-status")
    @Operation(summary = "更新分拣任务单明细")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order-detail:update')")
    public CommonResult<Boolean> updateSortingTaskOrderDetailStatus(@RequestBody SortingTaskOrderDetailStatusReqVO updateReqVO) {
        sortingTaskOrderDetailService.updateSortingTaskOrderDetailStatus(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除分拣任务单明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order-detail:delete')")
    public CommonResult<Boolean> deleteSortingTaskOrderDetail(@RequestParam("id") Long id) {
        sortingTaskOrderDetailService.deleteSortingTaskOrderDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得分拣任务单明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order-detail:query')")
    public CommonResult<SortingTaskOrderDetailRespVO> getSortingTaskOrderDetail(@RequestParam("id") Long id) {
        SortingTaskOrderDetailDO sortingTaskOrderDetail = sortingTaskOrderDetailService.getSortingTaskOrderDetail(id);
        return success(BeanUtils.toBean(sortingTaskOrderDetail, SortingTaskOrderDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得分拣任务单明细分页")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order-detail:query')")
    public CommonResult<PageResult<SortingTaskOrderDetailRespVO>> getSortingTaskOrderDetailPage(@Valid SortingTaskOrderDetailPageReqVO pageReqVO) {
        PageResult<SortingTaskOrderDetailDO> pageResult = sortingTaskOrderDetailService.getSortingTaskOrderDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SortingTaskOrderDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出分拣任务单明细 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:sorting-task-order-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSortingTaskOrderDetailExcel(@Valid SortingTaskOrderDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SortingTaskOrderDetailDO> list = sortingTaskOrderDetailService.getSortingTaskOrderDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "分拣任务单明细.xls", "数据", SortingTaskOrderDetailRespVO.class,
                        BeanUtils.toBean(list, SortingTaskOrderDetailRespVO.class));
    }

}