package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 出库订单主 DO
 *
 * <AUTHOR>
 */
@TableName("outbound_order")
@KeySequence("outbound_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundOrderDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 出库单号
     */
    private String outboundCode;
    /**
     * 要求交货日期
     */
    private LocalDateTime requiredDeliveryDate;
    /**
     * 出库类型(0'销售出库',1'调拨出库',2'其他出库')
     */
    private Integer outboundType;
    /**
     * 客户ID（关联客户表）
     */
    private Long customerId;
    /**
     * 客户ID（关联客户表）
     */
    private String customerName;
    /**
     * 出库总量
     */
    private Integer totalPending;
    /**
     * 实质出库总量
     */
    private Integer totalReceived;
    /**
     * 收货人
     */
    private String consignee;
    /**
     * 收货人电话
     */
    private String consigneePhone;
    /**
     * 出库单状态(0'草稿',1'已提交',2'已完成')
     */
    private Integer orderStatus;
    /**
     * 拣货状态(0'待拣货',1'部份拣货',2'已拣货')
     */
    private Integer pickingStatus;
    /**
     * 分拣状态(0'待分拣',1'已完成')
     */
    private Integer sortingStatus;
    /**
     * 承运商（非必填）
     */
    private String carrier;
    /**
     * 运费（非必填）
     */
    private BigDecimal freightCost;
    /**
     * 配送方式（非必填）
     */
    private String deliveryMethod;
    /**
     * 车牌号（非必填）
     */
    private String licensePlate;
    /**
     * 物流单号（非必填）
     */
    private String logisticsNumber;
    /**
     * 出库员
     */
    private String outboundOperator;
    /**
     * 审核人
     */
    private String auditOperator;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    /**
     * 出库方式(0普通出库,1紧急出库)，(出库方式为紧急出库直接生成总检单和分拣单)
     */
    private Integer outboundWay;
    /**
     * 备注（非必填）
     */
    private String remark;
    /**
     * 分拣完成时间
     */
    private LocalDateTime sortingCompleteTime;
    /**
     * 发运时间
     */
    private LocalDateTime sendTime;
    /**
     * 预约提货时间
     */
    private LocalDateTime bookingDeliveryTime;
    /**
     * 出库单总价
     */
    private Long price;
    /**
     * 货主id
     */
    private Long producerId;
    /**
     * 货主名称
     */
    private String producerName;
    /**
     * 货主单号
     */
    private String producerOrderCode;
    /**
     * 发运状态
     */
    private String sendStatus;

}