package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 货架管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ShelfRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6887")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "货架编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("货架编码")
    private String code;

    @Schema(description = "货架名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("货架名称")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "26769")
    @ExcelProperty("所属仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "29479")
    @ExcelProperty("所属库区ID（关联库区表）")
    private Long areaId;

    @Schema(description = "货架类型", example = "2")
    @ExcelProperty("货架类型")
    private String type;

    @Schema(description = "货架面积(㎡)")
    @ExcelProperty("货架面积(㎡)")
    private BigDecimal area;

    @Schema(description = "货架状态(1启用/0停用)", example = "1")
    @ExcelProperty("货架状态(1启用/0停用)")
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "层数")
    @NotEmpty(message = "货架层数不能为空")
    private Integer layers;

    @Schema(description = "列数")
    @NotEmpty(message = "货架列数不能为空")
    private Integer columns;


    @Schema(description = "样式")
    @NotEmpty(message = "货架样式不能为空")
    private Integer style;

    @Schema(description = "储位数")
    @NotEmpty(message = "储位数不能为空")
    private Integer location_count;

}