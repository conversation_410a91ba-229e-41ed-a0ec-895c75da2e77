package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 入库异常处理单主分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InboundExceptionPageReqVO extends PageParam {

    @Schema(description = "关联单据号（收货登记单号/上架单号）")
    private String relatedOrderCode;

    @Schema(description = "异常类型", example = "2")
    private String exceptionType;

    @Schema(description = "状态", example = "1")
    private String status;

    @Schema(description = "处理人（非必填）")
    private String handler;

    @Schema(description = "处理时间（非必填）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] handleTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}