<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper">


    <select id="getInfoByProductOutboundInfo"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutBoundProductDetail">
        SELECT a.*, iod.product_batch_number, ap.*
        from (select pwl.production_date, sum(pwl.available) as product_available, sum(pwl.scattered_available) as product_scattered_available
              from shtwms.product_warehouse_location pwl
              where pwl.product_id = #{productId}
              group by pwl.production_date
              order by pwl.production_date desc limit 1) a
                 inner join shtwms.inbound_order_detail iod
                            on a.production_date = iod.production_date and iod.product_id = #{productId}
                 CROSS JOIN (select p.loose_quantity, p.gross_weight, p.volume ,p.specification
                             from shtwms.product p
                             where p.id = #{productId} limit 1) ap
        where a.product_available >= #{outboundNumber}
    </select>
</mapper>