package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "货品转移 明细创建实体")
@Data
public class TransferDetailInputVO {

    @Schema(description = "货主ID")
    private Long producerId;

    @Schema(description = "货品ID")
    private Long productId;

    @Schema(description = "货品编码")
    private String productCode;

    @Schema(description = "生产日期")
    private String productionDate;

    @Schema(description = "转出货位ID")
    private Long fromWareHouseLocationId;

    @Schema(description = "转入货位ID")
    private Long toWareHouseLocationId;

    @Schema(description = "转移数量")
    private Integer quantity;

    @Schema(description = "转移零散数量")
    private Integer scatteredQuantity;
}
