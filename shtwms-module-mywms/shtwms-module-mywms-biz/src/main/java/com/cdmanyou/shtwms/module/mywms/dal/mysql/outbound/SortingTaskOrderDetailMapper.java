package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 分拣任务单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SortingTaskOrderDetailMapper extends BaseMapperX<SortingTaskOrderDetailDO> {

    default List<SortingTaskOrderDetailDO> selectListBySortingOrderId(Long sortingOrderId) {
        return selectList(SortingTaskOrderDetailDO::getSortingOrderId, sortingOrderId);
    }

    default int deleteBySortingOrderId(Long sortingOrderId) {
        return delete(SortingTaskOrderDetailDO::getSortingOrderId, sortingOrderId);
    }

}