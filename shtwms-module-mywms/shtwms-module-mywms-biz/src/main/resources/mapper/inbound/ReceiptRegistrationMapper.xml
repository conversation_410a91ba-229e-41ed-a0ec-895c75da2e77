<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ReceiptRegistrationMapper">

    <resultMap id="ReceiptRegistrationInfoMap" type="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.ReceiptRegistrationInfoVO">
        <id property="id" column="id"/>
        <result property="receiptCode" column="receipt_code"/>
        <result property="receiptStatus" column="receipt_status"/>
        <result property="inboundOrderId" column="inbound_order_id"/>
        <result property="inboundOrderCode" column="inbound_order_code"/>
        <result property="inboundType" column="inbound_type"/>
        <result property="producerName" column="producer_name"/>
        <result property="producerOrderCode" column="producer_order_code"/>
        <result property="receiptTime" column="receipt_time"/>
        <result property="warehouseName" column="warehouse_name"/>
        <collection property="receiptRegistrationDetails"
                    ofType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailInfoVO">
            <id property="id" column="detail_id"/>
            <result property="inboundOrderDetailId" column="inbound_order_detail_id"/>
            <result property="productId" column="product_id"/>
            <result property="productCode" column="product_code"/>
            <result property="productName" column="product_name"/>
            <result property="specification" column="specification"/>
            <result property="unit" column="unit"/>
            <result property="pendingQuantity" column="pending_quantity"/>
            <result property="pendingScatteredQuantity" column="pending_scattered_quantity"/>
            <result property="receivedQuantity" column="received_quantity"/>
            <result property="receivedScatteredQuantity" column="received_scattered_quantity"/>
            <result property="inboundWeight" column="inbound_weight"/>
            <result property="productBatchNumber" column="product_batch_number"/>
            <result property="systemBatchNumber" column="system_batch_number"/>
            <result property="productionDate" column="production_date"/>
            <result property="receivedWeight" column="received_weight"/>
            <result property="shelfLife" column="shelf_life"/>
            <result property="houseType" column="house_type"/>
        </collection>
    </resultMap>

    <select id="getReceiptRegistrationByCode" resultMap="ReceiptRegistrationInfoMap">
        SELECT
            rr.id,
            rr.receipt_code,
            rr.receipt_status,
            rr.inbound_order_id,
            io.order_code AS inbound_order_code,
            io.inbound_type,
            pr.name AS producer_name,
            io.producer_order_code,
            rr.receipt_time,
            w.name AS warehouse_name,
            rrd.id AS detail_id,
            rrd.inbound_order_detail_id,
            rrd.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            rrd.pending_quantity,
            rrd.pending_scattered_quantity,
            rrd.received_quantity,
            rrd.received_scattered_quantity,
            rrd.inbound_weight,
            rrd.product_batch_number,
            rrd.system_batch_number,
            rrd.production_date,
            rrd.received_weight,
            p.shelf_life,
            rrd.house_type
        FROM
            receipt_registration rr
        LEFT JOIN
            inbound_order io ON io.id = rr.inbound_order_id AND io.deleted = 0
        LEFT JOIN
            producer pr ON pr.id = io.producer_id AND pr.deleted = 0
        LEFT JOIN
            warehouse w ON rr.warehouse_id = w.id AND w.deleted = 0
        LEFT JOIN
            receipt_registration_detail rrd ON rrd.receipt_id = rr.id AND rrd.deleted = 0
        LEFT JOIN
            product p ON p.id = rrd.product_id AND p.deleted = 0
        WHERE
            rr.receipt_code = #{code}
        AND
            rr.deleted = 0
    </select>

    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.ReceiptRegistrationPageRespVO">
        SELECT
            rr.id,
            rr.receipt_code,
            rr.receipt_time,
            rr.inbound_order_id,
            io.order_code AS inbound_order_code,
            w.name AS warehouse_name,
            rr.receipt_status,
            rr.pending_quantity_total,
            rr.received_quantity_total,
            (rr.pending_quantity_total - rr.received_quantity_total) AS diff_quantity,
            rr.pending_scattered_quantity_total,
            rr.received_scattered_quantity_total,
            (rr.pending_scattered_quantity_total - rr.received_scattered_quantity_total) AS diff_scattered_quantity,
            rr.receipt_operator,
            su.nickname AS receipt_operator_name
        FROM
            receipt_registration rr
        LEFT JOIN
            inbound_order io ON rr.inbound_order_id = io.id AND io.deleted = 0
        LEFT JOIN
            producer pr ON io.producer_id = pr.id AND pr.deleted = 0
        LEFT JOIN
            warehouse w ON rr.warehouse_id = w.id AND w.deleted = 0
        LEFT JOIN
            system_users su ON rr.receipt_operator = su.id AND su.deleted = 0
        WHERE
            rr.deleted = 0
        <if test="reqVO.receiptCode != null and reqVO.receiptCode != ''">
            AND rr.receipt_code LIKE CONCAT('%', #{reqVO.receiptCode}, '%')
        </if>
        <if test="reqVO.inboundOrderCode != null and reqVO.inboundOrderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{reqVO.inboundOrderCode}, '%')
        </if>
        <if test="reqVO.receiptStatus != null">
            AND rr.receipt_status = #{reqVO.receiptStatus}
        </if>
        <if test="reqVO.receiptOperatorName != null and reqVO.receiptOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{reqVO.receiptOperatorName}, '%')
        </if>

        <!-- 添加上架状态查询条件 -->

        <if test="reqVO.customerName != null and reqVO.customerName != ''">
            AND pr.name LIKE CONCAT('%', #{reqVO.customerName}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND rr.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.receiptSearchStartTime != null and reqVO.receiptSearchStartTime != ''">
            AND rr.receipt_time &gt;= #{reqVO.receiptSearchStartTime}
        </if>
        <if test="reqVO.receiptSearchEndTime != null and reqVO.receiptSearchEndTime != ''">
            AND rr.receipt_time &lt;= #{reqVO.receiptSearchEndTime}
        </if>
        ORDER BY
            rr.receipt_time DESC
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            COUNT(rr.id)
        FROM
            receipt_registration rr
        LEFT JOIN
            inbound_order io ON rr.inbound_order_id = io.id AND io.deleted = 0
        LEFT JOIN
            producer pr ON io.producer_id = pr.id AND pr.deleted = 0
        LEFT JOIN
            warehouse w ON rr.warehouse_id = w.id AND w.deleted = 0
        LEFT JOIN
            system_users su ON rr.receipt_operator = su.id AND su.deleted = 0
        WHERE
            rr.deleted = 0
        <if test="reqVO.receiptCode != null and reqVO.receiptCode != ''">
            AND rr.receipt_code LIKE CONCAT('%', #{reqVO.receiptCode}, '%')
        </if>
        <if test="reqVO.inboundOrderCode != null and reqVO.inboundOrderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{reqVO.inboundOrderCode}, '%')
        </if>
        <if test="reqVO.receiptStatus != null">
            AND rr.receipt_status = #{reqVO.receiptStatus}
        </if>
        <if test="reqVO.receiptOperatorName != null and reqVO.receiptOperatorName != ''">
            AND su.nickname LIKE CONCAT('%', #{reqVO.receiptOperatorName}, '%')
        </if>

        <!-- 添加上架状态查询条件 -->

        <if test="reqVO.customerName != null and reqVO.customerName != ''">
            AND pr.name LIKE CONCAT('%', #{reqVO.customerName}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND rr.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.receiptSearchStartTime != null and reqVO.receiptSearchStartTime != ''">
            AND rr.receipt_time &gt;= #{reqVO.receiptSearchStartTime}
        </if>
        <if test="reqVO.receiptSearchEndTime != null and reqVO.receiptSearchEndTime != ''">
            AND rr.receipt_time &lt;= #{reqVO.receiptSearchEndTime}
        </if>
        ORDER BY
            rr.receipt_time DESC
    </select>

    <select id="getMaxSequence" resultType="java.lang.Long">
        SELECT
            MAX(CAST(SUBSTRING(receipt_code, -6) AS UNSIGNED))
        FROM
            receipt_registration
        WHERE
            receipt_code LIKE CONCAT('YS', #{dateStr}, '%')
    </select>

    <resultMap id="ShelvingManagementCreateMap" type="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.ShelvingManagementCreateVO">
        <id property="receiptId" column="receipt_order_id"/>
        <result property="inboundOrderId" column="inbound_order_id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="receivedQuantityTotal" column="received_quantity_total"/>
        <result property="receivedScatteredQuantityTotal" column="received_scattered_quantity_total"/>
        <result property="receivedWeightTotal" column="received_weight_total"/>
        <collection property="shelvingManagementDetails"
                    ofType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailCreateVO">
            <id property="receiptDetailId" column="receipt_detail_id"/>
            <result property="producerId" column="producer_id"/>
            <result property="productId" column="product_id"/>
            <result property="productCode" column="product_code"/>
            <result property="productBatchNumber" column="product_batch_number"/>
            <result property="systemBatchNumber" column="system_batch_number"/>
            <result property="productionDate" column="production_date"/>
            <result property="pendingQuantity" column="pending_quantity"/>
            <result property="pendingScatteredQuantity" column="pending_scattered_quantity"/>
            <result property="receivedQuantity" column="received_quantity"/>
            <result property="receivedScatteredQuantity" column="received_scattered_quantity"/>
            <result property="receivedWeight" column="received_weight"/>
            <result property="houseType" column="house_type"/>
        </collection>
    </resultMap>
    <select id="getCreateVOByReceiptId" resultMap="ShelvingManagementCreateMap">
        SELECT
            rr.id AS receipt_order_id,
            rr.inbound_order_id,
            rr.warehouse_id,
            rr.received_quantity_total,
            rr.received_scattered_quantity_total,
            rr.received_weight_total,
            rrd.id AS receipt_detail_id,
            io.producer_id,
            rrd.product_id,
            rrd.product_code,
            rrd.product_batch_number,
            rrd.system_batch_number,
            rrd.production_date,
            rrd.pending_quantity,
            rrd.pending_scattered_quantity,
            rrd.received_quantity,
            rrd.received_scattered_quantity,
            rrd.received_weight,
            rrd.house_type
        FROM
            receipt_registration rr
        LEFT JOIN
            inbound_order io ON rr.inbound_order_id = io.id
        LEFT JOIN
            receipt_registration_detail rrd ON rrd.receipt_id = rr.id AND rrd.deleted = 0
        WHERE
            rr.deleted = 0
        AND
            rr.id = #{receiptId}
    </select>

</mapper>