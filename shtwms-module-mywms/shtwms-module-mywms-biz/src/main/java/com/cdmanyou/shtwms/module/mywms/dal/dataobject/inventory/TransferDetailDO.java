package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 货品转移明细 DO
 *
 * <AUTHOR>
 */
@TableName("goods_transfer_detail")
@KeySequence("goods_transfer_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 转移单ID（关联主表）
     */
    private Long transferId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格
     */
    private String specification;
    /**
     * 单位
     */
    private String unit;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 转移数量
     */
    private Integer quantity;
    /**
     * 转出库位ID（关联库位表）
     */
    private Long fromWareHouseLocationId;
    /**
     * 转出库位ID（关联库位表）
     */
    private Long toWareHouseLocationId;

}