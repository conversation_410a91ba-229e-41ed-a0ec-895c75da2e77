package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.security.core.util.SecurityFrameworkUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.InboundOrderSubmitVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailSubmitVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.ShelvingManagementCreateVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailCreateVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ReceiptRegistrationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ReceiptRegistrationDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.*;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundOrderService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ReceiptRegistrationDetailService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ReceiptRegistrationService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ShelvingManagementService;
import com.cdmanyou.shtwms.module.mywms.util.SystemBatchNumberValidator;
import org.springframework.context.annotation.Lazy;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;
import static com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil.getDecimalFieldSum;
import static com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil.getIntFieldSum;

/**
 * 收货登记单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReceiptRegistrationServiceImpl implements ReceiptRegistrationService {

    @Resource
    private ReceiptRegistrationMapper receiptRegistrationMapper;

    @Resource
    private ReceiptRegistrationDetailMapper receiptRegistrationDetailMapper;

    @Resource
    private InboundOrderMapper inboundOrderMapper;

    @Resource
    private InboundOrderDetailMapper inboundOrderDetailMapper;

    @Resource
    private ShelvingManagementDetailMapper shelvingManagementDetailMapper;

    @Resource
    private ReceiptRegistrationDetailService receiptRegistrationDetailService;

    @Resource
    private InboundOrderService inboundOrderService;

    @Lazy
    @Resource
    private ShelvingManagementService shelvingManagementService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createReceiptRegistration(InboundOrderSubmitVO submitVO) {
        ReceiptRegistrationDO receiptDO = new ReceiptRegistrationDO();
        // 生成收货单号
        receiptDO.setReceiptCode(generateReceiptCode());
        // 插入收货单
        receiptDO.setInboundOrderId(submitVO.getId())
                .setWarehouseId(submitVO.getWarehouseId())
                .setPendingQuantityTotal(submitVO.getPendingQuantityTotal())
                .setPendingScatteredQuantityTotal(submitVO.getPendingScatteredQuantityTotal());
        receiptRegistrationMapper.insert(receiptDO);
        // 插入收货单明细
        receiptRegistrationDetailService.createDetailsByInboundOrderSubmit(receiptDO.getId(), submitVO.getInboundOrderDetails());
        // 返回收货单号
        return receiptDO.getReceiptCode();
    }

    @Lock4j(keys = {"'receipt_registration_code'"}, autoRelease = true)
    private String generateReceiptCode() {
        // 生成收货单日期号
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成序列号
        Long maxSequence = receiptRegistrationMapper.getMaxSequence(dateStr);
        Long newMaxSequence = (maxSequence == null) ? 1L : maxSequence + 1;
        // 格式化序列号
        String sequence = String.format("%06d", newMaxSequence);
        return "YS" + dateStr + sequence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReceiptRegistration(ReceiptRegistrationSaveVO saveReqVO) {
        // 校验
        ReceiptRegistrationDO receiptDO = validateReceiptRegistrationExists(saveReqVO.getId());
        // 校验提交状态
        if (receiptDO.getReceiptStatus() != null && receiptDO.getReceiptStatus() == 1) {
            throw exception(RECEIPT_REGISTRATION_ALREADY_SUBMITTED);
        }
        List<ReceiptRegistrationDetailSaveVO> receiptRegistrationDetails = saveReqVO.getReceiptRegistrationDetails();
        if (CollUtil.isEmpty(receiptRegistrationDetails)) {
            throw exception(RECEIPT_REGISTRATION_SAVE_ERROR_BY_DETAILS_EMPTY);
        }
        // 校验系统批次号是否重复
        validateSystemBatchNumberDuplicate(receiptRegistrationDetails);

        ReceiptRegistrationDO updateObj = new ReceiptRegistrationDO();
        updateObj.setId(saveReqVO.getId());
        updateObj.setReceiptTime(saveReqVO.getReceiptTime());

        if (saveReqVO.getSaveFlag() == 1) {
            // 校验必填字段
            validateReceiptRegistrationDetailsForSubmit(receiptRegistrationDetails);
            // 校验并更新新的系统批次号和生产日期
            validateAndUpdateNewSystemBatchNumber(receiptRegistrationDetails);
            // 更新
            updateObj.setReceiptStatus(1);
            updateObj.setReceiptOperator(SecurityFrameworkUtils.getLoginUserId());
            updateObj.setSubmitOperator(SecurityFrameworkUtils.getLoginUserId());
            updateObj.setSubmitTime(LocalDateTime.now());
            // 获取实收货品总数和零散数量
            updateObj.setReceivedQuantityTotal(getIntFieldSum(receiptRegistrationDetails, ReceiptRegistrationDetailSaveVO::getReceivedQuantity));
            updateObj.setReceivedScatteredQuantityTotal(getIntFieldSum(receiptRegistrationDetails, ReceiptRegistrationDetailSaveVO::getReceivedScatteredQuantity));
            // 更新入库单实收数量
            updateBackInboundOrderDetails(receiptRegistrationDetails);
            // 更新入库单验收完成时间
            inboundOrderMapper.updateReceiptCompleteTime(saveReqVO.getInboundOrderId(), saveReqVO.getReceiptTime());
        }

        // 更新收货单
        receiptRegistrationMapper.updateById(updateObj);
        // 更新收货明细
        receiptRegistrationDetailService.updateDetailsByReceiptRegistrationSubmit(saveReqVO.getId(), receiptRegistrationDetails);
    }


    
    private void validateReceiptRegistrationDetailsForSubmit(List<ReceiptRegistrationDetailSaveVO> receiptRegistrationDetails) {
        for (int i = 0; i < receiptRegistrationDetails.size(); i++) {
            ReceiptRegistrationDetailSaveVO detail = receiptRegistrationDetails.get(i);
            String detailInfo = String.format("第%d条", i + 1);
            if (detail.getReceivedQuantity() == null) {
                throw exception(RECEIPT_REGISTRATION_SAVE_ERROR_BY_RECEIVED_QUANTITY_EMPTY, detailInfo);
            }
            if (detail.getReceivedScatteredQuantity() == null) {
                throw exception(RECEIPT_REGISTRATION_SAVE_ERROR_BY_RECEIVED_SCATTERED_QUANTITY_EMPTY, detailInfo);
            }
            if (detail.getProductionDate() == null) {
                throw exception(RECEIPT_REGISTRATION_SAVE_ERROR_BY_PRODUCTION_DATE_EMPTY, detailInfo);
            }
            if (detail.getReceivedWeight() == null) {
                throw exception(RECEIPT_REGISTRATION_SAVE_ERROR_BY_RECEIVED_WEIGHT_EMPTY, detailInfo);
            }
        }
    }

    private void validateAndUpdateNewSystemBatchNumber(List<ReceiptRegistrationDetailSaveVO> receiptRegistrationDetails) {
        for (ReceiptRegistrationDetailSaveVO detail : receiptRegistrationDetails) {
            // 获取新的生产日期和系统批次号
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            StringBuilder stringBuilder = new StringBuilder();
            StringBuilder newSystemBatchNumber = stringBuilder.append(detail.getProductCode()).append(detail.getProductionDate().format(formatter));
            detail.setSystemBatchNumber(newSystemBatchNumber.toString());
        }
        validateSystemBatchNumberDuplicate(receiptRegistrationDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBackInboundOrderDetails(List<ReceiptRegistrationDetailSaveVO> receiptRegistrationDetails) {
        // 修改新的生产日期和系统批次号
        for (ReceiptRegistrationDetailSaveVO detailSaveVO : receiptRegistrationDetails) {
            InboundOrderDetailDO updateObj = new InboundOrderDetailDO();
            updateObj.setId(detailSaveVO.getInboundOrderDetailId());
            updateObj.setProductionDate(detailSaveVO.getProductionDate());
            updateObj.setSystemBatchNumber(detailSaveVO.getSystemBatchNumber());
            inboundOrderDetailMapper.updateById(updateObj);
        }
        // 按入库单明细ID分组
        Map<Long, List<ReceiptRegistrationDetailSaveVO>> groupedDetails = receiptRegistrationDetails.stream()
                .collect(Collectors.groupingBy(ReceiptRegistrationDetailSaveVO::getInboundOrderDetailId));
        for (Map.Entry<Long, List<ReceiptRegistrationDetailSaveVO>> entry : groupedDetails.entrySet()) {
            Long inboundOrderDetailId = entry.getKey();
            List<ReceiptRegistrationDetailSaveVO> detailsInGroup = entry.getValue();
            // 查询入库单明细
            InboundOrderDetailDO inboundOrderDetail = inboundOrderDetailMapper.selectById(inboundOrderDetailId);
            if (inboundOrderDetail == null) {
                continue;
            }
            // 校验待收数量合计
            int totalPendingQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSaveVO::getPendingQuantity);
            Integer pendingQuantity = inboundOrderDetail.getPendingQuantity();
            if (pendingQuantity == null) {
                pendingQuantity = 0;
            }
            int totalPendingScatteredQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSaveVO::getPendingScatteredQuantity);
            Integer pendingScatteredQuantity = inboundOrderDetail.getPendingScatteredQuantity();
            if (pendingScatteredQuantity == null) {
                pendingScatteredQuantity = 0;
            }
            if (totalPendingQuantity != pendingQuantity || totalPendingScatteredQuantity != pendingScatteredQuantity) {
                throw exception(INBOUND_ORDER_DETAIL_PENDING_QUANTITY_MISMATCH,
                        inboundOrderDetail.getProductCode(), inboundOrderDetail.getProductionDate());
            }
            // 统计实收数量合计
            int totalReceivedQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSaveVO::getReceivedQuantity);
            int totalReceivedScatteredQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSaveVO::getReceivedScatteredQuantity);
            // 回写入库单明细表
            InboundOrderDetailDO updateObj = new InboundOrderDetailDO();
            updateObj.setId(inboundOrderDetailId);
            updateObj.setReceivedQuantity(totalReceivedQuantity);
            updateObj.setReceivedScatteredQuantity(totalReceivedScatteredQuantity);
            inboundOrderDetailMapper.updateById(updateObj);
        }
    }

    
    private void validateSystemBatchNumberDuplicate(List<ReceiptRegistrationDetailSaveVO> receiptRegistrationDetails) {
        SystemBatchNumberValidator.validateNoDuplicate(receiptRegistrationDetails, ReceiptRegistrationDetailSaveVO::getSystemBatchNumber);
    }

    private ReceiptRegistrationDO validateReceiptRegistrationExists(Long id) {
        ReceiptRegistrationDO receiptDO = receiptRegistrationMapper.selectById(id);
        if (receiptDO == null) {
            throw exception(RECEIPT_REGISTRATION_NOT_EXISTS);
        }
        return receiptDO;
    }

    @Override
    public ReceiptRegistrationInfoVO getReceiptRegistrationByCode(String code) {
        return receiptRegistrationMapper.getReceiptRegistrationByCode(code);
    }

    @Override
    public PageResult<ReceiptRegistrationPageRespVO> getReceiptRegistrationPage(ReceiptRegistrationPageReqVO pageReqVO) {
        // 查询数据列表
        Page<ReceiptRegistrationPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = receiptRegistrationMapper.selectPage(page, pageReqVO);
        // 查询总数
        Long total = receiptRegistrationMapper.selectPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createShelvingManagement(ReceiptRegistrationCreateVO createReqVO) {
        // 校验收货单
        Long warehouseId = validateCreateShelvingRequest(createReqVO);
        // 查询并校验收货单数据
        List<ReceiptRegistrationSubmitVO> receiptRegistrations = queryAndValidateReceiptRegistrations(createReqVO.getReceiptIds());
        // 提取并合并收货明细
        List<ShelvingManagementDetailCreateVO> shelvingDetails = extractAndMergeReceiptDetails(receiptRegistrations);
        // 创建上架单
        Long shelvingId = createShelvingOrder(warehouseId, shelvingDetails);
        // 回写收货单
        updateBackReceiptRegistrationForCreateShelving(createReqVO.getReceiptIds(), shelvingId);
        return shelvingId;
    }

    private Long validateCreateShelvingRequest(ReceiptRegistrationCreateVO createReqVO) {
        if (createReqVO == null || CollUtil.isEmpty(createReqVO.getReceiptIds())) {
            throw exception(CREATE_SHELVING_ERROR_BY_RECEIPT_REGISTRATION_EMPTY);
        }
        List<ReceiptRegistrationDO> receiptRegistrations = receiptRegistrationMapper.selectListByIds(createReqVO.getReceiptIds());
        if (CollUtil.isEmpty(receiptRegistrations)) {
            throw exception(CREATE_SHELVING_ERROR_BY_RECEIPT_REGISTRATION_EMPTY);
        }
        Long warehouseId = receiptRegistrations.get(0).getWarehouseId();
        boolean hasDifferentWarehouseId = receiptRegistrations.stream().anyMatch(r -> !r.getWarehouseId().equals(warehouseId));
        if (hasDifferentWarehouseId) {
            throw exception(CREATE_SHELVING_ERROR_BY_DIFFERENT_WAREHOUSE);
        }
        return warehouseId;
    }

    private List<ReceiptRegistrationSubmitVO> queryAndValidateReceiptRegistrations(List<Long> receiptIds) {
        // 查询
        List<ReceiptRegistrationSubmitVO> registrationSubmitVOS = receiptRegistrationMapper.getReceiptRegistrationSubmitVOListByIds(receiptIds);
        if (CollUtil.isEmpty(registrationSubmitVOS)) {
            throw exception(CREATE_SHELVING_ERROR_BY_RECEIPT_REGISTRATION_EMPTY);
        }
        // 校验
        validateReceiptRegistrationForCreateShelving(registrationSubmitVOS);
        return registrationSubmitVOS;
    }

    private void validateReceiptRegistrationForCreateShelving(List<ReceiptRegistrationSubmitVO> registrationSubmitVOS) {
        // 校验收货状态
        validateReceiptStatus(registrationSubmitVOS);
        // 校验必要字段
        validateRequiredFields(registrationSubmitVOS);
    }

    private void validateReceiptStatus(List<ReceiptRegistrationSubmitVO> registrationSubmitVOS) {
        boolean receiptStatusErrorFlag = registrationSubmitVOS.stream()
                .anyMatch(submit -> submit.getReceiptStatus() == null || submit.getReceiptStatus() != 1);
        if (receiptStatusErrorFlag) {
            throw exception(CREATE_SHELVING_ERROR_BY_RECEIPT_REGISTRATION_STATUS);
        }
    }

    private void validateRequiredFields(List<ReceiptRegistrationSubmitVO> registrationSubmitVOS) {
        for (ReceiptRegistrationSubmitVO submitVO : registrationSubmitVOS) {
            String receiptCode = submitVO.getReceiptCode();
            if (submitVO.getReceivedQuantityTotal() == null) {
                throw exception(CREATE_SHELVING_ERROR_BY_RECEIVED_QUANTITY_TOTAL_EMPTY, receiptCode);
            }
            if (submitVO.getReceivedScatteredQuantityTotal() == null) {
                throw exception(CREATE_SHELVING_ERROR_BY_RECEIVED_SCATTERED_QUANTITY_TOTAL_EMPTY, receiptCode);
            }
        }
    }
    
    private List<ShelvingManagementDetailCreateVO> extractAndMergeReceiptDetails(List<ReceiptRegistrationSubmitVO> receiptRegistrations) {
        // 提取所有收货明细
        List<ReceiptRegistrationDetailSubmitVO> allDetails = receiptRegistrations.stream()
                .flatMap(registration -> registration.getReceiptRegistrationDetails().stream())
                .collect(Collectors.toList());
        // 按组合值分组
        Map<String, List<ReceiptRegistrationDetailSubmitVO>> groupedDetails = allDetails.stream()
                .filter(detail -> StrUtil.isNotBlank(detail.getCombinedValue()))
                .collect(Collectors.groupingBy(ReceiptRegistrationDetailSubmitVO::getCombinedValue));
        // 合并相同分组内的商品明细
        return mergeReceiptDetailsToShelvingDetails(groupedDetails);
    }

    private List<ShelvingManagementDetailCreateVO> mergeReceiptDetailsToShelvingDetails(Map<String, List<ReceiptRegistrationDetailSubmitVO>> groupedDetails) {
        List<ShelvingManagementDetailCreateVO> result = new ArrayList<>();
        for (Map.Entry<String, List<ReceiptRegistrationDetailSubmitVO>> entry : groupedDetails.entrySet()) {
            String combinedValue = entry.getKey();
            List<ReceiptRegistrationDetailSubmitVO> detailsInGroup = entry.getValue();
            if (CollUtil.isEmpty(detailsInGroup)) {
                continue;
            }
            // 取第一个明细作为基础信息
            ReceiptRegistrationDetailSubmitVO firstDetail = detailsInGroup.get(0);
            ShelvingManagementDetailCreateVO mergedDetail = new ShelvingManagementDetailCreateVO();
            // 设置基础信息
            mergedDetail.setProducerId(firstDetail.getProducerId());
            mergedDetail.setProductId(firstDetail.getProductId());
            mergedDetail.setProductCode(firstDetail.getProductCode());
            mergedDetail.setProductBatchNumber(firstDetail.getProductBatchNumber());
            mergedDetail.setSystemBatchNumber(combinedValue.split("_")[0]);
            mergedDetail.setProductionDate(firstDetail.getProductionDate());
            // 计算并设置合计数量
            int totalPendingQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSubmitVO::getPendingQuantity);
            int totalPendingScatteredQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSubmitVO::getPendingScatteredQuantity);
            int totalReceivedQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSubmitVO::getReceivedQuantity);
            int totalReceivedScatteredQuantity = getIntFieldSum(detailsInGroup, ReceiptRegistrationDetailSubmitVO::getReceivedScatteredQuantity);
            BigDecimal totalReceivedWeight = getDecimalFieldSum(detailsInGroup, ReceiptRegistrationDetailSubmitVO::getReceivedWeight);
            // 设置合计后的数量
            mergedDetail.setPendingQuantity(totalPendingQuantity);
            mergedDetail.setPendingScatteredQuantity(totalPendingScatteredQuantity);
            mergedDetail.setReceivedQuantity(totalReceivedQuantity);
            mergedDetail.setReceivedScatteredQuantity(totalReceivedScatteredQuantity);
            mergedDetail.setReceivedWeight(totalReceivedWeight);
            result.add(mergedDetail);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createShelvingOrder(Long warehouseId, List<ShelvingManagementDetailCreateVO> shelvingManagementDetails) {
        ShelvingManagementCreateVO shelvingCreateVO = new ShelvingManagementCreateVO();
        int receivedQuantity = getIntFieldSum(shelvingManagementDetails, ShelvingManagementDetailCreateVO::getReceivedQuantity);
        int receivedScatteredQuantity = getIntFieldSum(shelvingManagementDetails, ShelvingManagementDetailCreateVO::getReceivedScatteredQuantity);
        BigDecimal receivedWeight = getDecimalFieldSum(shelvingManagementDetails, ShelvingManagementDetailCreateVO::getReceivedWeight);
        shelvingCreateVO.setReceivedQuantityTotal(receivedQuantity);
        shelvingCreateVO.setReceivedScatteredQuantityTotal(receivedScatteredQuantity);
        shelvingCreateVO.setReceivedWeightTotal(receivedWeight);
        shelvingCreateVO.setShelvingManagementDetails(shelvingManagementDetails);
        return shelvingManagementService.createManagement(warehouseId, shelvingCreateVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBackReceiptRegistrationForCreateShelving(List<Long> receiptIds, Long shelvingId) {
        // 更新收货单
        updateReceiptRegistrationForCreateShelving(receiptIds, shelvingId);
        // 更新收货单明细
        updateReceiptRegistrationDetailListForCreateShelving(receiptIds, shelvingId);
    }

    private void updateReceiptRegistrationForCreateShelving(List<Long> receiptIds, Long shelvingId) {
        List<ReceiptRegistrationDO> updateList = new ArrayList<>();
        for (Long receiptId : receiptIds) {
            ReceiptRegistrationDO updateObj = new ReceiptRegistrationDO();
            updateObj.setId(receiptId).setShelvingId(shelvingId);
            updateList.add(updateObj);
        }
        receiptRegistrationMapper.updateBatch(updateList);
    }

    private void updateReceiptRegistrationDetailListForCreateShelving(List<Long> receiptIds, Long shelvingId) {
        List<ReceiptRegistrationDetailDO> updateList = new ArrayList<>();
        List<ReceiptRegistrationDetailDO> receiptRegistrationDetails = receiptRegistrationDetailMapper.selectListByReceiptIds(receiptIds);
        List<ShelvingManagementDetailDO> shelvingManagementDetails = shelvingManagementDetailMapper.selectListByShelvingId(shelvingId);
        if (CollUtil.isEmpty(receiptRegistrationDetails) || CollUtil.isEmpty(shelvingManagementDetails)) {
            return;
        }
        Map<String, Long> systemBatchNumberToIdMap = shelvingManagementDetails.stream()
                .collect(Collectors.toMap(ShelvingManagementDetailDO::getSystemBatchNumber, ShelvingManagementDetailDO::getId));
        receiptRegistrationDetails.forEach(receiptDetail -> {
            Long shelvingDetailId = systemBatchNumberToIdMap.get(receiptDetail.getSystemBatchNumber());
            if (shelvingDetailId != null) {
                ReceiptRegistrationDetailDO updateObj = new ReceiptRegistrationDetailDO().setId(receiptDetail.getId()).setShelvingDetailId(shelvingDetailId);
                updateList.add(updateObj);
            }
        });
        receiptRegistrationDetailMapper.updateBatch(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShelvedQuantityByShelvingDetailId(Long shelvingDetailId, Integer shelvedQuantityTotal, Integer shelvedScatteredQuantityTotal, BigDecimal shelvedWeightTotal) {
        List<ReceiptRegistrationDetailDO> registrationDetails =  receiptRegistrationDetailMapper.selectListByShelvingDetailId(shelvingDetailId);
        if (CollUtil.isEmpty(registrationDetails)) {
            throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_RECEIPT_REGISTRATION_DETAILS_EMPTY);
        }
        // 剩余分配量
        int remainingQuantity = shelvedQuantityTotal;
        int remainingScatteredQuantity = shelvedScatteredQuantityTotal;
        BigDecimal remainingWeight = shelvedWeightTotal;
        for (ReceiptRegistrationDetailDO detail : registrationDetails) {
            if (remainingQuantity > 0 && detail.getReceivedQuantity() > 0) {
                int allocated = Math.min(remainingQuantity, detail.getReceivedQuantity());
                detail.setShelvedQuantity(allocated);
                remainingQuantity -= allocated;
            }else {
                detail.setShelvedQuantity(0);
            }

            if (remainingScatteredQuantity > 0 && detail.getReceivedScatteredQuantity() > 0) {
                int allocated = Math.min(remainingScatteredQuantity, detail.getReceivedScatteredQuantity());
                detail.setShelvedScatteredQuantity(allocated);
                remainingScatteredQuantity -= allocated;
            } else {
                detail.setShelvedScatteredQuantity(0);
            }

            if (remainingWeight.compareTo(BigDecimal.ZERO) > 0 && detail.getReceivedWeight().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal allocated = remainingWeight.min(detail.getReceivedWeight());
                detail.setShelvedWeight(allocated);
                remainingWeight = remainingWeight.subtract(allocated);
            } else {
                detail.setShelvedWeight(BigDecimal.ZERO);
            }

            if (remainingQuantity <= 0 && remainingScatteredQuantity <= 0 && remainingWeight.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }
        // 批量更新收货单明细
        receiptRegistrationDetailMapper.updateBatch(registrationDetails);

        // 分组处理
        Map<Long, List<ReceiptRegistrationDetailDO>> groupedDetails = registrationDetails.stream()
                .filter(detail -> detail.getInboundOrderDetailId() != null)
                .collect(Collectors.groupingBy(ReceiptRegistrationDetailDO::getInboundOrderDetailId));
        for (Map.Entry<Long, List<ReceiptRegistrationDetailDO>> entry : groupedDetails.entrySet()) {
            Long inboundOrderDetailId = entry.getKey();
            List<ReceiptRegistrationDetailDO> groupDetails = entry.getValue();
            int shelvedQuantity = getIntFieldSum(groupDetails, ReceiptRegistrationDetailDO::getShelvedQuantity);
            int shelvedScatteredQuantity = getIntFieldSum(groupDetails, ReceiptRegistrationDetailDO::getShelvedScatteredQuantity);
            BigDecimal shelvedWeight = getDecimalFieldSum(groupDetails, ReceiptRegistrationDetailDO::getShelvedWeight);
            inboundOrderService.updateShelvedQuantityByInboundOrderDetailId(inboundOrderDetailId, shelvedQuantity, shelvedScatteredQuantity, shelvedWeight);
        }
    }

}