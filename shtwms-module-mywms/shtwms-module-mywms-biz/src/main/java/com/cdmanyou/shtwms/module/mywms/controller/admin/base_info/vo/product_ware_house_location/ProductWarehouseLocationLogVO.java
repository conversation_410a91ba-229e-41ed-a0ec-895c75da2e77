package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "库存管理操作日志实体")
@Data
public class ProductWarehouseLocationLogVO {

    @Schema(description = "操作类型（0解冻，1冻结）")
    private Integer type;

    @Schema(description = "操作数量")
    private Integer count;

    @Schema(description = "操作零散数量")
    private Integer scatteredCount;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime operateTime;
}
