package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 货品转移主分页 Request VO")
@Data
public class TransferPageReqVO extends PageParam {

    @Schema(description = "转移单号")
    private String transferCode;

    @Schema(description = "转移日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] transferDate;

    @Schema(description = "转移数量")
    private Integer totalQuantity;

    @Schema(description = "状态（0待转移，1转移成功，2转移失败）", example = "2")
    private Integer status;

    @Schema(description = "操作人员ID")
    private String transferUser;

    @Schema(description = "操作时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] transferTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}