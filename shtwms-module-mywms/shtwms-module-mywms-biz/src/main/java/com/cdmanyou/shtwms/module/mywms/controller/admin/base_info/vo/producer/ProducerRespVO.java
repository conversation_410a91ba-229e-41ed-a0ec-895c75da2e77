package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.producer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 货主管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProducerRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18362")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "货主编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("货主编码")
    private String code;

    @Schema(description = "货主名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("货主名称")
    private String name;

    @Schema(description = "货主简称", example = "李四")
    @ExcelProperty("货主简称")
    private String shortName;

    @Schema(description = "货主类型", example = "1")
    @ExcelProperty("货主类型")
    private String type;

    @Schema(description = "货主英文名称", example = "张三")
    @ExcelProperty("货主英文名称")
    private String englishName;

    @Schema(description = "营业执照号")
    @ExcelProperty("营业执照号")
    private String businessLicense;

    @Schema(description = "货主描述", example = "随便")
    @ExcelProperty("货主描述")
    private String description;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("联系人")
    private String contactPerson;

    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    @ExcelProperty("联系邮箱")
    private String contactEmail;

    @Schema(description = "所在地区(省市区)")
    @ExcelProperty("所在地区(省市区)")
    private String region;

    @Schema(description = "信用额度")
    @ExcelProperty("信用额度")
    private BigDecimal creditLimit;

    @Schema(description = "结算方式")
    @ExcelProperty("结算方式")
    private String settlementMethod;

    @Schema(description = "启用状态", example = "2")
    @ExcelProperty("启用状态")
    private String status;

    @Schema(description = "合作状态", example = "2")
    @ExcelProperty("合作状态")
    private String cooperationStatus;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}