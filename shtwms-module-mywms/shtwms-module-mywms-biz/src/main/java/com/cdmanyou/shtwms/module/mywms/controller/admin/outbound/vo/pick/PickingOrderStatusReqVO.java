package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 总拣单主新增/修改 Request VO")
@Data
public class PickingOrderStatusReqVO {

    @NotNull
    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5718")
    private Long id;

    @NotNull
    @Schema(description = "状态(0'待拣货',1'提交',2'作废')")
    private Integer pickingStatus;

}