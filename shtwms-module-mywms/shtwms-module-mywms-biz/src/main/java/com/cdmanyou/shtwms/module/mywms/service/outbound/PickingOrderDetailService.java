package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;

import javax.validation.Valid;

/**
 * 总拣单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PickingOrderDetailService {

    /**
     * 创建总拣单明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPickingOrderDetail(@Valid PickingOrderDetailSaveReqVO createReqVO);

    /**
     * 更新总拣单明细
     *
     * @param updateReqVO 更新信息
     */
    void updatePickingOrderDetail(@Valid PickingOrderDetailSaveReqVO updateReqVO);

    /**
     * 删除总拣单明细
     *
     * @param id 编号
     */
    void deletePickingOrderDetail(Long id);

    /**
     * 获得总拣单明细
     *
     * @param id 编号
     * @return 总拣单明细
     */
    PickingOrderDetailDO getPickingOrderDetail(Long id);

    /**
     * 获得总拣单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 总拣单明细分页
     */
    PageResult<PickingOrderDetailDO> getPickingOrderDetailPage(PickingOrderDetailPageReqVO pageReqVO);

}