package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHousePageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHouseSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 仓库管理 Service 接口
 *
 * <AUTHOR>
 */
public interface WareHouseService {

    /**
     * 创建仓库管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWareHouse(@Valid WareHouseSaveReqVO createReqVO);

    /**
     * 更新仓库管理
     *
     * @param updateReqVO 更新信息
     */
    void updateWareHouse(@Valid WareHouseSaveReqVO updateReqVO);

    /**
     * 删除仓库管理
     *
     * @param id 编号
     */
    void deleteWareHouse(Long id);

    /**
     * 获得仓库管理
     *
     * @param id 编号
     * @return 仓库管理
     */
    WareHouseDO getWareHouse(Long id);

    /**
     * 获得仓库管理分页
     *
     * @param pageReqVO 分页查询
     * @return 仓库管理分页
     */
    PageResult<WareHouseDO> getWareHousePage(WareHousePageReqVO pageReqVO);

    void updateWareHouseStatus(Long id, Integer status);

    List<WareHouseDO> getWareHouseList();

}