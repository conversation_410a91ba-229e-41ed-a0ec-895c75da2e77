package com.cdmanyou.shtwms.module.mywms.service.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.InboundOrderSubmitVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.*;

import javax.validation.Valid;
import java.math.BigDecimal;


/**
 * 收货登记单 Service 接口
 *
 * <AUTHOR>
 */
public interface ReceiptRegistrationService {

    /**
     * 创建收货单 - 入库单提交
     *
     * @param submitVO 创建信息
     * @return 收货单号
     */
    String createReceiptRegistration(@Valid InboundOrderSubmitVO submitVO);

    /**
     * 保存/提交收货单
     *
     * @param saveReqVO 保存信息
     */
    void saveReceiptRegistration(@Valid ReceiptRegistrationSaveVO saveReqVO);

    /**
     * 通过收货单号获取收货单详情
     *
     * @param code 编号
     * @return 收货登记单
     */
    ReceiptRegistrationInfoVO getReceiptRegistrationByCode(String code);

    /**
     * 获得收货单分页
     *
     * @param pageReqVO 分页查询
     * @return 收货单分页
     */
    PageResult<ReceiptRegistrationPageRespVO> getReceiptRegistrationPage(ReceiptRegistrationPageReqVO pageReqVO);

    /**
     * 创建上架单
     *
     * @param createReqVO 创建信息
     * @return 上架单ID
     */
    Long createShelvingManagement(@Valid ReceiptRegistrationCreateVO createReqVO);

    /**
     * 修改收货单明细上架数量 - 上架单提交
     *
     * @param shelvingDetailId 上架单明细ID
     * @param shelvedQuantityTotal 上架数量
     * @param shelvedScatteredQuantityTotal 上架零散数量
     */
    void updateShelvedQuantityByShelvingDetailId(Long shelvingDetailId, Integer shelvedQuantityTotal, Integer shelvedScatteredQuantityTotal, BigDecimal shelvedWeightTotal);
}