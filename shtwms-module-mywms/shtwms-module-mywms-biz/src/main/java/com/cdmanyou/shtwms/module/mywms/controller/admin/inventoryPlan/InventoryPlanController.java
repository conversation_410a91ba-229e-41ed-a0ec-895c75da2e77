package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseAreaMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseMapper;
import com.cdmanyou.shtwms.module.system.api.user.AdminUserApi;
import com.cdmanyou.shtwms.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.*;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import com.cdmanyou.shtwms.module.mywms.service.inventoryplan.InventoryPlanService;

@Tag(name = "管理后台 - 盘点计划主")
@RestController
@RequestMapping("/mywms/inventory-plan")
@Validated
public class InventoryPlanController {

    @Resource
    private InventoryPlanService inventoryPlanService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private WareHouseMapper wareHouseMapper;

    @PostMapping("/create")
    @Operation(summary = "创建盘点计划主")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-plan:create')")
    public CommonResult<Long> createInventoryPlan(@Valid @RequestBody InventoryPlanSaveReqVO createReqVO) {
        return success(inventoryPlanService.createInventoryPlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新盘点计划主")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-plan:update')")
    public CommonResult<Boolean> updateInventoryPlan(@Valid @RequestBody InventoryPlanSaveReqVO updateReqVO) {
        inventoryPlanService.updateInventoryPlan(updateReqVO);
        return success(true);
    }

    @PutMapping("/createInventory")
    @Operation(summary = "创建盘点")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-plan:createInventory')")
    public CommonResult<Boolean> createInventory(@Valid @RequestBody InventoryPlanSaveReqVO updateReqVO) throws Exception {
        inventoryPlanService.createInventory(updateReqVO);
        return success(true);
    }



    @GetMapping("/get")
    @Operation(summary = "获得盘点计划主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-plan:query')")
    public CommonResult<InventoryPlanRespVO> getInventoryPlan(@RequestParam("id") Long id) {
        InventoryPlanDO inventoryPlan = inventoryPlanService.getInventoryPlan(id);
        InventoryPlanRespVO inventoryPlanRespVO = BeanUtils.toBean(inventoryPlan, InventoryPlanRespVO.class);
        Long creator = inventoryPlanRespVO.getCreator();
        CommonResult<AdminUserRespDTO> user = adminUserApi.getUser(creator);
        if (user.isSuccess()) {
            inventoryPlanRespVO.setCreatorName(user.getData().getNickname());
        }
        Long warehouseId = inventoryPlanRespVO.getWarehouseId();
        WareHouseDO wareHouseDO = wareHouseMapper.selectById(warehouseId);
        if (wareHouseDO != null){
            inventoryPlanRespVO.setWarehouseName(wareHouseDO.getName());
        }
        return success(inventoryPlanRespVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得盘点计划主分页")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-plan:query')")
    public CommonResult<PageResult<InventoryPlanRespVO>> getInventoryPlanPage(@Valid InventoryPlanPageReqVO pageReqVO) {
        PageResult<InventoryPlanDO> pageResult = inventoryPlanService.getInventoryPlanPage(pageReqVO);
        PageResult<InventoryPlanRespVO> bean = BeanUtils.toBean(pageResult, InventoryPlanRespVO.class);
        List<InventoryPlanRespVO> list = bean.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (InventoryPlanRespVO inventoryPlanRespVO : list) {
                Long creator = inventoryPlanRespVO.getCreator();
                CommonResult<AdminUserRespDTO> user = adminUserApi.getUser(creator);
                if (user.isSuccess()) {
                    inventoryPlanRespVO.setCreatorName(user.getData().getNickname());
                }
                Long warehouseId = inventoryPlanRespVO.getWarehouseId();
                WareHouseDO wareHouseDO = wareHouseMapper.selectById(warehouseId);
                if (wareHouseDO != null){
                    inventoryPlanRespVO.setWarehouseName(wareHouseDO.getName());
                }
            }
        }
        return success(bean);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出盘点计划主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-plan:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInventoryPlanExcel(@Valid InventoryPlanPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InventoryPlanDO> list = inventoryPlanService.getInventoryPlanPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "盘点详情.xls", "数据", InventoryPlanRespVO.class,
                        BeanUtils.toBean(list, InventoryPlanRespVO.class));
    }

    // ==================== 子表（盘点计划明细） ====================

    @GetMapping("/plan-detail/list-by-inventory-plan-id")
    @Operation(summary = "获得盘点计划明细列表")
    @Parameter(name = "inventoryPlanId", description = "盘点计划ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-plan:query')")
    public CommonResult<List<InventoryPlanDetailDO>> getPlanDetailListByInventoryPlanId(@RequestParam("inventoryPlanId") Long inventoryPlanId) {
        return success(inventoryPlanService.getPlanDetailListByInventoryPlanId(inventoryPlanId));
    }

}