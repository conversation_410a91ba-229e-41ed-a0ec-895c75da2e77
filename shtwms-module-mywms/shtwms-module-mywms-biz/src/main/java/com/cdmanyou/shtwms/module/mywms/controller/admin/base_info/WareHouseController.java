package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHousePageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHouseRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house.WareHouseSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.WareHouseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 仓库管理")
@RestController
@RequestMapping("/mywms/ware-house")
@Validated
public class WareHouseController {

    @Resource
    private WareHouseService wareHouseService;

    @PostMapping("/create")
    @Operation(summary = "创建仓库管理")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:create')")
    public CommonResult<Long> createWareHouse(@Valid @RequestBody WareHouseSaveReqVO createReqVO) {
        return success(wareHouseService.createWareHouse(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仓库管理")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:update')")
    public CommonResult<Boolean> updateWareHouse(@Valid @RequestBody WareHouseSaveReqVO updateReqVO) {
        wareHouseService.updateWareHouse(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仓库管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:delete')")
    public CommonResult<Boolean> deleteWareHouse(@RequestParam("id") Long id) {
        wareHouseService.deleteWareHouse(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仓库管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:query')")
    public CommonResult<WareHouseRespVO> getWareHouse(@RequestParam("id") Long id) {
        WareHouseDO wareHouse = wareHouseService.getWareHouse(id);
        return success(BeanUtils.toBean(wareHouse, WareHouseRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得仓库管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:query')")
    public CommonResult<PageResult<WareHouseRespVO>> getWareHousePage(@Valid WareHousePageReqVO pageReqVO) {
        PageResult<WareHouseDO> pageResult = wareHouseService.getWareHousePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WareHouseRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出仓库管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWareHouseExcel(@Valid WareHousePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WareHouseDO> list = wareHouseService.getWareHousePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "仓库管理.xls", "数据", WareHouseRespVO.class,
                        BeanUtils.toBean(list, WareHouseRespVO.class));
    }

    @PutMapping("/updatestatus")
    @Operation(summary = "更新仓库状态")
    @Parameters({
            @Parameter(name = "id", description = "仓库ID", required = true),
            @Parameter(name = "status", description = "状态(1启用/0停用)", required = true)
    })
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:update')")
    public CommonResult<Boolean> updateWareHouseStatus(
            @RequestParam("id") Long id,
            @RequestParam("status") @Min(0) @Max(1) Integer status) {

        wareHouseService.updateWareHouseStatus(id, status);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有仓库列表")
    @PreAuthorize("@ss.hasPermission('mywms:ware-house:query')")
    public CommonResult<List<WareHouseRespVO>> getWareHouseList() {
        List<WareHouseDO> list = wareHouseService.getWareHouseList();
        // 将 DO 转换为 VO 返回
        return success(BeanUtils.toBean(list, WareHouseRespVO.class));
    }



}