package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 出库订单主新增/修改 Request VO")
@Data
public class OutboundOrderSaveReqVO {

    @Schema(description = "自增ID", example = "23497")
    private Long id;

    @Schema(description = "出库单号")
    private String outboundCode;

    @Schema(description = "要求交货日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime requiredDeliveryDate;

    @Schema(description = "出库类型(0'销售出库',1'退货出库',2'盘盈（亏）出库',3'其他出库')", example = "2")
    private Integer outboundType;

    @Schema(description = "客户ID（关联客户表）", example = "7441")
    private Long customerId;

    @Schema(description = "出库总量")
    private Integer totalPending;

    @Schema(description = "实质出库总量")
    private Integer totalReceived;

    @Schema(description = "收货人")
    private String consignee;

    @Schema(description = "收货人电话")
    private String consigneePhone;

    @Schema(description = "出库单状态(0'草稿',1'已提交',2'已完成',4'作废')", example = "2")
    private Integer orderStatus;

    @Schema(description = "拣货状态(0'待拣货',1'部份拣货',2'已拣货')", example = "2")
    private Integer pickingStatus;

    @Schema(description = "分拣状态(0'待分拣',1'已完成')", example = "1")
    private Integer sortingStatus;

    @Schema(description = "承运商（非必填）")
    private String carrier;

    @Schema(description = "运费（非必填）")
    private BigDecimal freightCost;

    @Schema(description = "配送方式（非必填）")
    private String deliveryMethod;

    @Schema(description = "车牌号（非必填）")
    private String licensePlate;

    @Schema(description = "业户单号")
    private String logisticsNumber;

    @Schema(description = "出库员")
    private String outboundOperator;

    @Schema(description = "审核人")
    private String auditOperator;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime auditTime;

    @Schema(description = "出库方式(0普通出库,1紧急出库)，(出库方式为紧急出库直接生成总检单和分拣单)")
    private Integer outboundWay;

    @Schema(description = "备注（非必填）", example = "你说的对")
    private String remark;

    @Schema(description = "分拣完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime sortingCompleteTime;

    @Schema(description = "发运时间")
@JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime sendTime;

    @Schema(description = "预约提货时间")
@JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime bookingDeliveryTime;

    @Schema(description = "出库单总价", example = "28741")
    private Long price;

    @Schema(description = "货主id", example = "29049")
    private Long producerId;

    @Schema(description = "货主名称", example = "张三")
    private String producerName;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "发运状态", example = "1")
    private String sendStatus;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id", example = "1")
    private Long supplierId;
    /**
     * 客户名称
     */
    @Schema(description = "客户名称", example = "1")
    private String customerName;
    /**
     * 出库零散量
     */
    @Schema(description = "出库零散量", example = "1")
    private Integer totalSimplePending;

    /**
     * 出库体积
     */
    @Schema(description = "出库体积", example = "1")
    private Long volume;
    /**
     * 出库重量
     */
    @Schema(description = "出库重量", example = "1")
    private BigDecimal grossWeight;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id", example = "1")
    private Long warehouseId;
    /**
     * 时间区间(0夜间,1日间)
     */
    @Schema(description = "时间区间(0夜间,1日间)", example = "1")
    private Integer timePeriod;

    @Schema(description = "出库订单明细列表")
    private List<OutboundOrderDetailDO> outboundOrderDetails;

}