package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategoryPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_category.ProductCategorySaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductCategoryDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductCategoryMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductCategoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;

/**
 * 商品类别管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductCategoryServiceImpl implements ProductCategoryService {

    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private ProductMapper productMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableProductCategory(Long id) {
        ProductCategoryDO category = validateProductCategoryExists(id);
        // 幂等性检查：当前已启用则无需操作
        if (category.getStatus() == 1) {
            throw exception(PRODUCT_CATEGORY_ALREADY_ENABLED);
        }
        // 更新状态为启用(1)
        ProductCategoryDO updateObj = new ProductCategoryDO();
        updateObj.setId(id);
        updateObj.setStatus(1);
        productCategoryMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableProductCategory(Long id) {
        ProductCategoryDO category = validateProductCategoryExists(id);
        // 幂等性检查：当前已停用则无需操作
        if (category.getStatus() == 0) {
            throw exception(PRODUCT_CATEGORY_ALREADY_DISABLED);
        }
        // 更新状态为停用(0)
        ProductCategoryDO updateObj = new ProductCategoryDO();
        updateObj.setId(id);
        updateObj.setStatus(0);
        productCategoryMapper.updateById(updateObj);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProductCategory(ProductCategorySaveReqVO createReqVO) {
        // 设置默认父级类别（一级类别）
        if (createReqVO.getParentId() == null) {
            createReqVO.setParentId(0L); // 0 表示根节点/一级类别
        }

        // 新增唯一性校验
        validateProductCategoryUnique(createReqVO.getName(), createReqVO.getCode(), null);

        // 插入
        ProductCategoryDO productCategory = BeanUtils.toBean(createReqVO, ProductCategoryDO.class);
        productCategoryMapper.insert(productCategory);

        // 返回
        return productCategory.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductCategory(ProductCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateProductCategoryExists(updateReqVO.getId());
        // 新增唯一性校验(排除自身)
        validateProductCategoryUnique(updateReqVO.getName(), updateReqVO.getCode(), updateReqVO.getId());

        // 更新
        ProductCategoryDO updateObj = BeanUtils.toBean(updateReqVO, ProductCategoryDO.class);
        productCategoryMapper.updateById(updateObj);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductCategory(Long id) {
        // 校验存在
        validateProductCategoryExists(id);
        // 删除
        productCategoryMapper.deleteById(id);

        // 删除子表
        deleteProductByCategoryId(id);
    }

    private ProductCategoryDO validateProductCategoryExists(Long id) {
        ProductCategoryDO category = productCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(PRODUCT_CATEGORY_NOT_EXISTS);
        }
        return category;
    }

    // 新增唯一性校验方法
    private void validateProductCategoryUnique(String name, String code, Long excludeId) {
        // 校验类别名称唯一性
        QueryWrapper<ProductCategoryDO> nameWrapper = new QueryWrapper<>();
        nameWrapper.eq("name", name)
                .eq("status", 1)
                .ne(excludeId != null, "id", excludeId) // 动态排除当前ID
                .last("LIMIT 1");
        if (productCategoryMapper.selectCount(nameWrapper) > 0) {
            throw exception(PRODUCT_CATEGORY_NAME_DUPLICATE);
        }

        // 校验类别编码唯一性
        QueryWrapper<ProductCategoryDO> codeWrapper = new QueryWrapper<>();
        codeWrapper.eq("code", code)
                .eq("status", 1)
                .ne(excludeId != null, "id", excludeId)
                .last("LIMIT 1");
        if (productCategoryMapper.selectCount(codeWrapper) > 0) {
            throw exception(PRODUCT_CATEGORY_CODE_DUPLICATE);
        }
    }

    @Override
    public ProductCategoryDO getProductCategory(Long id) {
        return productCategoryMapper.selectById(id);
    }

    @Override
    public PageResult<ProductCategoryDO> getProductCategoryPage(ProductCategoryPageReqVO pageReqVO) {
        return productCategoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductCategoryDO> getProductCategoryList() {
        return productCategoryMapper.selectList();
    }

    // ==================== 子表（商品管理） ====================

    @Override
    public List<ProductDO> getProductListByCategoryId(Long categoryId) {
        return productMapper.selectListByCategoryId(categoryId);
    }

    private void createProductList(Long categoryId, List<ProductDO> list) {
        list.forEach(o -> o.setCategoryId(categoryId));
        productMapper.insertBatch(list);
    }

    private void updateProductList(Long categoryId, List<ProductDO> newList) {
        // 1. 获取现有商品列表
        List<ProductDO> existingList = productMapper.selectListByCategoryId(categoryId);

        // 2. 识别需要删除的商品
        Set<Long> newIds = newList.stream()
                .map(ProductDO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<Long> idsToDelete = existingList.stream()
                .map(ProductDO::getId)
                .filter(id -> !newIds.contains(id))
                .collect(Collectors.toList());

        // 3. 删除不再关联的商品
        if (!idsToDelete.isEmpty()) {
            productMapper.deleteBatchIds(idsToDelete);
        }

        // 4. 批量更新/插入商品
        for (ProductDO product : newList) {
            product.setCategoryId(categoryId);
            if (product.getId() != null) {
                productMapper.updateById(product);
            } else {
                productMapper.insert(product);
            }
        }
    }

    private void deleteProductByCategoryId(Long categoryId) {
        productMapper.deleteByCategoryId(categoryId);
    }

}