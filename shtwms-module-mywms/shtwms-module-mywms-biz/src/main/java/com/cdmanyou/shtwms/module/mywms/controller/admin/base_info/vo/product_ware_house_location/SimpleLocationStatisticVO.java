package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "货位统计简略信息")
@Data
public class SimpleLocationStatisticVO {

    @Schema(description = "货位总数")
    private Integer locationTotalCount;

    @Schema(description = "已用货位数")
    private Integer locationOccupationCount;

    @Schema(description = "空闲货位数")
    private Integer locationAvailableCount;

    @Schema(description = "空闲货位占比")
    private BigDecimal locationAvailablePercent;
}
