package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;

import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.InboundExceptionDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.InboundExceptionMapper;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundExceptionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 入库异常处理单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InboundExceptionServiceImpl implements InboundExceptionService {

    @Resource
    private InboundExceptionMapper inboundExceptionMapper;
    @Resource
    private InboundExceptionDetailMapper exceptionDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createInboundException(InboundExceptionSaveReqVO createReqVO) {
        // 插入
        InboundExceptionDO inboundException = BeanUtils.toBean(createReqVO, InboundExceptionDO.class);
        inboundExceptionMapper.insert(inboundException);

        // 插入子表
//        createExceptionDetailList(inboundException.getId(), createReqVO.getExceptionDetails());
        // 返回
        return inboundException.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInboundException(InboundExceptionSaveReqVO updateReqVO) {
        // 校验存在
        validateInboundExceptionExists(updateReqVO.getId());
        // 更新
        InboundExceptionDO updateObj = BeanUtils.toBean(updateReqVO, InboundExceptionDO.class);
        inboundExceptionMapper.updateById(updateObj);

        // 更新子表
//        updateExceptionDetailList(updateReqVO.getId(), updateReqVO.getExceptionDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInboundException(Long id) {
        // 校验存在
        validateInboundExceptionExists(id);
        // 删除
        inboundExceptionMapper.deleteById(id);

        // 删除子表
//        deleteExceptionDetailByExceptionId(id);
    }

    private void validateInboundExceptionExists(Long id) {
        if (inboundExceptionMapper.selectById(id) == null) {
            throw exception(new ErrorCode(500,"对象不存在"));
        }
    }

    @Override
    public InboundExceptionDO getInboundException(Long id) {
        return inboundExceptionMapper.selectById(id);
    }

    @Override
    public PageResult<InboundExceptionDO> getInboundExceptionPage(InboundExceptionPageReqVO pageReqVO) {
        return inboundExceptionMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（入库异常处理单明细） ====================

//    @Override
//    public List<InboundExceptionDetailDO> getExceptionDetailListByExceptionId(Long exceptionId) {
//        return exceptionDetailMapper.selectListByExceptionId(exceptionId);
//    }

//    private void createExceptionDetailList(Long exceptionId, List<InboundExceptionDetailDO> list) {
//        list.forEach(o -> o.setExceptionId(exceptionId));
//        exceptionDetailMapper.insertBatch(list);
//    }

//    private void updateExceptionDetailList(Long exceptionId, List<InboundExceptionDetailDO> list) {
//        deleteExceptionDetailByExceptionId(exceptionId);
//		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
//        createExceptionDetailList(exceptionId, list);
//    }

//    private void deleteExceptionDetailByExceptionId(Long exceptionId) {
//        exceptionDetailMapper.deleteByExceptionId(exceptionId);
//    }

}