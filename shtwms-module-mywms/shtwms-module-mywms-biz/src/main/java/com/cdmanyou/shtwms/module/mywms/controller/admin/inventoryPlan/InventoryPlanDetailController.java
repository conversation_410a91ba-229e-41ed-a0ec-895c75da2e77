package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import com.cdmanyou.shtwms.module.mywms.handler.InventoryMergeStrategy;
import com.cdmanyou.shtwms.module.mywms.service.inventoryplan.InventoryPlanDetailService;
import com.cdmanyou.shtwms.module.mywms.service.inventoryplan.InventoryPlanService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.io.IOException;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 盘点计划明细")
@RestController
@RequestMapping("/mywms/plan-detail")
@Validated
public class InventoryPlanDetailController {

    @Resource
    private InventoryPlanDetailService planDetailService;
    @Resource
    private InventoryPlanService inventoryPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建盘点计划明细")
    @PreAuthorize("@ss.hasPermission('inventory:plan-detail:create')")
    public CommonResult<Long> createPlanDetail(@Valid @RequestBody InventoryPlanDetailSaveReqVO createReqVO) {
        return success(planDetailService.createPlanDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新盘点计划明细")
    @PreAuthorize("@ss.hasPermission('inventory:plan-detail:update')")
    public CommonResult<Boolean> updatePlanDetail(@Valid @RequestBody InventoryPlanDetailSaveReqVO updateReqVO) {
        planDetailService.updatePlanDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除盘点计划明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('inventory:plan-detail:delete')")
    public CommonResult<Boolean> deletePlanDetail(@RequestParam("id") Long id) {
        planDetailService.deletePlanDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得盘点计划明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('inventory:plan-detail:query')")
    public CommonResult<InventoryPlanDetailRespVO> getPlanDetail(@RequestParam("id") Long id) {
        InventoryPlanDetailDO planDetail = planDetailService.getPlanDetail(id);
        return success(BeanUtils.toBean(planDetail, InventoryPlanDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得盘点计划明细分页")
    @PreAuthorize("@ss.hasPermission('inventory:plan-detail:query')")
    public CommonResult<PageResult<InventoryPlanDetailRespVO>> getPlanDetailPage(@Valid InventoryPlanDetailPageReqVO pageReqVO) {
        PageResult<InventoryPlanDetailDO> pageResult = planDetailService.getPlanDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InventoryPlanDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出盘点计划明细 Excel")
    @PreAuthorize("@ss.hasPermission('inventory:plan-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPlanDetailExcel(@Valid InventoryPlanDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InventoryPlanDetailDO> list = planDetailService.getPlanDetailPage(pageReqVO).getList();
        List<InventoryPlanDetailRespVO> bean = BeanUtils.toBean(list, InventoryPlanDetailRespVO.class);

        InventoryPlanDO inventoryPlan = inventoryPlanService.getInventoryPlan(pageReqVO.getInventoryPlanId());
        InventoryPlanRespVO bean1 = BeanUtils.toBean(inventoryPlan, InventoryPlanRespVO.class);
//        // 导出 Excel
//        ExcelUtils.write(response, "盘点计划明细.xls", "数据", InventoryPlanDetailRespVO.class,
//                        BeanUtils.toBean(list, InventoryPlanDetailRespVO.class));


        InventoryExportData inventoryExportData = new InventoryExportData();
        inventoryExportData.setDetails(bean);
        inventoryExportData.setHeader(bean1);
        exportInventory(inventoryExportData,response);
    }


    public void exportInventory(InventoryExportData exportData,
                                HttpServletResponse response) throws IOException {

        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("盘点报告", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 创建ExcelWriter
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new InventoryMergeStrategy(exportData.getHeader()))
                .build();

        // 写入明细数据
        WriteSheet writeSheet = EasyExcel.writerSheet("盘点明细")
                .head(InventoryPlanDetailRespVO.class)
                .build();

        // 设置从第5行开始写入明细（0-based）
        writeSheet.setRelativeHeadRowIndex(6);

        excelWriter.write(exportData.getDetails(), writeSheet);
        excelWriter.finish();


//        // 对文件名进行编码
//        // 拼接完整的文件路径
//        String fullPath = "C:\\Users\\<USER>