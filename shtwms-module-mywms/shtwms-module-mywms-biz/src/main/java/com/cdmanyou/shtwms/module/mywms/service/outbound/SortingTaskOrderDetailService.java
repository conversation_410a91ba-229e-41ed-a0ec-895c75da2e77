package com.cdmanyou.shtwms.module.mywms.service.outbound;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail.SortingTaskOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;

import javax.validation.Valid;

/**
 * 分拣任务单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface SortingTaskOrderDetailService {

    /**
     * 创建分拣任务单明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSortingTaskOrderDetail(@Valid SortingTaskOrderDetailSaveReqVO createReqVO);

    /**
     * 更新分拣任务单明细
     *
     * @param updateReqVO 更新信息
     */
    void updateSortingTaskOrderDetail(@Valid SortingTaskOrderDetailSaveReqVO updateReqVO);

    /**
     * 删除分拣任务单明细
     *
     * @param id 编号
     */
    void deleteSortingTaskOrderDetail(Long id);

    /**
     * 获得分拣任务单明细
     *
     * @param id 编号
     * @return 分拣任务单明细
     */
    SortingTaskOrderDetailDO getSortingTaskOrderDetail(Long id);

    /**
     * 获得分拣任务单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 分拣任务单明细分页
     */
    PageResult<SortingTaskOrderDetailDO> getSortingTaskOrderDetailPage(SortingTaskOrderDetailPageReqVO pageReqVO);

}