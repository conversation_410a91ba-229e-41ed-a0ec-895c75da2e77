package com.cdmanyou.shtwms.module.mywms.service.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.supplier.SupplierPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.supplier.SupplierSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.SupplierDO;

import javax.validation.Valid;

/**
 * 供应商管理 Service 接口
 *
 * <AUTHOR>
 */
public interface SupplierService {

    /**
     * 创建供应商管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSupplier(@Valid SupplierSaveReqVO createReqVO);

    /**
     * 更新供应商管理
     *
     * @param updateReqVO 更新信息
     */
    void updateSupplier(@Valid SupplierSaveReqVO updateReqVO);

    /**
     * 删除供应商管理
     *
     * @param id 编号
     */
    void deleteSupplier(Long id);

    /**
     * 获得供应商管理
     *
     * @param id 编号
     * @return 供应商管理
     */
    SupplierDO getSupplier(Long id);

    /**
     * 获得供应商管理分页
     *
     * @param pageReqVO 分页查询
     * @return 供应商管理分页
     */
    PageResult<SupplierDO> getSupplierPage(SupplierPageReqVO pageReqVO);

    void enableSupplier(Long id);

    void disableSupplier(Long id);


}