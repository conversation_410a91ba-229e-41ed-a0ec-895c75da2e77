package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 商品货位关联表-记录货位上的商品数量新增/修改 Request VO")
@Data
public class ProductWareHouseLocationSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10961")
    private Long id;

    @Schema(description = "入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12637")
    @NotNull(message = "入库单ID不能为空")
    private Long inboundOrderId;

    @Schema(description = "货主ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27126")
    @NotNull(message = "货主ID不能为空")
    private Long producerId;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9107")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @Schema(description = "货位ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30806")
    @NotNull(message = "货位ID不能为空")
    private Long warehouseLocationId;

    @Schema(description = "生产日期")
    private LocalDate productionDate;

    @Schema(description = "商品总数", example = "5930")
    private Integer count;

    @Schema(description = "占用数量")
    private Integer occupation;

    @Schema(description = "可用数量")
    private Integer available;

    @Schema(description = "零散总量", example = "30895")
    private Integer scatteredCount;

    @Schema(description = "零散占用量")
    private Integer scatteredOccupation;

    @Schema(description = "零散可用量")
    private Integer scatteredAvailable;

    @Schema(description = "长度（厘米）")
    private Double length;

    @Schema(description = "宽（厘米）")
    private Double wide;

    @Schema(description = "高（厘米） ")
    private Double high;

    @Schema(description = "体积（立方米）")
    private Double volume;

    @Schema(description = "货位状态(1启用/0停用)")
    private  Integer status;

    @Schema(description = "卸货方式(0叉车卸货，1其他卸货方式)")
    private int unloading_method;
}