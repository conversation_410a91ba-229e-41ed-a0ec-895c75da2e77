package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailCreateVO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "上架单创建实体")
@Data
public class ShelvingManagementCreateVO {

    @Schema(description = "收货单ID")
    private Long receiptId;

    @Schema(description = "入库单ID")
    private Long inboundOrderId;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "待上架总数（实收货品总数）")
    private Integer receivedQuantityTotal;

    @Schema(description = "待上架零散总数（实收货品零散总数）")
    private Integer receivedScatteredQuantityTotal;

    @Schema(description = "待上架总重量（实收货品总重量）")
    private BigDecimal receivedWeightTotal;

    @Schema(description = "上架单明细列表")
    private List<ShelvingManagementDetailCreateVO> shelvingManagementDetails;
}
