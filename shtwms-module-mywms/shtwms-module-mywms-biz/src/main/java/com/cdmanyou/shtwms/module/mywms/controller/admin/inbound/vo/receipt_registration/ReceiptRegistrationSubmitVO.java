package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailSubmitVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ReceiptRegistrationSubmitVO {

    @Schema(description = "收货单ID")
    private Long id;

    @Schema(description = "收货单号")
    private String receiptCode;

    @Schema(description = "收货状态（0草稿，1待上架，2已上架）")
    private Integer receiptStatus;

    @Schema(description = "实收货品总量")
    private Integer receivedQuantityTotal;

    @Schema(description = "实收货品零散总量")
    private Integer receivedScatteredQuantityTotal;

    @Schema(description = "商品明细")
    private List<ReceiptRegistrationDetailSubmitVO> receiptRegistrationDetails;

}
