package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 货品转移明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransferDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4651")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "转移单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "25002")
    @ExcelProperty("转移单ID（关联主表）")
    private Long transferId;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14627")
    @ExcelProperty("商品ID")
    private Long productId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String unit;

    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate productionDate;

    @Schema(description = "转移数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("转移数量")
    private Integer quantity;

    @Schema(description = "转出库位ID（关联库位表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "27693")
    @ExcelProperty("转出库位ID（关联库位表）")
    private Long fromWareHouseLocationId;

    @Schema(description = "转出库位ID（关联库位表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "11086")
    @ExcelProperty("转出库位ID（关联库位表）")
    private Long toWareHouseLocationId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}