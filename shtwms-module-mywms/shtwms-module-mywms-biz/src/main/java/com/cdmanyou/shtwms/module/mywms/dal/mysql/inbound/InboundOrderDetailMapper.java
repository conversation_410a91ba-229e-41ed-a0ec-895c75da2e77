package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 入库单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InboundOrderDetailMapper extends BaseMapperX<InboundOrderDetailDO> {

    default List<InboundOrderDetailDO> selectListByOrderId(Long orderId) {
        return selectList(InboundOrderDetailDO::getInboundOrderId, orderId);
    }

    default void deleteByOrderId(Long orderId) {
        delete(InboundOrderDetailDO::getInboundOrderId, orderId);
    }

}