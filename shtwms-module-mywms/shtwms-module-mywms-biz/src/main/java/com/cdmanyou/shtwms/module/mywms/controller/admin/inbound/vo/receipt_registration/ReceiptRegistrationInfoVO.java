package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailInfoVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "收货单详情实体")
@Data
public class ReceiptRegistrationInfoVO {

    @Schema(description = "收货单ID")
    private Long id;

    @Schema(description = "收货单单号")
    private String receiptCode;

    @Schema(description = "入库单ID")
    private Long inboundOrderId;

    @Schema(description = "入库单单号")
    private String inboundOrderCode;

    @Schema(description = "入库类型（0采购入库，1退货入库，2其他入库）")
    private Integer inboundType;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "收货时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime receiptTime;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "收货商品明细")
    private List<ReceiptRegistrationDetailInfoVO> receiptRegistrationDetails;

}
