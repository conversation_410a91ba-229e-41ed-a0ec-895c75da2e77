package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import com.cdmanyou.shtwms.framework.common.util.date.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 收货登记单主 DO
 *
 * <AUTHOR>
 */
@TableName("receipt_registration")
@KeySequence("receipt_registration_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiptRegistrationDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 收货单号
     */
    private String receiptCode;
    /**
     * 收货时间
     */
    private LocalDateTime receiptTime;
    /**
     * 入库单ID
     */
    private Long inboundOrderId;
    /**
     * 上架单ID
     */
    private Long shelvingId;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 收货状态（0草稿，1待上架，2已上架）
     */
    private Integer receiptStatus;
    /**
     * 待收货品总量
     */
    private Integer pendingQuantityTotal;
    /**
     * 待收货品零散总量
     */
    private Integer pendingScatteredQuantityTotal;
    /**
     * 实收货品总量
     */
    private Integer receivedQuantityTotal;
    /**
     * 实收货品零散总量
     */
    private Integer receivedScatteredQuantityTotal;
    /**
     * 收货员ID
     */
    private Long receiptOperator;
    /**
     * 提交人ID
     */
    private Long submitOperator;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 备注（非必填）
     */
    private String remark;

}