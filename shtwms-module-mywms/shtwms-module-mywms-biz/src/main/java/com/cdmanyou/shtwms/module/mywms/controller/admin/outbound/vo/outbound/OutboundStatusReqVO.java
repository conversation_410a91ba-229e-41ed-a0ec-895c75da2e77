package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/16 17:56
 */
@Data
public class OutboundStatusReqVO {

    /**
     * 出库单id
     */
    @NotNull
    private Long id;

    /**
     * 提交
     */
    @Schema(description = "出库单状态(0'草稿',1'已提交',2'已完成',4'作废')")
    private Integer orderStatus;

    /**
     * 拣货状态
     */
    @Schema(description = "0'未发运',1'已发运'")
    private Integer sendStatus;

}
