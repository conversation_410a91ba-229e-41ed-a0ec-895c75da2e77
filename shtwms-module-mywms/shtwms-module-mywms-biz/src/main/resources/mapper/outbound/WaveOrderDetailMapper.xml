<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.WaveOrderDetailMapper">


    <select id="getWaveOrderDetailListByWaveId"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailEasyRespVO">
        SELECT wod.outbound_code,
               wod.product_name,
               wod.specification,
               wod.production_date,
               wod.pending_picking,
               wod.pending_simple_picking,
               ANY_VALUE(pwl.warehouse_name) as warehouse_name,
               COALESCE(pwl.available, 0)              AS available,
               COALESCE(pwl.scattered_available, 0)    AS scattered_available,
               COALESCE(pwl.all_count, 0)              AS all_count,
               COALESCE(pwl.scattered_count, 0)        AS scattered_count,
               COALESCE(pwl.freeze_count, 0)           AS freeze_count,
               COALESCE(pwl.freeze_scattered_count, 0) AS freeze_scattered_count
        FROM (
                 -- 先聚合订单数据
                 SELECT outbound_code,
                        product_name,
                        specification,
                        production_date,
                        product_id, -- 必须包含用于关联
                        SUM(pending_picking)        AS pending_picking,
                        SUM(pending_simple_picking) AS pending_simple_picking
                 FROM shtwms.wave_order_detail
                 WHERE deleted = 0
                   AND wave_id = #{waveId}
                 GROUP BY outbound_code,
                          product_name,
                          specification,
                          production_date,
                          product_id -- 关键：确保唯一性
             ) wod
                 LEFT JOIN (
            -- 再聚合库存数据
            SELECT pwl.product_id,
                   pwl.production_date,
                   w.name                      as warehouse_name,
                   SUM(available)              AS available,
                   SUM(scattered_available)    AS scattered_available,
                   SUM(count)                  AS all_count,
                   SUM(scattered_count)        AS scattered_count,
                   SUM(freeze_count)           AS freeze_count,
                   SUM(freeze_scattered_count) AS freeze_scattered_count
            FROM shtwms.product_warehouse_location pwl
                     inner join shtwms.warehouse_location wl on pwl.warehouse_location_id = wl.id
                     inner join shtwms.warehouse w on wl.warehouse_id = w.id
            GROUP BY pwl.product_id, pwl.production_date, w.name) pwl ON wod.product_id = pwl.product_id
            AND wod.production_date = pwl.production_date
    </select>
</mapper>