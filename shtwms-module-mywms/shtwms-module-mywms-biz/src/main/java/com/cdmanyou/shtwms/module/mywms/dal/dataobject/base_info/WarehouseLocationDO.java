package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import lombok.*;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 货位管理 DO
 *
 * <AUTHOR>
 */
@TableName("warehouse_location")
@KeySequence("warehouse_location_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseLocationDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 货位编码
     */
    private String code;
    /**
     * 货位名称
     */
    private String name;
    /**
     * 所属仓库ID（关联仓库表）
     */
    private Long warehouseId;
    /**
     * 所属库区ID（关联库区表）
     */
    private Long areaId;
    /**
     * 所属货架ID（关联货架表）
     */
    private Long shelfId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 长度(米)
     */
    private BigDecimal length;
    /**
     * 宽度(米)
     */
    private BigDecimal width;
    /**
     * 高度(米)
     */
    private BigDecimal height;
    /**
     * 承重(吨)
     */
    private BigDecimal weight;
    /**
     * 体积(立方米)
     */
    private BigDecimal volume;
    /**
     * 货位状态(1启用/0停用)
     */
    private Integer status;
    /**
     * 卸货方式(0叉车卸货，1其他卸货方式)
     */
    private Integer unloadingMethod;

}