package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer.CustomerSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.CustomerDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.CustomerService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 客户管理")
@RestController
@RequestMapping("/mywms/customer")
@Validated
public class CustomerController {

    @Resource
    private CustomerService customerService;

    @PostMapping("/create")
    @Operation(summary = "创建客户管理")
    @PreAuthorize("@ss.hasPermission('mywms:customer:create')")
    public CommonResult<Long> createCustomer(@Valid @RequestBody CustomerSaveReqVO createReqVO) {
        return success(customerService.createCustomer(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新客户管理")
    @PreAuthorize("@ss.hasPermission('mywms:customer:update')")
    public CommonResult<Boolean> updateCustomer(@Valid @RequestBody CustomerSaveReqVO updateReqVO) {
        customerService.updateCustomer(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客户管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:customer:delete')")
    public CommonResult<Boolean> deleteCustomer(@RequestParam("id") Long id) {
        customerService.deleteCustomer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客户管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:customer:query')")
    public CommonResult<CustomerRespVO> getCustomer(@RequestParam("id") Long id) {
        CustomerDO customer = customerService.getCustomer(id);
        return success(BeanUtils.toBean(customer, CustomerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:customer:query')")
    public CommonResult<PageResult<CustomerRespVO>> getCustomerPage(@Valid CustomerPageReqVO pageReqVO) {
        PageResult<CustomerDO> pageResult = customerService.getCustomerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustomerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客户管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:customer:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustomerExcel(@Valid CustomerPageReqVO pageReqVO,
                                    HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustomerDO> list = customerService.getCustomerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "客户管理.xls", "数据", CustomerRespVO.class,
                BeanUtils.toBean(list, CustomerRespVO.class));
    }

    @PutMapping("/updateCustomerStatus")
    @Operation(summary = "更新客户状态", description = "启用/停用客户状态，status=1启用，status=0停用")
    @PreAuthorize("@ss.hasPermission('mywms:customer:update')")
    public CommonResult<Boolean> updateCustomerStatus(@RequestParam("id") Long id,
                                                      @RequestParam("status") Integer status) {
        // 校验状态值合法性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("非法的状态值：" + status + "，只允许0(停用)或1(启用)");
        }

        if (status == 1) {
            customerService.enableCustomer(id);
        } else {
            customerService.disableCustomer(id);
        }
        return success(true);
    }

}