package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "效期统计简略信息")
@Data
public class SimpleExpirationStatisticVO {

    @Schema(description = "货品总数")
    private Integer productTotalCount;

    @Schema(description = "安全效期货品总数")
    private Integer safetyProductCount;

    @Schema(description = "临期风险货品总数")
    private Integer riskProductCount;

    @Schema(description = "临期货品总数")
    private Integer maxProductCount;

    @Schema(description = "过期货品总数")
    private Integer warningProductCount;

}
