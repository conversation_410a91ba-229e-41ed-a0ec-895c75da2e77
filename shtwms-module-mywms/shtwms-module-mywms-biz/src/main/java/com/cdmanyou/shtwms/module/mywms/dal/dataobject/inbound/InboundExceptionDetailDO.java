package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 入库异常处理单明细 DO
 *
 * <AUTHOR>
 */
@TableName("inbound_exception_detail")
@KeySequence("inbound_exception_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InboundExceptionDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 异常类型（0入库异常，1上架异常）
     */
    private Integer exceptionType;
    /**
     * 关联的明细表ID
     */
    private Long relatedDetailId;
    /**
     * 业务主表id
     */
    private Long businessId;
    /**
     * 处理方案
     */
    private String handlingScenarios;
    /**
     * 处理结果
     */
    private String handlingResult;
    /**
     * 处理状态（0未处理，1已处理）
     */
    private Integer handlingStatus;
    /**
     * 处理人ID
     */
    private Long handlingUser;
    /**
     * 处理时间
     */
    private LocalDateTime handlingTime;

}