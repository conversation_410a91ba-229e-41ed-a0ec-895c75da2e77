package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 波次单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WaveOrderDetailMapper extends BaseMapperX<WaveOrderDetailDO> {

    default List<WaveOrderDetailDO> selectListByWaveId(Long waveId) {
        return selectList(WaveOrderDetailDO::getWaveId, waveId);
    }

    default int deleteByWaveId(Long waveId) {
        return delete(WaveOrderDetailDO::getWaveId, waveId);
    }

}