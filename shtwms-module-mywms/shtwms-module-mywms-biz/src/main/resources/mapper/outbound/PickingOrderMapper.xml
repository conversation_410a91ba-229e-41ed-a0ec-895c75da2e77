<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.PickingOrderMapper">


    <select id="selectPickingOrderPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageRespVO">
        select tpo.id,
               tpo.create_time,
               tpo.picking_order_code,
               tpo.status,
               tpo.picking_type,
               tpo.total_pending,
               tpo.picking_cpmplete_time,
               tpo.picker
        from shtwms.total_picking_order tpo where tpo.deleted = 0


    </select>
</mapper>