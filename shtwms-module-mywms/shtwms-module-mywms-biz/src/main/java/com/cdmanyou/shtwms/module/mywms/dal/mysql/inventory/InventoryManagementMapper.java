package com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory;


import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.InventoryManagementDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 库存管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryManagementMapper extends BaseMapperX<InventoryManagementDO> {

    default PageResult<InventoryManagementDO> selectPage(InventoryManagementPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryManagementDO>()
                .likeIfPresent(InventoryManagementDO::getWarehouseName, reqVO.getWarehouseName())
                .likeIfPresent(InventoryManagementDO::getAreaName, reqVO.getAreaName())
                .likeIfPresent(InventoryManagementDO::getProductName, reqVO.getProductName())
                .betweenIfPresent(InventoryManagementDO::getProductionDate, reqVO.getProductionDate())
                .eqIfPresent(InventoryManagementDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(InventoryManagementDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(InventoryManagementDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryManagementDO::getId));
    }

}