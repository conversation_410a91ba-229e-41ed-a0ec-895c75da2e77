<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper">
    <update id="freezeProduct">
        UPDATE
            product_warehouse_location
        SET
            freeze_type = 1
        WHERE
            product_id = #{productId}
        AND
            product_batch_number = #{productBatchNumber}
    </update>
    <update id="unfreezeProduct">
        UPDATE
            product_warehouse_location
        SET
            freeze_type = 0
        WHERE
            product_id = #{productId}
        AND
            product_batch_number = #{productBatchNumber}
    </update>

    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationPageRespVO">
        SELECT
            pwl.id,
            pr.name AS producer_name,
            pwl.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            pwl.production_date,
            pwl.product_batch_number,
            pwl.available,
            w.name AS warehouse_name,
            wl.code AS warehouse_location_code
        FROM
            product_warehouse_location pwl
        LEFT JOIN
            producer pr ON pwl.producer_id = pr.id
        LEFT JOIN
            product p ON pwl.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON pwl.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        WHERE
            pwl.deleted = 0
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND wl.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.productBatchNumber != null and reqVO.productBatchNumber != ''">
            AND pwl.product_batch_number LIKE CONCAT('%', #{reqVO.productBatchNumber}, '%')
        </if>
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            COUNT(pwl.id)
        FROM
            product_warehouse_location pwl
        LEFT JOIN
            producer pr ON pwl.producer_id = pr.id
        LEFT JOIN
            product p ON pwl.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON pwl.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        WHERE
            pwl.deleted = 0
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        <if test="reqVO.warehouseId != null">
            AND wl.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.productBatchNumber != null and reqVO.productBatchNumber != ''">
            AND pwl.product_batch_number LIKE CONCAT('%', #{reqVO.productBatchNumber}, '%')
        </if>
    </select>
    <select id="getProductWarehouseLocationById"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationRespVO">
        SELECT
            pwl.id,
            pr.name AS producer_name,
            pwl.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            pwl.production_date,
            pwl.product_batch_number,
            w.name AS warehouse_name,
            wl.code AS warehouse_location_code,
            pwl.count,
            pwl.occupation,
            pwl.available,
            pwl.scattered_count,
            pwl.scattered_occupation,
            pwl.scattered_available
        FROM
            product_warehouse_location pwl
        LEFT JOIN
            producer pr ON pwl.producer_id = pr.id
        LEFT JOIN
            product p ON pwl.product_id = p.id
        LEFT JOIN
            warehouse_location wl ON pwl.warehouse_location_id = wl.id
        LEFT JOIN
            warehouse w ON wl.warehouse_id = w.id
        WHERE
            pwl.deleted = 0
        AND
            pwl.id = #{id}
    </select>
    <select id="selectByConditions"
            resultType="com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO">
        SELECT
            *
        FROM
            product_warehouse_location
        WHERE
            producer_id = #{producerId}
        AND
            product_id = #{productId}
        AND
            production_date = #{productionDate}
        AND
            warehouse_location_id = #{warehouseLocationId}
        AND
            deleted = 0
    </select>
    <select id="getSimpleLocationStatistics"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.SimpleLocationStatisticVO"></select>


</mapper>