package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TrayPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TrayRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TraySaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.TrayDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.TrayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 托盘管理")
@RestController
@RequestMapping("/mywms/tray")
@Validated
public class TrayController {

    @Resource
    private TrayService trayService;

    @PostMapping("/create")
    @Operation(summary = "创建托盘管理")
    @PreAuthorize("@ss.hasPermission('mywms:tray:create')")
    public CommonResult<Long> createTray(@Valid @RequestBody TraySaveReqVO createReqVO) {
        return success(trayService.createTray(createReqVO));
    }

    @PutMapping("/updatestatus")
    @Operation(summary = "更新托盘状态")
    @Parameters({
            @Parameter(name = "id", description = "托盘ID", required = true),
            @Parameter(name = "status", description = "状态(1启用/0停用)", required = true)
    })
    @PreAuthorize("@ss.hasPermission('mywms:tray:update')")
    public CommonResult<Boolean> updateTrayStatus(
            @RequestParam("id") Long id,
            @RequestParam("status") @Min(0) @Max(1) Integer status) {

        trayService.updateTrayStatus(id, status);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新托盘管理")
    @PreAuthorize("@ss.hasPermission('mywms:tray:update')")
    public CommonResult<Boolean> updateTray(@Valid @RequestBody TraySaveReqVO updateReqVO) {
        trayService.updateTray(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除托盘管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:tray:delete')")
    public CommonResult<Boolean> deleteTray(@RequestParam("id") Long id) {
        trayService.deleteTray(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得托盘管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:tray:query')")
    public CommonResult<TrayRespVO> getTray(@RequestParam("id") Long id) {
        TrayDO tray = trayService.getTray(id);
        return success(BeanUtils.toBean(tray, TrayRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得托盘管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:tray:query')")
    public CommonResult<PageResult<TrayRespVO>> getTrayPage(@Valid TrayPageReqVO pageReqVO) {
        PageResult<TrayDO> pageResult = trayService.getTrayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TrayRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出托盘管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:tray:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTrayExcel(@Valid TrayPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TrayDO> list = trayService.getTrayPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "托盘管理.xls", "数据", TrayRespVO.class,
                        BeanUtils.toBean(list, TrayRespVO.class));
    }


}