package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 收货登记单明细 DO
 *
 * <AUTHOR>
 */
@TableName("receipt_registration_detail")
@KeySequence("receipt_registration_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiptRegistrationDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 收货单ID
     */
    private Long receiptId;
    /**
     * 入库单明细ID
     */
    private Long inboundOrderDetailId;
    /**
     * 上架单明细ID
     */
    private Long shelvingDetailId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 商品批次号
     */
    private String productBatchNumber;
    /**
     * 系统批次号
     */
    private String systemBatchNumber;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 待入库数量
     */
    private Integer pendingQuantity;
    /**
     * 待入库零散数量
     */
    private Integer pendingScatteredQuantity;
    /**
     * 实收数量
     */
    private Integer receivedQuantity;
    /**
     * 实收零散数量
     */
    private Integer receivedScatteredQuantity;
    /**
     * 上架数量
     */
    private Integer shelvedQuantity;
    /**
     * 上架零散数量
     */
    private Integer shelvedScatteredQuantity;
    /**
     * 实收重量
     */
    private BigDecimal receivedWeight;
    /**
     * 上架重量
     */
    private BigDecimal shelvedWeight;
    /**
     * 差异备注（非必填）
     */
    private String differenceRemark;

}