package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 波次单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WaveOrderPageRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12675")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "波次单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("波次单号")
    private String waveCode;

    @Schema(description = "波次类型(0'普通波次',1'紧急波次')", example = "2")
    @ExcelProperty("波次类型(0'普通波次',1'紧急波次')")
    private Integer waveType;

    @Schema(description = "出库单号", example = "2")
    @ExcelProperty("出库单号")
    private String outboundCode;

    @Schema(description = "仓库名称", example = "22504")
    @ExcelProperty("仓库名称")
    private String warehouseName;

    @Schema(description = "波次单状态(0'正常',1'作废',2'确认')", example = "1")
    @ExcelProperty("波次单状态(0'正常',1'作废',2'确认')")
    private Integer waveStatus;

    @Schema(description = "拣货状态(0'待拣货',1'拣货中',2'已完成')", example = "2")
    @ExcelProperty("拣货状态(0'待拣货',1'拣货中',2'已完成')")
    private Integer pickingStatus;

    @Schema(description = "拣货员")
    @ExcelProperty("拣货员")
    private String picker;

    @Schema(description = "仓库ID（关联仓库表）", example = "22504")
    @ExcelProperty("仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "备注（非必填）", example = "你说的对")
    @ExcelProperty("备注（非必填）")
    private String remark;


}