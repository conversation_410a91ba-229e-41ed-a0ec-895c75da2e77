package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.supplier.SupplierPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.CustomerDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.SupplierDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 供应商管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SupplierMapper extends BaseMapperX<SupplierDO> {


    default boolean existsByCode(String code) {
        return selectCount(new QueryWrapper<SupplierDO>().eq("code", code)) > 0;
    }
    // 校验名称唯一性
    default boolean existsByName(String name) {
        return selectCount(new QueryWrapper<SupplierDO>().eq("name", name)) > 0;
    }
    // 更新时校验编码唯一性（排除自身）
    default boolean existsByCodeAndIdNot(String code, Long excludeId) {
        return selectCount(new QueryWrapper<SupplierDO>()
                .eq("code", code)
                .ne("id", excludeId)) > 0;
    }
    // 更新时校验名称唯一性（排除自身）
    default boolean existsByNameAndIdNot(String name, Long excludeId) {
        return selectCount(new QueryWrapper<SupplierDO>()
                .eq("name", name)
                .ne("id", excludeId)) > 0;
    }
    default PageResult<SupplierDO> selectPage(SupplierPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SupplierDO>()
                .eqIfPresent(SupplierDO::getCode, reqVO.getCode())
                .likeIfPresent(SupplierDO::getName, reqVO.getName()) // 供应商名称模糊查询
                .likeIfPresent(SupplierDO::getContactPerson, reqVO.getContactPerson()) // 新增联系人模糊查询
                .eqIfPresent(SupplierDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SupplierDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SupplierDO::getId));
    }

}