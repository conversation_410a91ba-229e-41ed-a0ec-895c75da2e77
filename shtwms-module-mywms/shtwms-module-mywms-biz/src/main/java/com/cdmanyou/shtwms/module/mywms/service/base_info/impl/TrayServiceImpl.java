package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TrayPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.tray.TraySaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.TrayDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.TrayMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.TrayService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.TRAY_CODE_DUPLICATE;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.TRAY_NOT_EXISTS;

/**
 * 托盘管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TrayServiceImpl implements TrayService {

    @Resource
    private TrayMapper trayMapper;

    @Override
    public Long createTray(TraySaveReqVO createReqVO) {

        // 校验托盘码唯一性
        if (trayMapper.existsByCode(createReqVO.getCode())) {
            throw exception(TRAY_CODE_DUPLICATE);
        }
        // 插入
        TrayDO tray = BeanUtils.toBean(createReqVO, TrayDO.class);
        trayMapper.insert(tray);
        // 返回
        return tray.getId();
    }

    @Override
    public void updateTray(TraySaveReqVO updateReqVO) {
        // 校验存在
        validateTrayExists(updateReqVO.getId());
        // 校验托盘码唯一性（排除自身）
        if (trayMapper.existsByCodeAndIdNot(updateReqVO.getCode(), updateReqVO.getId())) {
            throw exception(TRAY_CODE_DUPLICATE);
        }
        // 更新
        TrayDO updateObj = BeanUtils.toBean(updateReqVO, TrayDO.class);
        trayMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTrayStatus(Long id, Integer status) {
        // 校验存在
        validateTrayExists(id);

        // 校验状态值合法性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("状态值只能是0或1");
        }

        // 更新状态
        TrayDO updateObj = new TrayDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        trayMapper.updateById(updateObj);
    }

    @Override
    public void deleteTray(Long id) {
        // 校验存在
        validateTrayExists(id);
        // 删除
        trayMapper.deleteById(id);
    }

    private void validateTrayExists(Long id) {
        if (trayMapper.selectById(id) == null) {
            throw exception(TRAY_NOT_EXISTS);
        }
    }

    @Override
    public TrayDO getTray(Long id) {
        return trayMapper.selectById(id);
    }

    @Override
    public PageResult<TrayDO> getTrayPage(TrayPageReqVO pageReqVO) {
        return trayMapper.selectPage(pageReqVO);
    }

}