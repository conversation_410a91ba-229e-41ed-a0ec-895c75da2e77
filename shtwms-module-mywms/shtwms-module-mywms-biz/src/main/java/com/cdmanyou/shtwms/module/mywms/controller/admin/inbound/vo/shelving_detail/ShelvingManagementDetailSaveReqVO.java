package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 上架管理单明细新增/修改 Request VO")
@Data
public class ShelvingManagementDetailSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20983")
    private Long id;

    @Schema(description = "上架单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6136")
    @NotNull(message = "上架单ID不能为空")
    private Long shelvingId;

    @Schema(description = "收货单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13575")
    @NotNull(message = "收货单ID不能为空")
    private Long receiptId;

    @Schema(description = "入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29764")
    @NotNull(message = "入库单ID不能为空")
    private Long inboundOrderId;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23939")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "系统批次号")
    private String systemBatchNumber;

    @Schema(description = "生产日期")
    private LocalDate productionDate;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "收货数量（待上架数量）")
    private Integer receivedQuantity;

    @Schema(description = "上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "托盘ID（关联托盘表，提交并上架时必填）", example = "5672")
    private Long trayId;

    @Schema(description = "仓库ID（关联仓库表，提交并上架时必填）", example = "27451")
    private Long warehouseId;

    @Schema(description = "库区ID（关联库区表，提交并上架时必填）", example = "16657")
    private Long areaId;

    @Schema(description = "库位ID（关联货位表，提交并上架时必填）", example = "22563")
    private Long locationId;

}