package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "上架单明细详情实体")
@Data
public class ShelvingManagementDetailInfoVO {

    @Schema(description = "自增ID")
    private Long id;

    @Schema(description = "父级ID（分拆ID）")
    private Long parentId;

    @Schema(description = "上架单ID")
    private Long shelvingId;

    @Schema(description = "货主ID")
    private Long producerId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "系统批次号")
    private String systemBatchNumber;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate productionDate;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "待入库零散数量")
    private Integer pendingScatteredQuantity;

    @Schema(description = "收货数量（待上架数量）")
    private Integer receivedQuantity;

    @Schema(description = "收货零散数量（待上架零散数量）")
    private Integer receivedScatteredQuantity;

    @Schema(description = "上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "上架零散数量")
    private Integer shelvedScatteredQuantity;

    @Schema(description = "实收重量")
    private BigDecimal receivedWeight;

    @Schema(description = "上架重量")
    private BigDecimal shelvedWeight;

    @Schema(description = "托盘ID（关联托盘表，提交并上架时必填）")
    private Long trayId;

    @Schema(description = "托盘名称")
    private String trayName;

    @Schema(description = "货位ID（关联货位表，提交并上架时必填）")
    private Long locationId;

    @Schema(description = "货位编码")
    private String locationCode;

}
