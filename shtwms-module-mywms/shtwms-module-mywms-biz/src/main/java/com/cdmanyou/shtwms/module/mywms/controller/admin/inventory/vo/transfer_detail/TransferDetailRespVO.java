package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "管理后台 - 货品转移明细 Response VO")
@Data
public class TransferDetailRespVO {

    @Schema(description = "转移单详情ID")
    private Long id;

    @Schema(description = "货主ID")
    private Long producerId;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "转移数量")
    private Integer quantity;

    @Schema(description = "转移零散数量")
    private Integer scatteredQuantity;

    @Schema(description = "转出货位ID")
    private Long fromWareHouseLocationId;

    @Schema(description = "转出货位编号")
    private String fromWareHouseLocationCode;

    @Schema(description = "转入货位ID")
    private Long toWareHouseLocationId;

    @Schema(description = "转入货位编号")
    private String toWareHouseLocationCode;

}