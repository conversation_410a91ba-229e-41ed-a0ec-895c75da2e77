package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 总拣单主分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PickingOrderPageReqVO extends PageParam {

    @Schema(description = "拣货单号")
    private String pickingOrderCode;

    @Schema(description = "波次单编号（关联波次单主表）")
    private String waveCode;

    @Schema(description = "仓库ID（关联仓库表）", example = "859")
    private Long warehouseId;

    @Schema(description = "拣货员")
    private String picker;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}