package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import javax.validation.constraints.*;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "管理后台 - 盘点计划明细新增/修改 Request VO")
@Data
public class InventoryPlanDetailSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11316")
    private Long id;

    @Schema(description = "盘点计划ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "8868")
    @NotNull(message = "盘点计划ID（关联主表）不能为空")
    private Long inventoryPlanId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "规格（非必填）")
    private String specification;

    @Schema(description = "账面数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "账面数量不能为空")
    private Integer bookQuantity;

    @Schema(description = "实盘数量（非必填）")
    private Integer actualQuantity;

    @Schema(description = "差异数量（非必填）")
    private Integer differenceQuantity;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate manufactureDate;
    /**
     * 有效期
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate effectiveDate;
    /**
     * 库位id
     */
    private Long locationId;
    /**
     * 货位号
     */
    private String locationCode;
    /**
     * 货物id
     */
    private Long productId;

}