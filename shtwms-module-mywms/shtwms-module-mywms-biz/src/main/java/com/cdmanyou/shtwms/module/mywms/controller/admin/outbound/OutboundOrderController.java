package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutBoundProductDetail;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 出库订单主")
@RestController
@RequestMapping("/mywms/outbound-order")
@Validated
public class OutboundOrderController {

    @Resource
    private OutboundOrderService outboundOrderService;

    @Resource
    private ProductService productService;

    @PostMapping("/create")
    @Operation(summary = "创建出库订单主")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:create')")
    public CommonResult<Long> createOutboundOrder(@Valid @RequestBody OutboundOrderSaveReqVO createReqVO) {
        return success(outboundOrderService.createOutboundOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新出库订单主")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:update')")
    public CommonResult<Boolean> updateOutboundOrder(@Valid @RequestBody OutboundOrderSaveReqVO updateReqVO) {
        outboundOrderService.updateOutboundOrder(updateReqVO);
        return success(true);
    }

    @PutMapping("/outbound-status-update")
    @Operation(summary = "出库单状态边跟")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:outbound-status-update')")
    public CommonResult<String> outboundStatusUpdate(@Valid @RequestBody OutboundStatusReqVO outboundStatusReqVO) {
        outboundOrderService.outboundStatusUpdate(outboundStatusReqVO);
        return CommonResult.success("状态变更成功");
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除出库订单主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:delete')")
    public CommonResult<Boolean> deleteOutboundOrder(@RequestParam("id") Long id) {
        outboundOrderService.deleteOutboundOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得出库订单主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:query')")
    public CommonResult<OutboundOrderRespVO> getOutboundOrder(@RequestParam("id") Long id) {
        OutboundOrderRespVO outboundOrder = outboundOrderService.getOutboundOrder(id);
        return CommonResult.success(outboundOrder);
    }

    @GetMapping("/page")
    @Operation(summary = "获得出库订单主分页")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:query')")
    public CommonResult<PageResult<OutboundOrderPageRespVO>> getOutboundOrderPage(@Valid OutboundOrderPageReqVO pageReqVO) {
        PageResult<OutboundOrderPageRespVO> pageResult = outboundOrderService.getOutboundOrderPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出出库订单主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOutboundOrderExcel(@Valid OutboundOrderPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OutboundOrderPageRespVO> list = outboundOrderService.getOutboundOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "出库订单主.xls", "数据", OutboundOrderPageRespVO.class,
                list);
    }

    // ==================== 子表（出库订单明细） ====================

    @GetMapping("/outbound-order-detail/list-by-outbound-id")
    @Operation(summary = "获得出库订单明细列表")
    @Parameter(name = "outboundId", description = "出库单ID（关联主表）")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:query')")
    public CommonResult<List<OutboundOrderDetailDO>> getOutboundOrderDetailListByOutboundId(@RequestParam("outboundId") Long outboundId) {
        return success(outboundOrderService.getOutboundOrderDetailListByOutboundId(outboundId));
    }


    @GetMapping("/product-type")
    @Operation(summary = "出货时获取货品列表")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:product-type')")
    public CommonResult<List<ProductSimpleVO>> getProductSimpleList(@RequestParam("producerId") Long producerId) {
        List<ProductSimpleVO> res = productService.getProductSimpleList(producerId);
        return CommonResult.success(res);
    }


    @GetMapping("/info-product-code")
    @Operation(summary = "出货时获取货品详情")
    @PreAuthorize("@ss.hasPermission('mywms:outbound-order:info-product-code')")
    public CommonResult<OutBoundProductDetail> getInfoByProductOutboundInfo(@RequestParam("productId") String productId, @RequestParam("outboundNumber") Integer outboundNumber) {
        OutBoundProductDetail res = productService.getInfoByProductOutboundInfo(productId, outboundNumber);
        return CommonResult.success(res);
    }


}