package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @date 2025/6/16 10:54
 */
@Data
@Schema(description = "出库商品明细")
public class OutBoundProductDetail {

    /**
     * 商品规格
     */
    @Schema(description = "商品规格")
    private String specification;

    /**
     * 货品批次号
     */
    @Schema(description = "货品批次号")
    private String productBatchNumber;

    /**
     * 拆零数量
     */
    @Schema(description = "拆零数量")
    private Integer looseQuantity;

    /**
     * 拆零数量
     */
    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    /**
     * 产品可用数量
     */
    @Schema(description = "产品可用数量")
    private BigDecimal productAvailable;

    /**
     * 产品可用零散数量
     */
    @Schema(description = "产品可用零散数量")
    private BigDecimal productScatteredAvailable;

    /**
     * 重量
     */
    @Schema(description = "重量")
    private BigDecimal grossWeight;

    /**
     * 体积
     */
    @Schema(description = "体积")
    private BigDecimal volume;

    /**
     * 体积
     */
    @Schema(description = "是否跨库")
    private Integer straddleWarehouse;

    /**
     * 体积
     */
    @Schema(description = "仓库id")
    private List<Long> WareHouseIds;


}
