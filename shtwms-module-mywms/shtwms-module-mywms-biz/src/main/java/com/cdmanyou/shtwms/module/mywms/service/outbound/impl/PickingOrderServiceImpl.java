package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderStatusReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailStatusReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.PickingOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.PickingOrderMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.WaveOrderMapper;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderDetailService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.WaveOrderDetailService;
import com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.PICKING_ORDER_NOT_EXISTS;

/**
 * 总拣单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PickingOrderServiceImpl implements PickingOrderService {

    @Resource
    private PickingOrderMapper pickingOrderMapper;

    @Resource
    private WaveOrderMapper waveOrderMapper;

    @Resource
    private WaveOrderDetailService waveOrderDetailService;
    @Resource
    private PickingOrderDetailMapper pickingOrderDetailMapper;
    @Resource
    private PickingOrderDetailService pickingOrderDetailService;
    @Resource
    private NumberFieldUtil numberFieldUtil;
    @Autowired
    private OutboundOrderMapper outboundOrderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPickingOrder(List<Long> waveOrderIds, Integer waveOrderType, Long outboundId, String outboundCode) {
        List<WaveOrderDO> waveOrderDOS = waveOrderMapper.selectByIds(waveOrderIds);
        List<PickingOrderDO> pickingOrderDOS = new ArrayList<>();
        for (WaveOrderDO waveOrderDO : waveOrderDOS) {
            PickingOrderDO pickingOrderDO = BeanUtils.toBean(waveOrderDO, PickingOrderDO.class);
            pickingOrderDO.setId(null);
            pickingOrderDO.setPickingOrderCode(numberFieldUtil.createWaveOrderCode("pickingOrder", "ZJ"));
            if (waveOrderType == 0) {
                pickingOrderDO.setWaveId(waveOrderDO.getId());
            } else {
                pickingOrderDO.setOutboundId(outboundId);
                pickingOrderDO.setOutboundCode(outboundCode);
            }
            pickingOrderDO.setPickingType(waveOrderType);
            pickingOrderDO.setTotalPending(waveOrderDO.getProductCount());
            pickingOrderDOS.add(pickingOrderDO);
        }
        //创建总检单
        pickingOrderMapper.insertBatch(pickingOrderDOS);

        //创建总检单详情
        for (PickingOrderDO pickingOrderDO : pickingOrderDOS) {
            pickingOrderDetailService.createPickingOrderDetailList(pickingOrderDO);
        }
        // 返回
        return 0L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePickingOrder(PickingOrderSaveReqVO updateReqVO) {
        // 校验存在
        validatePickingOrderExists(updateReqVO.getId());
        // 更新
        PickingOrderDO updateObj = BeanUtils.toBean(updateReqVO, PickingOrderDO.class);
        pickingOrderMapper.updateById(updateObj);

        // 更新子表
        updatePickingOrderDetailList(updateReqVO.getId(), updateReqVO.getPickingOrderDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePickingOrder(Long id) {
        // 校验存在
        validatePickingOrderExists(id);
        // 删除
        pickingOrderMapper.deleteById(id);

        // 删除子表
        deletePickingOrderDetailByPickingOrderId(id);
    }

    private void validatePickingOrderExists(Long id) {
        if (pickingOrderMapper.selectById(id) == null) {
            throw exception(PICKING_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public PickingOrderDO getPickingOrder(Long id) {
        return pickingOrderMapper.selectById(id);
    }

    @Override
    public PageResult<PickingOrderPageRespVO> getPickingOrderPage(PickingOrderPageReqVO pageReqVO) {
        Page<PickingOrderPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = pickingOrderMapper.selectPickingOrderPage(page, pageReqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    // ==================== 子表（总拣单明细） ====================

    @Override
    public List<PickingOrderDetailPageRespVO> getPickingOrderDetailListByPickingOrderId(Long pickingOrderId) {
        return pickingOrderDetailMapper.selectPickingOrderDetailListByPickingOrderId(pickingOrderId);
    }

    @Override
    public void updatePickingOrderStatus(PickingOrderStatusReqVO pickingOrderStatusReqVO) {
        PickingOrderDO pickingOrderDO = pickingOrderMapper.selectById(pickingOrderStatusReqVO.getId());
        if (pickingOrderStatusReqVO.getPickingStatus() == 2) {
            //总检单作废
            if (pickingOrderDO.getPickingType() == 0) {
                //普通出库，修改波次单状态为草稿
                LambdaUpdateWrapper<WaveOrderDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.set(WaveOrderDO::getWaveStatus, 0);
                lambdaUpdateWrapper.eq(WaveOrderDO::getId, pickingOrderDO.getWaveId());
                waveOrderMapper.update(lambdaUpdateWrapper);
            } else {
                //紧急出库，修改出库单状态为草稿
                LambdaUpdateWrapper<OutboundOrderDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.set(OutboundOrderDO::getOrderStatus, 0);
                lambdaUpdateWrapper.eq(OutboundOrderDO::getId, pickingOrderDO.getOutboundId());
                outboundOrderMapper.update(lambdaUpdateWrapper);
            }
        }
        pickingOrderDO.setStatus(pickingOrderStatusReqVO.getPickingStatus());
        pickingOrderMapper.updateById(pickingOrderDO);
    }

    @Override
    public void updateDeterminePickingOrder(List<PickingOrderDetailStatusReqVO> pickingOrderStatusReqVO) {
        pickingOrderStatusReqVO.forEach(pickingOrderDetailDO -> {
            pickingOrderDetailDO.setPickingDetailStatus(1);
            pickingOrderDetailService.updatePickingOrderDetailStatus(pickingOrderDetailDO);
        });

    }


    private void updatePickingOrderDetailList(Long pickingOrderId, List<PickingOrderDetailDO> list) {
        deletePickingOrderDetailByPickingOrderId(pickingOrderId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
//        createPickingOrderDetailList(pickingOrderId, list);
    }

    private void deletePickingOrderDetailByPickingOrderId(Long pickingOrderId) {
        pickingOrderDetailMapper.deleteByPickingOrderId(pickingOrderId);
    }

}