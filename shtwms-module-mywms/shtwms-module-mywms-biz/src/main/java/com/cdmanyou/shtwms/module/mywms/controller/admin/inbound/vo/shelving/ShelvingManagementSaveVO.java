package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "上架单保存/提交请求实体")
@Data
public class ShelvingManagementSaveVO {

    @Schema(description = "保存标识（0保存，1提交）")
    private Integer saveFlag;

    @Schema(description = "上架单ID")
    private Long id;

    @Schema(description = "上架单号")
    private String shelvingCode;

    @Schema(description = "上架时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime shelvingTime;

    @Schema(description = "上架单明细列表")
    private List<ShelvingManagementDetailSaveVO> shelvingManagementDetails;

}