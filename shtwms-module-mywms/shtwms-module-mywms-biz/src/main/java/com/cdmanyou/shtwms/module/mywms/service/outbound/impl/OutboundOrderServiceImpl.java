package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.collection.CollectionUtils;
import com.cdmanyou.shtwms.framework.common.util.number.NumberUtils;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OrderDetailProductWarehouseDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OrderDetailProductWarehouseMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductService;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderDetailService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.WaveOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.bo.ProductAndHouseBO;
import com.cdmanyou.shtwms.module.mywms.service.outbound.bo.ProductHouseLocationBO;
import com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.OUTBOUND_ORDER_NOT_EXISTS;

/**
 * 出库订单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutboundOrderServiceImpl implements OutboundOrderService {

    @Resource
    private OutboundOrderMapper outboundOrderMapper;

    @Resource
    private OutboundOrderDetailMapper outboundOrderDetailMapper;

    @Resource
    private OutboundOrderDetailService outboundOrderDetailService;

    @Resource
    private PickingOrderService pickingOrderService;

    @Resource
    private NumberFieldUtil numberFieldUtil;

    @Resource
    private WaveOrderService waveOrderService;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private OrderDetailProductWarehouseMapper orderDetailProductWarehouseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOutboundOrder(OutboundOrderSaveReqVO createReqVO) {
        // 插入
        OutboundOrderDO outboundOrder = BeanUtils.toBean(createReqVO, OutboundOrderDO.class);
        outboundOrder.setOutboundCode(numberFieldUtil.createWaveOrderCode("outboundOrder", "CK"));
        outboundOrderMapper.insert(outboundOrder);

        // 插入子表
        outboundOrderDetailService.createOutboundOrderDetailList(outboundOrder, createReqVO.getOutboundOrderDetails());
        // 返回
        return outboundOrder.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOutboundOrder(OutboundOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateOutboundOrderExists(updateReqVO.getId());
        // 更新
        OutboundOrderDO updateObj = BeanUtils.toBean(updateReqVO, OutboundOrderDO.class);
        outboundOrderMapper.updateById(updateObj);

        outboundOrderDetailService.deleteOrAbrogateOutboundOrderDetailByOutboundId(updateReqVO.getId(), true);
        updateReqVO.getOutboundOrderDetails().forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        outboundOrderDetailService.createOutboundOrderDetailList(BeanUtils.toBean(updateReqVO, OutboundOrderDO.class), updateReqVO.getOutboundOrderDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOutboundOrder(Long id) {
        // 校验存在
        validateOutboundOrderExists(id);
        // 删除
        outboundOrderMapper.deleteById(id);

        // 删除子表
        outboundOrderDetailService.deleteOrAbrogateOutboundOrderDetailByOutboundId(id, true);
    }

    private void validateOutboundOrderExists(Long id) {
        if (outboundOrderMapper.selectById(id) == null) {
            throw exception(OUTBOUND_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public OutboundOrderRespVO getOutboundOrder(Long id) {
        OutboundOrderDO outboundOrderDO = outboundOrderMapper.selectById(id);
        OutboundOrderRespVO bean = BeanUtils.toBean(outboundOrderDO, OutboundOrderRespVO.class);
        List<OutboundOrderDetailDO> outboundOrderDetailDOS = outboundOrderDetailMapper.selectListByOutboundId(id);
        bean.setOutboundOrderDetailList(BeanUtils.toBean(outboundOrderDetailDOS, OutboundOrderDetailRespVO.class));
        return bean;
    }

    @Override
    public PageResult<OutboundOrderPageRespVO> getOutboundOrderPage(OutboundOrderPageReqVO pageReqVO) {
        Page<OutboundOrderPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = outboundOrderMapper.getOutboundOrderPage(page, pageReqVO);
        page.getRecords().forEach(e -> {
            if (e.getOutboundType() != 1) {
                e.setUnitName(e.getCustomerName());
                e.setUnitCode(e.getCustomerCode());
                e.setUnitType("客户");
            } else {
                e.setUnitName(e.getSupplierName());
                e.setUnitCode(e.getSupplierCode());
                e.setUnitType("供应商");
            }
        });
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    // ==================== 子表（出库订单明细） ====================

    @Override
    public List<OutboundOrderDetailRespVO> getOutboundOrderDetailListByOutboundId(Long outboundId) {
        List<OutboundOrderDetailDO> outboundOrderDetailDOS = outboundOrderDetailMapper.selectListByOutboundId(outboundId);
        return BeanUtils.toBean(outboundOrderDetailDOS, OutboundOrderDetailRespVO.class);
    }

    @Override
    @Transactional
    public void outboundStatusUpdate(OutboundStatusReqVO outboundStatusReqVO) {
        OutboundOrderDO outboundOrderDO = outboundOrderMapper.selectById(outboundStatusReqVO.getId());
        if (outboundOrderDO == null) {
            throw exception(OUTBOUND_ORDER_NOT_EXISTS);
        }
        if (outboundStatusReqVO.getOrderStatus() == 1) {
            // 出库单提交扣减库存
            List<OutboundOrderDetailDO> outboundOrderDetailDOS = outboundOrderDetailMapper.selectListByOutboundId(outboundStatusReqVO.getId());

            for (OutboundOrderDetailDO detailDO : outboundOrderDetailDOS) {
                //找到对应货位，扣减货位可用量
                List<ProductAndHouseBO> locationBOS = productWareHouseLocationMapper.selectListByProductIdAndProductionDate(detailDO.getProductId(), detailDO.getProductionDate(), outboundOrderDO.getWarehouseId(), detailDO.getHouseType());
                //库存零散数剩余总量
                int allAvailable = locationBOS.stream().mapToInt(ProductAndHouseBO::getScatteredAvailable).sum();
                Integer simpleCount = detailDO.getSimpleCount();
                Integer pendingQuantity = detailDO.getPendingQuantity();
                List<OrderDetailProductWarehouseDO> outboundProductArray = new ArrayList<>();
                for (ProductAndHouseBO houseBO : locationBOS) {
                    if (houseBO.getScatteredAvailable() > 0 || houseBO.getAvailable() > 0) {
                        OrderDetailProductWarehouseDO productWarehouseDO = new OrderDetailProductWarehouseDO();
                        productWarehouseDO.setOutboundDetailId(detailDO.getId());
                        productWarehouseDO.setProductWarehouseId(houseBO.getId());
                        productWarehouseDO.setWarehouseLocationId(houseBO.getWarehouseLocationId());
                        productWarehouseDO.setWarehouseId(houseBO.getWarehouseId());
                        if (simpleCount > 0) {
                            if (allAvailable < simpleCount && houseBO.getAvailable() > 0) {
                                Integer boAvailable = houseBO.getAvailable();
                                Integer boScatteredAvailable = houseBO.getScatteredAvailable();
                                ProductDO byId = productMapper.selectById(houseBO.getProductId());
                                Integer looseQuantity = byId.getLooseQuantity();
                                int needCount = simpleCount / looseQuantity;
                                if (houseBO.getAvailable() > needCount) {
                                    if (simpleCount % looseQuantity > 0) {
                                        productWarehouseDO.setOverallScatteredCount(needCount + 1);
                                        houseBO.setAvailable(boAvailable - needCount - 1);
                                        houseBO.setScatteredAvailable(boScatteredAvailable + (looseQuantity * (needCount + 1)));
                                    } else {
                                        productWarehouseDO.setOverallScatteredCount(needCount);
                                        houseBO.setAvailable(boAvailable - needCount);
                                        houseBO.setScatteredAvailable(boScatteredAvailable + (looseQuantity * needCount));
                                    }
                                } else {
                                    houseBO.setAvailable(0);
                                    houseBO.setScatteredAvailable(boScatteredAvailable + (looseQuantity * boAvailable));
                                    productWarehouseDO.setOverallScatteredCount(boAvailable);
                                }
                            }
                            if (houseBO.getScatteredAvailable() >= simpleCount) {
                                productWarehouseDO.setProductScatteredCount(simpleCount);
                                houseBO.setScatteredAvailable(houseBO.getScatteredAvailable() - simpleCount);
                                houseBO.setScatteredOccupation(houseBO.getScatteredOccupation() + simpleCount);
                                simpleCount = 0;
                            } else {
                                Integer scatteredAvailable = houseBO.getScatteredAvailable();
                                productWarehouseDO.setProductScatteredCount(scatteredAvailable);
                                houseBO.setScatteredOccupation(houseBO.getScatteredOccupation() + scatteredAvailable);
                                houseBO.setScatteredAvailable(0);
                                simpleCount -= scatteredAvailable;
                            }
                        }
                        if (pendingQuantity > 0 && houseBO.getAvailable() > 0) {
                            if (houseBO.getAvailable() >= pendingQuantity) {
                                productWarehouseDO.setProductOverallCount(pendingQuantity);
                                houseBO.setScatteredAvailable(houseBO.getAvailable() - pendingQuantity);
                                houseBO.setScatteredOccupation(houseBO.getOccupation() + pendingQuantity);
                                pendingQuantity = 0;
                            } else {
                                Integer available = houseBO.getAvailable();
                                productWarehouseDO.setProductOverallCount(available);
                                houseBO.setOccupation(houseBO.getOccupation() + available);
                                houseBO.setAvailable(0);
                                simpleCount -= available;
                            }
                        }
                        if (Objects.isNull(productWarehouseDO.getProductOverallCount()) && Objects.isNull(productWarehouseDO.getProductScatteredCount())) {
                            continue;
                        }
                        outboundProductArray.add(productWarehouseDO);
                    }
                }
                List<ProductWareHouseLocationDO> wareHouseLocationDOS = BeanUtils.toBean(locationBOS, ProductWareHouseLocationDO.class);
                if (!productWareHouseLocationMapper.updateBatch(wareHouseLocationDOS)) {
                    throw new RuntimeException("网络繁忙，请稍后再试");
                }
                if (!CollectionUtils.isAnyEmpty(outboundProductArray)) {
                    orderDetailProductWarehouseMapper.insertBatch(outboundProductArray);
                }
            }


            if (outboundOrderDO.getOutboundWay() == 1) {
                //直接生成波次单
                List<Long> waveOrderIds = waveOrderService.createWaveOrder(Collections.singletonList(outboundStatusReqVO.getId()));
                //直接生成总检单
                pickingOrderService.createPickingOrder(waveOrderIds, 1, outboundOrderDO.getId(), outboundOrderDO.getOutboundCode());
                // todo 未验证 删除波次单
                waveOrderIds.forEach(id ->
                        waveOrderService.deleteWaveOrder(id)
                );
            }
        }
        LambdaUpdateWrapper<OutboundOrderDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Objects.nonNull(outboundStatusReqVO.getOrderStatus()), OutboundOrderDO::getOrderStatus, outboundStatusReqVO.getOrderStatus());
        if (Objects.nonNull(outboundStatusReqVO.getSendStatus())) {
            updateWrapper.set(OutboundOrderDO::getSendStatus, outboundStatusReqVO.getSendStatus());
            updateWrapper.set(OutboundOrderDO::getSendTime, LocalDateTime.now());
        }
        updateWrapper.eq(OutboundOrderDO::getId, outboundStatusReqVO.getId());
        outboundOrderMapper.update(null, updateWrapper);
    }

    @Override
    public List<OutboundOrderDO> getOutboundListByIds(Collection<Long> outboundIds) {
        if (CollectionUtils.isAnyEmpty(outboundIds)) {
            return new ArrayList<>();
        }
        return outboundOrderMapper.selectByIds(outboundIds);
    }


}