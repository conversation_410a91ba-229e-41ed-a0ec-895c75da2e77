package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 分拣任务单主分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SortingTaskOrderPageReqVO extends PageParam {

    @Schema(description = "分拣单号")
    private String sortingOrderCode;

    @Schema(description = "客户名称", example = "李四")
    private String customerName;

    @Schema(description = "仓库ID（关联仓库表）", example = "21918")
    private Long warehouseId;

    @Schema(description = "分拣员")
    private String sorter;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "分拣时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sortingTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}