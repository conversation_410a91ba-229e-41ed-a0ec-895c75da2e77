package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 客户管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomerRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2460")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "客户编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("客户编码")
    private String code;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("客户名称")
    private String name;

    @Schema(description = "客户简称", example = "芋艿")
    @ExcelProperty("客户简称")
    private String shortName;

    @Schema(description = "客户类型（0内部，1外部，2其他）", example = "2")
    @ExcelProperty("客户类型（0内部，1外部，2其他）")
    private Integer type;

    @Schema(description = "客户英文名称", example = "张三")
    @ExcelProperty("客户英文名称")
    private String englishName;

    @Schema(description = "营业执照号")
    @ExcelProperty("营业执照号")
    private String businessLicense;

    @Schema(description = "客户描述", example = "你猜")
    @ExcelProperty("客户描述")
    private String description;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("联系人")
    private String contactPerson;

    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    @ExcelProperty("联系邮箱")
    private String contactEmail;

    @Schema(description = "所在地区(省市区)")
    @ExcelProperty("所在地区(省市区)")
    private String region;

    @Schema(description = "信用额度")
    @ExcelProperty("信用额度")
    private BigDecimal creditLimit;

    @Schema(description = "结算方式")
    @ExcelProperty("结算方式")
    private String settlementMethod;

    @Schema(description = "启用状态（0启用，1禁用）", example = "1")
    @ExcelProperty("启用状态（0启用，1禁用）")
    private Integer status;

    @Schema(description = "合作状态", example = "2")
    @ExcelProperty("合作状态")
    private String cooperationStatus;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}