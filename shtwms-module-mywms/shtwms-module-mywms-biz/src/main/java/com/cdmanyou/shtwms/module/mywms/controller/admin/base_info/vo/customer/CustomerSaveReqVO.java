package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 客户管理新增/修改 Request VO")
@Data
public class CustomerSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2460")
    private Long id;

    @Schema(description = "客户编码")
    private String code;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "客户名称不能为空")
    private String name;

    @Schema(description = "客户简称", example = "芋艿")
    private String shortName;

    @Schema(description = "客户类型（0内部，1外部，2其他）", example = "2")
    @NotNull(message = "客户类型不能为空") // 新增必填
    private Integer type;

    @Schema(description = "客户英文名称", example = "张三")
    private String englishName;

    @Schema(description = "营业执照号")
    private String businessLicense;

    @Schema(description = "客户描述", example = "你猜")
    private String description;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "联系人不能为空")
    private String contactPerson;

    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "联系电话不能为空")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "所在地区(省市区)")
    private String region;

    @Schema(description = "信用额度")
    private BigDecimal creditLimit;

    @Schema(description = "结算方式")
    private String settlementMethod;

    @Schema(description = "启用状态（0启用，1禁用）", example = "1")
    @NotNull(message = "启用状态不能为空") // 新增必填
    private Integer status;

    @Schema(description = "合作状态", example = "2")
    private String cooperationStatus;

}