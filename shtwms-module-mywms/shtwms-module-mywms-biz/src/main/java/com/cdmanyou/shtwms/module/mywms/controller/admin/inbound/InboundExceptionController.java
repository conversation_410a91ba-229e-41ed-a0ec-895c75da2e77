package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception.InboundExceptionPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception.InboundExceptionRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception.InboundExceptionSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundExceptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 入库异常处理单主")
@RestController
@RequestMapping("/mywms/inbound-exception")
@Validated
public class InboundExceptionController {

    @Resource
    private InboundExceptionService inboundExceptionService;

    @PostMapping("/create")
    @Operation(summary = "创建入库异常处理单主")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-exception:create')")
    public CommonResult<Long> createInboundException(@Valid @RequestBody InboundExceptionSaveReqVO createReqVO) {
        return success(inboundExceptionService.createInboundException(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新入库异常处理单主")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-exception:update')")
    public CommonResult<Boolean> updateInboundException(@Valid @RequestBody InboundExceptionSaveReqVO updateReqVO) {
        inboundExceptionService.updateInboundException(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除入库异常处理单主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:inbound-exception:delete')")
    public CommonResult<Boolean> deleteInboundException(@RequestParam("id") Long id) {
        inboundExceptionService.deleteInboundException(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得入库异常处理单主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-exception:query')")
    public CommonResult<InboundExceptionRespVO> getInboundException(@RequestParam("id") Long id) {
        InboundExceptionDO inboundException = inboundExceptionService.getInboundException(id);
        return success(BeanUtils.toBean(inboundException, InboundExceptionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得入库异常处理单主分页")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-exception:query')")
    public CommonResult<PageResult<InboundExceptionRespVO>> getInboundExceptionPage(@Valid InboundExceptionPageReqVO pageReqVO) {
        PageResult<InboundExceptionDO> pageResult = inboundExceptionService.getInboundExceptionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InboundExceptionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出入库异常处理单主 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-exception:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInboundExceptionExcel(@Valid InboundExceptionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InboundExceptionDO> list = inboundExceptionService.getInboundExceptionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "入库异常处理单主.xls", "数据", InboundExceptionRespVO.class,
                        BeanUtils.toBean(list, InboundExceptionRespVO.class));
    }

    // ==================== 子表（入库异常处理单明细） ====================

//    @GetMapping("/exception-detail/list-by-exception-id")
//    @Operation(summary = "获得入库异常处理单明细列表")
//    @Parameter(name = "exceptionId", description = "异常处理单ID（关联主表）")
//    @PreAuthorize("@ss.hasPermission('mywms:inbound-exception:query')")
//    public CommonResult<List<InboundExceptionDetailDO>> getExceptionDetailListByExceptionId(@RequestParam("exceptionId") Long exceptionId) {
//        return success(inboundExceptionService.getExceptionDetailListByExceptionId(exceptionId));
//    }

}