package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 * @date 2025/6/24 12:16
 */
@Data
public class WaveOrderDetailEasyRespVO {

    @Schema(description = "ID", example = "1")
    private Long id;

    @Schema(description = "出库单号", example = "123456789")
    private String outboundCode;

    @Schema(description = "产品名称", example = "产品A")
    private String productName;

    @Schema(description = "规格", example = "规格X")
    private String specification;

    @Schema(description = "规格", example = "规格X")
    private String warehouseName;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate productionDate;

    @Schema(description = "待拣货数量", example = "10")
    private Integer pendingPicking;

    @Schema(description = "待拣零散数量", example = "5")
    private Integer pendingSimplePicking;

    @Schema(description = "可用数量", example = "20")
    private Integer available;

    @Schema(description = "散货可用数量", example = "8")
    private Integer scatteredAvailable;

    @Schema(description = "总数量", example = "30")
    private Integer allCount;

    @Schema(description = "散货总数量", example = "15")
    private Integer scatteredCount;

    @Schema(description = "冻结数量", example = "3")
    private Integer freezeCount;

    @Schema(description = "散货冻结数量", example = "2")
    private Integer freezeScatteredCount;
}
