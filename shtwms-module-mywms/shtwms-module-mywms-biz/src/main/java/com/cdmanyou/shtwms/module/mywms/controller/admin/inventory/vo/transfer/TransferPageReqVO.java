package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台搜索实体")
@Data
public class TransferPageReqVO extends PageParam {

    @Schema(description = "转移单号")
    private String transferCode;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "状态（0待转移，1转移成功，2已作废）")
    private Integer status;

    @Schema(description = "操作人员ID")
    private String transferUser;

    @Schema(description = "转移搜索开始时间")
    private String transferSearchStartTime;

    @Schema(description = "转移搜索结束时间")
    private String transferSearchEndTime;


}