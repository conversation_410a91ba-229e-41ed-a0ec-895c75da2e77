package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.ShelvingManagementInfoVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.ShelvingManagementPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.ShelvingManagementPageRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 上架管理单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ShelvingManagementMapper extends BaseMapperX<ShelvingManagementDO> {

    ShelvingManagementInfoVO getShelvingManagementByCode(String code);

    Page<ShelvingManagementPageRespVO> selectPage(Page<ShelvingManagementPageRespVO> page, @Param("reqVO") ShelvingManagementPageReqVO reqVO);

    Long selectPageCount(ShelvingManagementPageReqVO pageReqVO);

    Long getMaxSequence(String dateStr);

}