<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.WaveOrderMapper">

    <select id="getWaveOrderPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderPageRespVO">
        select wo.id,
               wo.create_time,
               wo.wave_code,
               wo.wave_type,
               oo.outbound_code,
               w.name as warehouse_name,
               wo.wave_status,
               wo.picking_status,
               wo.picker,
               wo.remark
        from shtwms.wave_order wo
                 left join shtwms.wave_outbound_order woo on wo.id = woo.wellen_id
                 left join shtwms.outbound_order oo on woo.outbound_order_id = oo.id
                 left join shtwms.warehouse w on wo.warehouse_id = w.id
        where wo.deleted = 0
    </select>
</mapper>