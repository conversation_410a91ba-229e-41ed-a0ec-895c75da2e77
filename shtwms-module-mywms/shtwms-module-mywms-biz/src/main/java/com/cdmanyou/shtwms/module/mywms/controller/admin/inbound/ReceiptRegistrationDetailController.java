package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.ReceiptRegistrationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailPageRespVO;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ReceiptRegistrationDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 收货登记单明细")
@RestController
@RequestMapping("/mywms/receipt-registration-detail")
@Validated
public class ReceiptRegistrationDetailController {

    @Resource
    private ReceiptRegistrationDetailService receiptRegistrationDetailService;

    @PostMapping("/page")
    @Operation(summary = "获取收货登记明细")
    @PreAuthorize("@ss.hasPermission('mywms:receipt-registration-detail:query')")
    public CommonResult<PageResult<ReceiptRegistrationDetailPageRespVO>> getReceiptRegistrationPage(@Valid @RequestBody ReceiptRegistrationDetailPageReqVO pageReqVO) {
        return success(receiptRegistrationDetailService.getReceiptRegistrationPage(pageReqVO));
    }

}