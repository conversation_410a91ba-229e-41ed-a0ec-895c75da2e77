package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品货位关联表-记录货位上的商品数量分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductWareHouseLocationPageReqVO extends PageParam {

    @Schema(description = "入库单ID", example = "12637")
    private Long inboundOrderId;

    @Schema(description = "货主ID", example = "27126")
    private Long producerId;

    @Schema(description = "商品ID", example = "9107")
    private Long productId;

    @Schema(description = "货位ID", example = "30806")
    private Long warehouseLocationId;

    @Schema(description = "生产日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] productionDate;

    @Schema(description = "商品总数", example = "5930")
    private Integer count;

    @Schema(description = "占用数量")
    private Integer occupation;

    @Schema(description = "可用数量")
    private Integer available;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "零散总量", example = "30895")
    private Integer scatteredCount;

    @Schema(description = "零散占用量")
    private Integer scatteredOccupation;

    @Schema(description = "零散可用量")
    private Integer scatteredAvailable;

}