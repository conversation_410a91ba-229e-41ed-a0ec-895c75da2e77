package com.cdmanyou.shtwms.module.mywms.service.inventoryplan;

import javax.validation.*;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;

/**
 * 盘点计划明细 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryPlanDetailService {

    /**
     * 创建盘点计划明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanDetail(@Valid InventoryPlanDetailSaveReqVO createReqVO);

    /**
     * 更新盘点计划明细
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanDetail(@Valid InventoryPlanDetailSaveReqVO updateReqVO);

    /**
     * 删除盘点计划明细
     *
     * @param id 编号
     */
    void deletePlanDetail(Long id);

    /**
     * 获得盘点计划明细
     *
     * @param id 编号
     * @return 盘点计划明细
     */
    InventoryPlanDetailDO getPlanDetail(Long id);

    /**
     * 获得盘点计划明细分页
     *
     * @param pageReqVO 分页查询
     * @return 盘点计划明细分页
     */
    PageResult<InventoryPlanDetailDO> getPlanDetailPage(InventoryPlanDetailPageReqVO pageReqVO);

}