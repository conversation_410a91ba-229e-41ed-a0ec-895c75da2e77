package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ShelfDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 货架管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ShelfMapper extends BaseMapperX<ShelfDO> {

    default PageResult<ShelfDO> selectPage(ShelfPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ShelfDO>()
                .eqIfPresent(ShelfDO::getCode, reqVO.getCode())
                .likeIfPresent(ShelfDO::getName, reqVO.getName())
                .eqIfPresent(ShelfDO::getWarehouseId, reqVO.getWarehouseId())
                .eqIfPresent(ShelfDO::getAreaId, reqVO.getAreaId())
                .eqIfPresent(ShelfDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(ShelfDO::getWarehouseName, reqVO.getWarehouseName())
                .eqIfPresent(ShelfDO::getType, reqVO.getType())
                .eqIfPresent(ShelfDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ShelfDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ShelfDO::getId));
    }

    default List<ShelfDO> selectListByAreaId(Long areaId) {
        return selectList(new LambdaQueryWrapperX<ShelfDO>()
                .eq(ShelfDO::getAreaId, areaId)
                .orderByDesc(ShelfDO::getId));
    }

    int insert(ShelfDO entity);


}