package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 盘点计划主新增/修改 Request VO")
@Data
public class InventoryPlanSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27061")
    private Long id;

    @Schema(description = "盘点计划单号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotEmpty(message = "盘点计划单号不能为空")
    private String inventoryPlanCode;

    @Schema(description = "计划名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "第一次盘点")
    @NotEmpty(message = "计划名称不能为空")
    private String planName;

    @Schema(description = "盘点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "盘点类型不能为空")
    private String inventoryType;

    @Schema(description = "仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "17776")
    @NotNull(message = "仓库ID（关联仓库表）不能为空")
    private Long warehouseId;

    @Schema(description = "盘点日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-06-19")
    @NotNull(message = "盘点日期不能为空")
    private LocalDate inventoryDate;

    @Schema(description = "状态", example = "1")
    private String status;

    @Schema(description = "盘点计划明细列表")
    private List<InventoryPlanDetailDO> planDetails;

}