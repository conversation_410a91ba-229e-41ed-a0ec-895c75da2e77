package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 波次单主新增/修改 Request VO")
@Data
public class WaveOrderSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3539")
    private Long id;

    @Schema(description = "波次单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "波次单号不能为空")
    private String waveCode;

    @Schema(description = "波次类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "波次类型不能为空")
    private String waveType;

    @Schema(description = "订单数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "24052")
    @NotNull(message = "订单数量不能为空")
    private Integer orderCount;

    @Schema(description = "货品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "29338")
    @NotNull(message = "货品数量不能为空")
    private Integer productCount;

    @Schema(description = "仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "519")
    @NotNull(message = "仓库ID（关联仓库表）不能为空")
    private Long warehouseId;

    @Schema(description = "拣货员")
    private String picker;

    @Schema(description = "拣货状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "拣货状态不能为空")
    private String pickingStatus;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    @Schema(description = "波次单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "波次单状态不能为空")
    private String waveStatus;

    @Schema(description = "拣货时间")
    private LocalDateTime pickingTime;

    @Schema(description = "备注（非必填）", example = "你猜")
    private String remark;

    @Schema(description = "波次单明细列表")
    private List<WaveOrderDetailDO> waveOrderDetails;

}