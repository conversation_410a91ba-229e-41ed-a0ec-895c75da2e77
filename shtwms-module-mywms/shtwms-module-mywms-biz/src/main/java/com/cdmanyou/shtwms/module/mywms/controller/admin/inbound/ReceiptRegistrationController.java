package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration.*;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ReceiptRegistrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 收货登记单")
@RestController
@RequestMapping("/mywms/receipt-registration")
@Validated
public class ReceiptRegistrationController {

    @Resource
    private ReceiptRegistrationService receiptRegistrationService;

    @PostMapping("/save-submit")
    @Operation(summary = "保存/提交收货单")
    @PreAuthorize("@ss.hasPermission('mywms:receipt-registration:update')")
    public CommonResult<Boolean> saveReceiptRegistration(@Valid @RequestBody ReceiptRegistrationSaveVO saveReqVO) {
        receiptRegistrationService.saveReceiptRegistration(saveReqVO);
        return success(true);
    }

    @GetMapping("/getByCode")
    @Operation(summary = "通过收货单号获取收货单详情")
    @Parameter(name = "code", description = "收货单号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:receipt-registration:query')")
    public CommonResult<ReceiptRegistrationInfoVO> getReceiptRegistrationByCode(@RequestParam("code") String code) {
        return success(receiptRegistrationService.getReceiptRegistrationByCode(code));
    }

    @PostMapping("/page")
    @Operation(summary = "获得收货登记单分页")
    @PreAuthorize("@ss.hasPermission('mywms:receipt-registration:query')")
    public CommonResult<PageResult<ReceiptRegistrationPageRespVO>> getReceiptRegistrationPage(@Valid @RequestBody ReceiptRegistrationPageReqVO pageReqVO) {
        return success(receiptRegistrationService.getReceiptRegistrationPage(pageReqVO));
    }

    @PostMapping("/create-shelving-management")
    @Operation(summary = "创建上架单")
    @PreAuthorize("@ss.hasPermission('mywms:receipt-registration:create')")
    public CommonResult<Long> createShelvingManagement(@Valid @RequestBody ReceiptRegistrationCreateVO createReqVO) {
        return success(receiptRegistrationService.createShelvingManagement(createReqVO));
    }

}