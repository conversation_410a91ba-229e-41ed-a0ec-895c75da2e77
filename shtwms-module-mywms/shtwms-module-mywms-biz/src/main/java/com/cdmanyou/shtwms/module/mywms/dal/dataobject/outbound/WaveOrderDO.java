package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 波次单主 DO
 *
 * <AUTHOR>
 */
@TableName("wave_order")
@KeySequence("wave_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WaveOrderDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 波次单号
     */
    private String waveCode;
    /**
     * 波次类型
     */
    private Integer waveType;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 货品数量
     */
    private Integer productCount;
    /**
     * 仓库ID（关联仓库表）
     */
    private Long warehouseId;
    /**
     * 拣货员
     */
    private String picker;
    /**
     * 拣货员
     */
    private Long pickerId;
    /**
     * 拣货状态
     */
    private String pickingStatus;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 波次单状态
     */
    private String waveStatus;
    /**
     * 拣货时间
     */
    private LocalDateTime pickingTime;
    /**
     * 备注（非必填）
     */
    private String remark;

}