package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 总拣单主 DO
 *
 * <AUTHOR>
 */
@TableName("total_picking_order")
@KeySequence("total_picking_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PickingOrderDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 拣货单号
     */
    private String pickingOrderCode;
    /**
     * 波次单编号（关联波次单主表）
     */
    private String waveCode;
    /**
     * 待拣货总数
     */
    private Integer totalPending;
    /**
     * 已拣货总数
     */
    private Integer totalPicked;
    /**
     * 仓库ID（关联仓库表）
     */
    private Long warehouseId;
    /**
     * 拣货员
     */
    private String picker;
    /**
     * 托盘号（非必填）
     */
    private String trayCode;
    /**
     * 拣货容器（非必填）
     */
    private String containerType;
    /**
     * 拣货容器号（非必填）
     */
    private String containerCode;
    /**
     * 状态
     */
    private String status;

}