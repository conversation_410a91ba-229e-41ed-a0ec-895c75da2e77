package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 总拣单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PickingOrderDetailPageRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7290")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "货位编号")
    @ExcelProperty("货位编号")
    private String warehouseLocationCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格（非必填）")
    private String specification;

    @Schema(description = "生产日期（非必填）")
    @ExcelProperty("生产日期（非必填）")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "待拣货数量")
    @ExcelProperty("待拣货数量")
    private Integer pendingQuantity;

    @Schema(description = "已拣货数量")
    @ExcelProperty("已拣货数量")
    private Integer pickedQuantity;

    @Schema(description = "差异数量")
    @ExcelProperty("差异数量")
    private Integer differenceQuantity;

    @Schema(description = "待拣货零散数量")
    @ExcelProperty("待拣货零散数量")
    private Integer pendingSimpleQuantity;

    @Schema(description = "已拣货零散数量")
    @ExcelProperty("已拣货零散数量")
    private Integer pickedSimpleQuantity;

    @Schema(description = "差异零散数量")
    @ExcelProperty("差异零散数量")
    private Integer differenceSimpleQuantity;

    @Schema(description = "仓库")
    @ExcelProperty("仓库")
    private String warehouseName;

    @Schema(description = "拣货库区ID（关联库区表，非必填）", example = "17763")
    @ExcelProperty("拣货库区ID（关联库区表，非必填）")
    private Long areaId;

    @Schema(description = "拣货库位ID（关联货位表，非必填）", example = "22260")
    @ExcelProperty("拣货库位ID（关联货位表，非必填）")
    private Long locationId;

    @Schema(description = "总检单详情状态(0未确认,1已确认)", example = "22260")
    @ExcelProperty("总检单详情状态(0未确认,1已确认)")
    private Integer pickingDetailStatus;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createTime;

    @Schema(description = "总量")
    @ExcelProperty("总量")
    private Integer productCount;

    @Schema(description = "可用量")
    @ExcelProperty("可用量")
    private Integer productAvailable;

}