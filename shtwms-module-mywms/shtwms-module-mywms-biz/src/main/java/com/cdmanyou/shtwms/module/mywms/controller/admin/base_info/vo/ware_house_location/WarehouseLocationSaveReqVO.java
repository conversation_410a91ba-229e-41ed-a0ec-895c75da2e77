package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 货位管理新增/修改 Request VO")
@Data
public class WarehouseLocationSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23132")
    private Long id;

    @Schema(description = "货位编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "货位编码不能为空")
    private String code;

    @Schema(description = "货位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "货位名称不能为空")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "23872")
    @NotNull(message = "所属仓库ID（关联仓库表）不能为空")
    private Long warehouseId;

    @Schema(description = "所属库区ID（关联库区表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "30224")
    @NotNull(message = "所属库区ID（关联库区表）不能为空")
    private Long areaId;

    @Schema(description = "所属货架ID（关联货架表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "24501")
    @NotNull(message = "所属货架ID（关联货架表）不能为空")
    private Long shelfId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "长度(米)")
    private BigDecimal length;

    @Schema(description = "宽度(米)")
    private BigDecimal width;

    @Schema(description = "高度(米)")
    private BigDecimal height;

    @Schema(description = "承重(吨)")
    private BigDecimal weight;

    @Schema(description = "体积(立方米)")
    private BigDecimal volume;

    @Schema(description = "货位状态(1启用/0停用)", example = "2")
    private Integer status;

    @Schema(description = "卸货方式(0叉车卸货，1其他卸货方式)")
    private Integer unloadingMethod;
}
