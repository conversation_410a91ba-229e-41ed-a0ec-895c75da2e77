package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 上架管理单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ShelvingManagementDetailPageRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20983")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "上架单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6136")
    @ExcelProperty("上架单ID")
    private Long shelvingId;

    @Schema(description = "收货单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13575")
    @ExcelProperty("收货单ID")
    private Long receiptId;

    @Schema(description = "入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29764")
    @ExcelProperty("入库单ID")
    private Long inboundOrderId;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23939")
    @ExcelProperty("商品ID")
    private Long productId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String unit;

    @Schema(description = "商品批次号")
    @ExcelProperty("商品批次号")
    private String productBatchNumber;

    @Schema(description = "系统批次号")
    @ExcelProperty("系统批次号")
    private String systemBatchNumber;

    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate productionDate;

    @Schema(description = "待入库数量")
    @ExcelProperty("待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "收货数量（待上架数量）")
    @ExcelProperty("收货数量（待上架数量）")
    private Integer receivedQuantity;

    @Schema(description = "上架数量")
    @ExcelProperty("上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "托盘ID（关联托盘表，提交并上架时必填）", example = "5672")
    @ExcelProperty("托盘ID（关联托盘表，提交并上架时必填）")
    private Long trayId;

    @Schema(description = "仓库ID（关联仓库表，提交并上架时必填）", example = "27451")
    @ExcelProperty("仓库ID（关联仓库表，提交并上架时必填）")
    private Long warehouseId;

    @Schema(description = "库区ID（关联库区表，提交并上架时必填）", example = "16657")
    @ExcelProperty("库区ID（关联库区表，提交并上架时必填）")
    private Long areaId;

    @Schema(description = "库位ID（关联货位表，提交并上架时必填）", example = "22563")
    @ExcelProperty("库位ID（关联货位表，提交并上架时必填）")
    private Long locationId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}