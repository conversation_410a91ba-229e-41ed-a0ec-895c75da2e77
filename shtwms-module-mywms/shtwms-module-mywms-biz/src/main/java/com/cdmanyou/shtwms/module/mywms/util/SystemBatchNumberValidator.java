package com.cdmanyou.shtwms.module.mywms.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.cdmanyou.shtwms.framework.common.exception.ErrorCode;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 系统批次号校验工具类
 *
 * <AUTHOR>
 */
public class SystemBatchNumberValidator {

    /**
     * 校验系统批次号是否重复
     *
     * @param collection 需要校验的集合
     * @param batchNumberExtractor 系统批次号提取器
     * @param errorCode 重复时抛出的错误码
     * @param <T> 集合元素类型
     */
    public static <T> void validateNoDuplicate(Collection<T> collection, Function<T, String> batchNumberExtractor, ErrorCode errorCode) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }
        List<String> systemBatchNumbers = collection.stream()
                .map(batchNumberExtractor)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
        Set<String> uniqueBatchNumbers = new HashSet<>();
        for (String batchNumber : systemBatchNumbers) {
            if (!uniqueBatchNumbers.add(batchNumber)) {
                // 目前默认取为8位（系统批次号后8位默认为生产日期）
                String productCode = batchNumber.length() > 8 ?
                    batchNumber.substring(0, batchNumber.length() - 8) : batchNumber;
                throw exception(errorCode, productCode);
            }
        }
    }

    /**
     * 校验系统批次号是否重复（使用默认错误信息）
     *
     * @param collection 需要校验的集合
     * @param batchNumberExtractor 系统批次号提取器
     * @param <T> 集合元素类型
     */
    public static <T> void validateNoDuplicate(Collection<T> collection, Function<T, String> batchNumberExtractor) {
        validateNoDuplicate(collection, batchNumberExtractor, 
            new ErrorCode(1001_11111, "商品编号为{}的商品生产日期无法重复"));
    }
}
