package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/21 11:45
 */
@Data
public class PickingOrderDetailStatusReqVO {

    @NotNull
    @Schema(description = "总检单id")
    private Long pickingOrderId;

    @NotNull
    @Schema(description = "总检单详情id")
    private Long pickingDetailOrderId;

    @Schema(description = "拣货数量")
    private Integer pickedQuantity;

    @Schema(description = "拣货差异数量")
    private Integer differenceQuantity;

    @Schema(description = "零散拣货数量")
    private Integer pickedSimpleQuantity;

    @Schema(description = "零散拣货差异数量")
    private Integer differenceSimpleQuantity;

    @NotNull
    @Schema(description = "状态(0未确认,1已确认,2作废)",example = "1")
    private Integer pickingDetailStatus;
}
