package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 波次单明细新增/修改 Request VO")
@Data
public class WaveOrderDetailSaveReqVO {

    private Long id;

    @Schema(description = "波次单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "13302")
    @NotNull(message = "波次单ID（关联主表）不能为空")
    private Long waveId;

    @Schema(description = "出库单号（关联出库订单主表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "出库单号（关联出库订单主表）不能为空")
    private String outboundCode;

    @Schema(description = "客户名称", example = "赵六")
    private String customerName;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    private String unit;

    @Schema(description = "待拣货数量")
    private Integer pendingPicking;

    @Schema(description = "已拣货数量")
    private Integer pickedQuantity;

    @Schema(description = "差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "库区ID（关联库区表）", example = "26669")
    private Long areaId;

    @Schema(description = "库位ID（关联货位表）", example = "15581")
    private Long locationId;

}