package com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo;

import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "管理后台 - 盘点计划明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryPlanDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11316")
    private Long id;

    @Schema(description = "盘点计划ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "8868")
    private Long inventoryPlanId;

    @Schema(description = "仓库名")
    @ExcelProperty("仓库")
    private String warehouseName;

    /**
     * 货位号
     */
    @Schema(description = "货位")
    @ExcelProperty("货位")
    private String locationCode;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "商品批次号")
    @ExcelProperty("货品批次号")
    private String productBatchNumber;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate manufactureDate;
    /**
     * 有效期
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @Schema(description = "有效期")
    @ExcelProperty("有效期")
    private LocalDate effectiveDate;

    @Schema(description = "主计量单位类别（0件，1公斤）")
    @ExcelProperty("主计量单位")
    private Integer masterMeasure;

    @Schema(description = "账面数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存数量")
    private Integer bookQuantity;

    @Schema(description = "实盘数量")
    @ExcelProperty("实盘数量")
    private Integer actualQuantity;

    @Schema(description = "差异数量")
    @ExcelProperty("差异数量")
    private Integer differenceQuantity;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "副计量类别（0袋，1其他）")
    @ExcelProperty("副计量单位")
    private Integer deputyMeasure;

    /**
     * 零散账面数量
     */
    @Schema(description = "零散账面数量")
    @ExcelProperty("零散账面数量")
    private Integer scatteredBookQuantity;
    /**
     * 零散实盘数量（非必填）
     */
    @Schema(description = "零散实盘数量")
    @ExcelProperty("零散实盘数量")
    private Integer scatteredActualQuantity;
    /**
     * 零散差异数量（非必填）
     */
    @Schema(description = "零散差异数量")
    @ExcelProperty("零散差异数量")
    private Integer scatteredDifferenceQuantity;
    /**
     * 货位id
     */
    private Long locationId;




}