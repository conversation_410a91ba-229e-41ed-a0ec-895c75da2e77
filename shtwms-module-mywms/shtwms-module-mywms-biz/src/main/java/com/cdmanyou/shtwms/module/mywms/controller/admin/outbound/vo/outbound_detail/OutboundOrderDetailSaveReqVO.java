package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 出库订单明细新增/修改 Request VO")
@Data
public class OutboundOrderDetailSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3801")
    private Long id;

    @Schema(description = "出库单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "32711")
    @NotNull(message = "出库单ID（关联主表）不能为空")
    private Long outboundId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码（关联商品表）不能为空")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    private String unit;

    @Schema(description = "待出库数量")
    private Integer pendingQuantity;

    @Schema(description = "已出库数量")
    private Integer receivedQuantity;

    @Schema(description = "差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "出库价格", example = "15796")
    private BigDecimal outboundPrice;

    @Schema(description = "出库金额(含税额)")
    private BigDecimal outboundAmount;

    @Schema(description = "分拣完成时间")
    private LocalDateTime sortingCompleteTime;

    @Schema(description = "是否越库(0否，1是)")
    private Integer straddleWarehouse;

    @Schema(description = "出库体积")
    private Long volume;

    @Schema(description = "出库重量")
    private Long grossWeight;

    /**
     * 到期日期
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @Schema(description = "到期日期")
    private LocalDate expirationDate;

    /**
     * 零单位数量
     */
    @Schema(description = "零单位数量")
    private Integer simpleCount;
    /**
     * 产品批号
     */
    @Schema(description = "产品批号")
    private String productBatchNumber;

    @Schema(description = "波次单锁定数量")
    private Integer waveLockQuantity;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id", example = "1")
    private Long supplierId;

    /**
     * 总拣状态(0未完成，1完成)
     */
    @Schema(description = "总拣状态(0未完成，1完成)", example = "1")
    private Integer pickingStatus;

    /**
     * 仓类型(0成品仓1半成品仓,2原料仓)
     */
    @Schema(description = "仓类型(0成品仓1半成品仓,2原料仓)", example = "1")
    private Integer houseType;

}