package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.*;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.WaveOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.WaveOrderMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.WaveOutboundOrderMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderDetailService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.WaveOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.bo.ProductHouseLocationBO;
import com.cdmanyou.shtwms.module.system.api.user.AdminUserApi;
import com.cdmanyou.shtwms.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.WAVE_ORDER_NOT_EXISTS;

/**
 * 波次单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WaveOrderServiceImpl implements WaveOrderService {

    @Resource
    private WaveOrderMapper waveOrderMapper;

    @Resource
    private WaveOrderDetailMapper waveOrderDetailMapper;

    @Resource
    private OutboundOrderService outboundOrderService;

    @Resource
    private OutboundOrderDetailService outboundOrderDetailService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private WaveOutboundOrderMapper waveOutboundOrderMapper;

    @Resource
    private ProductWareHouseLocationMapper productWareHouseLocationMapper;

    @Resource
    private ProductWareHouseLocationService productWareHouseLocationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createWaveOrder(List<Long> outboundIds) {
        //查询出库单
        List<OutboundOrderDO> orderList = outboundOrderService.getOutboundListByIds(outboundIds);
        //查询出库单详情
        List<OutboundOrderDetailDO> orderDetailList = outboundOrderDetailService.getDetailListByOutboundIds(outboundIds);
        // 查看总检单员工
        List<AdminUserRespDTO> userRespDTOS = adminUserApi.getUserByRoleName("总拣").getCheckedData();
        //仓库id
        List<String> warehouseIds = orderDetailList.stream().flatMap(e -> Arrays.stream(e.getWarehouseId().split(","))).distinct().collect(Collectors.toList());
        for (String warehouseId : warehouseIds) {
            int index = ThreadLocalRandom.current().nextInt(userRespDTOS.size());
            //随机总拣人
            AdminUserRespDTO adminUserRespDTO = userRespDTOS.get(index);
            // 插入波次单主表
            WaveOrderDO waveOrder = new WaveOrderDO();
            waveOrder.setCreateTime(LocalDateTime.now());
            waveOrder.setWaveCode(createWaveOrderCode());
            waveOrder.setWaveType(0);
            waveOrder.setOrderCount(outboundIds.size());
            waveOrder.setProductCount(orderDetailList.size());
            waveOrder.setRemark("");
            waveOrder.setPicker(adminUserRespDTO.getNickname());
            waveOrder.setPickerId(adminUserRespDTO.getId());
            waveOrder.setWarehouseId(Long.parseLong(warehouseId));
            waveOrderMapper.insert(waveOrder);
            // 存入波次出库关系表
            List<OutboundOrderDetailDO> belongOutbound = orderDetailList.stream().filter(e -> !e.getWarehouseId().contains(warehouseId)).collect(Collectors.toList());
            List<WaveOutboundOrderDO> waveOutbound = new ArrayList<>();
            belongOutbound.forEach(outboundId -> {
                WaveOutboundOrderDO waveOutboundOrder = new WaveOutboundOrderDO();
                waveOutboundOrder.setOutboundOrderId(outboundId.getOutboundId());
                waveOutboundOrder.setWellenId(waveOrder.getId());
            });
            waveOutboundOrderMapper.insertBatch(waveOutbound);
            // 插入波次单子表
            List<WaveOrderDetailDO> waveOrderDetailDOS = new ArrayList<>();
            for (OutboundOrderDetailDO orderDetailDO : belongOutbound) {
                //出库单
                OutboundOrderDO outboundOrderDO = orderList.stream().filter(e -> !Objects.equals(e.getId(), orderDetailDO.getOutboundId())).collect(Collectors.toList()).get(0);
                Integer waitQuantity = orderDetailDO.getPendingQuantity() - orderDetailDO.getWaveLockQuantity();
                List<ProductHouseLocationBO> locationDOS = productWareHouseLocationMapper.selectListByWareHouse(orderDetailDO.getProductId(), orderDetailDO.getProductionDate(), warehouseId);
                for (ProductHouseLocationBO locationDO : locationDOS) {
                    WaveOrderDetailDO waveOrderDetailDO = BeanUtils.toBean(orderDetailDO, WaveOrderDetailDO.class);
                    waveOrderDetailDO.setWaveId(waveOrder.getId());
                    waveOrderDetailDO.setOutboundCode(outboundOrderDO.getOutboundCode());
                    waveOrderDetailDO.setCustomerName(outboundOrderDO.getCustomerName());
                    waveOrderDetailDO.setAreaId(locationDO.getAreaId());
                    waveOrderDetailDO.setLocationId(locationDO.getWarehouseLocationId());
                    if (locationDO.getAvailable()-locationDO.getOccupation() >= waitQuantity) {
                        waveOrderDetailDO.setPendingPicking(waitQuantity);
                        waveOrderDetailDOS.add(waveOrderDetailDO);
                        waitQuantity = 0;
                        //更新货品货位占用数量
                        productWareHouseLocationService.updateProductWarehouseLocationOccupation(locationDO.getId(),locationDO.getOccupation()+waitQuantity);
                        break;
                    } else {
                        waveOrderDetailDO.setPendingPicking(locationDO.getAvailable());
                        waitQuantity -= locationDO.getAvailable();
                        //更新货品货位占用数量
                        productWareHouseLocationService.updateProductWarehouseLocationOccupation(locationDO.getId(),locationDO.getAvailable());
                    }
                    waveOrderDetailDOS.add(waveOrderDetailDO);
                }
                outboundOrderDetailService.updateWaveLockQuantity(orderDetailDO.getId(), orderDetailDO.getPendingQuantity() - waitQuantity);
            }
            waveOrderDetailMapper.insertBatch(waveOrderDetailDOS);
        }

        // 返回
        return 0L;
    }

    private String createWaveOrderCode() {
        // 生成日期部分
        String datePart = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE); // yyyyMMdd

        // 生成Redis key（包含日期）
        String redisKey = "waveOrderCode:" + datePart;

        // 原子递增（从1开始）
        Long sequence = redisTemplate.opsForValue().increment(redisKey, 1);
        if (sequence == null) {
            // 递增失败时重试一次
            sequence = Optional.ofNullable(redisTemplate.opsForValue().increment(redisKey, 1))
                    .orElseThrow(() -> new RuntimeException("Redis increment failed"));
        }

        // 首次递增时设置过期时间
        if (sequence == 1) {
            setKeyWithDailyExpire(redisKey);
        }

        // 拼接完整订单号
        return "BCD-" + datePart + "-" + String.format("%03d", sequence);
    }

    private void setKeyWithDailyExpire(String key) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().plusDays(1).atStartOfDay();
        long secondsUntilMidnight = now.until(midnight, ChronoUnit.SECONDS);

        redisTemplate.expire(key, secondsUntilMidnight, TimeUnit.SECONDS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWaveOrder(WaveOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateWaveOrderExists(updateReqVO.getId());
        // 更新
        WaveOrderDO updateObj = BeanUtils.toBean(updateReqVO, WaveOrderDO.class);
        waveOrderMapper.updateById(updateObj);

        // 更新子表
        updateWaveOrderDetailList(updateReqVO.getId(), updateReqVO.getWaveOrderDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWaveOrder(Long id) {
        // 校验存在
        validateWaveOrderExists(id);
        // 删除
        waveOrderMapper.deleteById(id);

        // 删除子表
        deleteWaveOrderDetailByWaveId(id);
    }

    private void validateWaveOrderExists(Long id) {
        if (waveOrderMapper.selectById(id) == null) {
            throw exception(WAVE_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public WaveOrderDO getWaveOrder(Long id) {
        return waveOrderMapper.selectById(id);
    }

    @Override
    public PageResult<WaveOrderDO> getWaveOrderPage(WaveOrderPageReqVO pageReqVO) {
        return waveOrderMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（波次单明细） ====================

    @Override
    public List<WaveOrderDetailDO> getWaveOrderDetailListByWaveId(Long waveId) {
        return waveOrderDetailMapper.selectListByWaveId(waveId);
    }

    private void createWaveOrderDetailList(Long waveId, List<WaveOrderDetailDO> list) {
        list.forEach(o -> o.setWaveId(waveId));
        waveOrderDetailMapper.insertBatch(list);
    }

    private void updateWaveOrderDetailList(Long waveId, List<WaveOrderDetailDO> list) {
        deleteWaveOrderDetailByWaveId(waveId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createWaveOrderDetailList(waveId, list);
    }

    private void deleteWaveOrderDetailByWaveId(Long waveId) {
        waveOrderDetailMapper.deleteByWaveId(waveId);
    }

}