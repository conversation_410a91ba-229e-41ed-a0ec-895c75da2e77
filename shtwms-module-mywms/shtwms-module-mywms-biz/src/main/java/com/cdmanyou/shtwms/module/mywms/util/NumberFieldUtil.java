package com.cdmanyou.shtwms.module.mywms.util;

import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 数字字段工具类
 *
 * <AUTHOR>
 */
@Service
public class NumberFieldUtil {

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取集合中整数字段的合计值
     *
     * @param collection 需要获取的集合
     * @param numberField 整数字段名
     * @param <T> 集合元素类型
     */
    public static <T> int getIntFieldSum(Collection<T> collection, Function<T, Integer> numberField) {
        if (CollUtil.isEmpty(collection)) {
            return 0;
        }
        return collection.stream()
                .map(numberField)
                .filter(Objects::nonNull) // 过滤掉null值
                .reduce(0, Integer::sum);
    }

    public static <T> BigDecimal getDecimalFieldSum(Collection<T> collection, Function<T, BigDecimal> numberField) {
        if (CollUtil.isEmpty(collection)) {
            return BigDecimal.ZERO;
        }
        return collection.stream()
                .map(numberField)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    public String createWaveOrderCode(String key,String tittle) {
        // 生成日期部分
        String datePart = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE); // yyyyMMdd

        // 生成Redis key（包含日期）
        String redisKey = key + datePart;

        // 原子递增（从1开始）
        Long sequence = redisTemplate.opsForValue().increment(redisKey, 1);
        if (sequence == null) {
            // 递增失败时重试一次
            sequence = Optional.ofNullable(redisTemplate.opsForValue().increment(redisKey, 1))
                    .orElseThrow(() -> new RuntimeException("Redis increment failed"));
        }

        // 首次递增时设置过期时间
        if (sequence == 1) {
            setKeyWithDailyExpire(redisKey);
        }

        // 拼接完整订单号
        return tittle +"-"+ datePart + "-" + String.format("%04d", sequence);
    }


    private void setKeyWithDailyExpire(String key) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().plusDays(1).atStartOfDay();
        long secondsUntilMidnight = now.until(midnight, ChronoUnit.SECONDS);

        redisTemplate.expire(key, secondsUntilMidnight, TimeUnit.SECONDS);
    }
}
