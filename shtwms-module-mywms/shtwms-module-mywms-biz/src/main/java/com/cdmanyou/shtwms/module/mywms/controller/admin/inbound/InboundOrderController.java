package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.inbound.InboundOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 入库单")
@RestController
@RequestMapping("/mywms/inbound-order")
@Validated
public class InboundOrderController {

    @Resource
    private InboundOrderService inboundOrderService;

    @PostMapping("/input")
    @Operation(summary = "创建入库单 - 录入")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-order:input')")
    public CommonResult<Long> inputInboundOrder(@Valid @RequestBody InboundOrderInputVO inputVO) {
        return success(inboundOrderService.inputInboundOrder(inputVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新入库单")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-order:update')")
    public CommonResult<Boolean> updateInboundOrder(@Valid @RequestBody InboundOrderInputVO inputVO) {
        inboundOrderService.updateInboundOrder(inputVO);
        return success(true);
    }

    @PostMapping("/submit")
    @Operation(summary = "提交入库单")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-order:submit')")
    public CommonResult<String> submitInboundOrder(@Valid @RequestBody InboundOrderSubmitVO submitVO) {
        return success(inboundOrderService.submitInboundOrder(submitVO));
    }

    @GetMapping("/getByCode")
    @Operation(summary = "通过入库单号获取入库单详情")
    @Parameter(name = "code", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-order:query')")
    public CommonResult<InboundOrderInfoVO> getInboundOrderByCode(@RequestParam("code") String code) {
        return success(inboundOrderService.getInboundOrderByCode(code));
    }

    @PostMapping("/page")
    @Operation(summary = "获得入库单分页")
    @PreAuthorize("@ss.hasPermission('mywms:inbound-order:query')")
    public CommonResult<PageResult<InboundOrderPageRespVO>> getInboundOrderPage(@Valid @RequestBody InboundOrderPageReqVO pageReqVO) {
        return success(inboundOrderService.getInboundOrderPage(pageReqVO));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出入库单 Excel")
//    @PreAuthorize("@ss.hasPermission('mywms:inbound-order:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportInboundOrderExcel(@Valid InboundOrderPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<InboundOrderDO> list = inboundOrderService.getInboundOrderPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "入库单.xls", "数据", InboundOrderPageRespVO.class,
//                        BeanUtils.toBean(list, InboundOrderPageRespVO.class));
//    }


}