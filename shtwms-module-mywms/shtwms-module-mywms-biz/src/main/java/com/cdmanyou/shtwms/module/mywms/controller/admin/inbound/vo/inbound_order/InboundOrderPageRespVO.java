package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 入库单主 Response VO")
@Data
public class InboundOrderPageRespVO {

    @Schema(description = "自增ID")
    private Long id;

    @Schema(description = "入库单号")
    private String orderCode;

    @Schema(description = "入库类型（0采购入库，1退货入库，2其他入库）", example = "2")
    private Integer inboundType;

    @Schema(description = "入库单状态（0草稿,1已提交,2作废）", example = "2")
    private Integer inboundOrderStatus;

    @Schema(description = "入库单总额（含税额）")
    private BigDecimal totalAmount;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "货主ID（关联客户表）", example = "13373")
    private Long producerId;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "货主编码")
    private String producerCode;

    @Schema(description = "单位ID")
    private Long unitId;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "单位类别（0供应商，1客户）")
    private Integer unitType;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "商品名称", example = "芋艿")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "商品单价")
    private BigDecimal productPrice;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "到期日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate expirationDate;

    @Schema(description = "待入库数量")
    private Integer pendingQuantity;

    @Schema(description = "拆零数量")
    private Integer looseQuantity;

    @Schema(description = "入库重量")
    private String inboundWeight;

    @Schema(description = "入库体积")
    private String inboundVolume;

    @Schema(description = "温区（0冷藏，1冷冻）")
    private Integer inboundWeightUnit;

    @Schema(description = "入库板数")
    private Integer inboundQuantity;

    @Schema(description = "待入库商品总数")
    private Integer pendingQuantityTotal;

    @Schema(description = "待入库商品零散总数")
    private Integer pendingScatteredQuantityTotal;

    @Schema(description = "提交人ID")
    private Long submitOperator;

    @Schema(description = "提交人名称")
    private String submitOperatorName;

    @Schema(description = "提交时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime submitTime;

    @Schema(description = "到货登记时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime acceptanceCompleteTime;

    @Schema(description = "验收完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime receiptCompleteTime;

    @Schema(description = "上架完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime shelvingCompleteTime;

    @Schema(description = "创建人ID")
    private String creator;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "备注")
    private String remark;

}