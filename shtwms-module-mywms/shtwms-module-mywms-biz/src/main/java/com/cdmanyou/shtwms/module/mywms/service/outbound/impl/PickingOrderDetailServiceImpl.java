package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail.PickingOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.PickingOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderDetailService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.PICKING_ORDER_DETAIL_NOT_EXISTS;

/**
 * 总拣单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PickingOrderDetailServiceImpl implements PickingOrderDetailService {

    @Resource
    private PickingOrderDetailMapper pickingOrderDetailMapper;

    @Override
    public Long createPickingOrderDetail(PickingOrderDetailSaveReqVO createReqVO) {
        // 插入
        PickingOrderDetailDO pickingOrderDetail = BeanUtils.toBean(createReqVO, PickingOrderDetailDO.class);
        pickingOrderDetailMapper.insert(pickingOrderDetail);
        // 返回
        return pickingOrderDetail.getId();
    }

    @Override
    public void updatePickingOrderDetail(PickingOrderDetailSaveReqVO updateReqVO) {
        // 校验存在
        validatePickingOrderDetailExists(updateReqVO.getId());
        // 更新
        PickingOrderDetailDO updateObj = BeanUtils.toBean(updateReqVO, PickingOrderDetailDO.class);
        pickingOrderDetailMapper.updateById(updateObj);
    }

    @Override
    public void deletePickingOrderDetail(Long id) {
        // 校验存在
        validatePickingOrderDetailExists(id);
        // 删除
        pickingOrderDetailMapper.deleteById(id);
    }

    private void validatePickingOrderDetailExists(Long id) {
        if (pickingOrderDetailMapper.selectById(id) == null) {
            throw exception(PICKING_ORDER_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public PickingOrderDetailDO getPickingOrderDetail(Long id) {
        return pickingOrderDetailMapper.selectById(id);
    }

    @Override
    public PageResult<PickingOrderDetailDO> getPickingOrderDetailPage(PickingOrderDetailPageReqVO pageReqVO) {
        return pickingOrderDetailMapper.selectPage(pageReqVO,null);
    }

}