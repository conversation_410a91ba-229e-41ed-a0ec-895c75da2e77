package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 总拣单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PickingOrderDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7290")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "拣货单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "28493")
    @ExcelProperty("拣货单ID（关联主表）")
    private Long pickingOrderId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    @ExcelProperty("单位（非必填）")
    private String unit;

    @Schema(description = "待拣货数量")
    @ExcelProperty("待拣货数量")
    private Integer pendingQuantity;

    @Schema(description = "已拣货数量")
    @ExcelProperty("已拣货数量")
    private Integer pickedQuantity;

    @Schema(description = "差异数量")
    @ExcelProperty("差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    @ExcelProperty("批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    @ExcelProperty("生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "拣货库区ID（关联库区表，非必填）", example = "17763")
    @ExcelProperty("拣货库区ID（关联库区表，非必填）")
    private Long areaId;

    @Schema(description = "拣货库位ID（关联货位表，非必填）", example = "22260")
    @ExcelProperty("拣货库位ID（关联货位表，非必填）")
    private Long locationId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}