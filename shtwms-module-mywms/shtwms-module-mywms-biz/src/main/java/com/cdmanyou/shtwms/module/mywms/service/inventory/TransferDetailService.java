package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;

import javax.validation.Valid;

/**
 * 货品转移明细 Service 接口
 *
 * <AUTHOR>
 */
public interface TransferDetailService {

    /**
     * 创建货品转移明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransferDetail(@Valid TransferDetailSaveReqVO createReqVO);

    /**
     * 更新货品转移明细
     *
     * @param updateReqVO 更新信息
     */
    void updateTransferDetail(@Valid TransferDetailSaveReqVO updateReqVO);

    /**
     * 删除货品转移明细
     *
     * @param id 编号
     */
    void deleteTransferDetail(Long id);

    /**
     * 获得货品转移明细
     *
     * @param id 编号
     * @return 货品转移明细
     */
    TransferDetailDO getTransferDetail(Long id);

    /**
     * 获得货品转移明细分页
     *
     * @param pageReqVO 分页查询
     * @return 货品转移明细分页
     */
    PageResult<TransferDetailDO> getTransferDetailPage(TransferDetailPageReqVO pageReqVO);

}