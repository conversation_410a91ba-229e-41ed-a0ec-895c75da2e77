package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 库区管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WareHouseAreaPageReqVO extends PageParam {

    @Schema(description = "库区编码")
    private String code;

    @Schema(description = "库区名称", example = "王五")
    private String name;

    @Schema(description = "所属仓库ID（关联仓库表）", example = "1909")
    private Long warehouseId;

    @Schema(description = "库区类型", example = "1")
    private String type;

    @Schema(description = "库区状态(1启用/0停用)", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}