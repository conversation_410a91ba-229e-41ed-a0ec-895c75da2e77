package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail;

import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;

@Schema(description = "收货登记明细搜索实体")
@Data
public class ReceiptRegistrationDetailPageReqVO extends PageParam {

    @Schema(description = "收货单号")
    private String receiptRegistrationCode;

    @Schema(description = "入库单号")
    private String inboundOrderCode;

    @Schema(description = "收货状态（0待提交，1待上架，2已上架）")
    private Integer receiptStatus;

    @Schema(description = "收货员名称")
    private String receiptOperatorName;

    @Schema(description = "上架状态")
    private Integer shelvingStatus;

    @Schema(description = "货品ID")
    private Integer productId;

    @Schema(description = "仓库ID")
    private Integer warehouseId;

    @Schema(description = "收货搜索开始时间")
    private String receiptSearchStartTime;

    @Schema(description = "收货搜索结束时间")
    private String receiptSearchEndTime;

}
