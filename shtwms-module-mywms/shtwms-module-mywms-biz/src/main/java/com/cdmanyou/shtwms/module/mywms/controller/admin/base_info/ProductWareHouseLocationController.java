package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location.ProductWareHouseLocationSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ProductWareHouseLocationDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品货位关联表-记录货位上的商品数量")
@RestController
@RequestMapping("/product/warehouse-location")
@Validated
public class ProductWareHouseLocationController {

    @Resource
    private ProductWareHouseLocationService productWareHouseLocationService;

    @PostMapping("/create")
    @Operation(summary = "创建商品货位关联表-记录货位上的商品数量")
    @PreAuthorize("@ss.hasPermission('product:warehouse-location:create')")
    public CommonResult<Long> createProductWarehouseLocation(@Valid @RequestBody ProductWareHouseLocationSaveReqVO createReqVO) {
        return success(productWareHouseLocationService.createProductWarehouseLocation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品货位关联表-记录货位上的商品数量")
    @PreAuthorize("@ss.hasPermission('product:warehouse-location:update')")
    public CommonResult<Boolean> updateProductWarehouseLocation(@Valid @RequestBody ProductWareHouseLocationSaveReqVO updateReqVO) {
        productWareHouseLocationService.updateProductWarehouseLocation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品货位关联表-记录货位上的商品数量")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:warehouse-location:delete')")
    public CommonResult<Boolean> deleteProductWarehouseLocation(@RequestParam("id") Long id) {
        productWareHouseLocationService.deleteProductWarehouseLocation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品货位关联表-记录货位上的商品数量")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:warehouse-location:query')")
    public CommonResult<ProductWareHouseLocationRespVO> getProductWarehouseLocation(@RequestParam("id") Long id) {
        ProductWareHouseLocationDO warehouseLocation = productWareHouseLocationService.getProductWarehouseLocation(id);
        return success(BeanUtils.toBean(warehouseLocation, ProductWareHouseLocationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品货位关联表-记录货位上的商品数量分页")
    @PreAuthorize("@ss.hasPermission('product:warehouse-location:query')")
    public CommonResult<PageResult<ProductWareHouseLocationRespVO>> getProductWarehouseLocationPage(@Valid ProductWareHouseLocationPageReqVO pageReqVO) {
        PageResult<ProductWareHouseLocationDO> pageResult = productWareHouseLocationService.getProductWarehouseLocationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductWareHouseLocationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品货位关联表-记录货位上的商品数量 Excel")
    @PreAuthorize("@ss.hasPermission('product:warehouse-location:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductWarehouseLocationExcel(@Valid ProductWareHouseLocationPageReqVO pageReqVO,
                                                    HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductWareHouseLocationDO> list = productWareHouseLocationService.getProductWarehouseLocationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品货位关联表-记录货位上的商品数量.xls", "数据", ProductWareHouseLocationRespVO.class,
                        BeanUtils.toBean(list, ProductWareHouseLocationRespVO.class));
    }


}

