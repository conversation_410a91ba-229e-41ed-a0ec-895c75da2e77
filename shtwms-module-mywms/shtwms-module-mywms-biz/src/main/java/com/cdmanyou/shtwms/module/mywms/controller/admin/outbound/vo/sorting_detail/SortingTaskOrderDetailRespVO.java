package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 分拣任务单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SortingTaskOrderDetailRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31550")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "分拣任务单ID（关联主表）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2883")
    @ExcelProperty("分拣任务单ID（关联主表）")
    private Long sortingOrderId;

    @Schema(description = "商品编码（关联商品表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码（关联商品表）")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "规格（非必填）")
    @ExcelProperty("规格（非必填）")
    private String specification;

    @Schema(description = "单位（非必填）")
    @ExcelProperty("单位（非必填）")
    private String unit;

    @Schema(description = "待分拣数量")
    @ExcelProperty("待分拣数量")
    private Integer pendingSortingQuantity;

    @Schema(description = "已分拣数量")
    @ExcelProperty("已分拣数量")
    private Integer sortedQuantity;

    @Schema(description = "差异数量")
    @ExcelProperty("差异数量")
    private Integer differenceQuantity;

    @Schema(description = "批次号（非必填）")
    @ExcelProperty("批次号（非必填）")
    private String batchNumber;

    @Schema(description = "生产日期（非必填）")
    @ExcelProperty("生产日期（非必填）")
    private LocalDate productionDate;

    @Schema(description = "拣货容器", example = "2")
    @ExcelProperty("拣货容器")
    private String pickingContainerType;

    @Schema(description = "拣货容器号")
    @ExcelProperty("拣货容器号")
    private String pickingContainerCode;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}