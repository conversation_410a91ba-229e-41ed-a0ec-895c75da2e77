package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.shelf.ShelfSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.ShelfDO;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ShelfService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 货架管理")
@RestController
@RequestMapping("/mywms/shelf")
@Validated
public class ShelfController {

    @Resource
    private ShelfService shelfService;

    @PostMapping("/create")
    @Operation(summary = "创建货架管理")
    @PreAuthorize("@ss.hasPermission('mywms:shelf:create')")
    public CommonResult<Long> createShelf(@Valid @RequestBody ShelfSaveReqVO createReqVO) {
        return success(shelfService.createShelf(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货架管理")
    @PreAuthorize("@ss.hasPermission('mywms:shelf:update')")
    public CommonResult<Boolean> updateShelf(@Valid @RequestBody ShelfSaveReqVO updateReqVO) {
        shelfService.updateShelf(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除货架管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:shelf:delete')")
    public CommonResult<Boolean> deleteShelf(@RequestParam("id") Long id) {
        shelfService.deleteShelf(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得货架管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:shelf:query')")
    public CommonResult<ShelfRespVO> getShelf(@RequestParam("id") Long id) {
        ShelfDO shelf = shelfService.getShelf(id);
        return success(BeanUtils.toBean(shelf, ShelfRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货架管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:shelf:query')")
    public CommonResult<PageResult<ShelfRespVO>> getShelfPage(@Valid ShelfPageReqVO pageReqVO) {
        PageResult<ShelfDO> pageResult = shelfService.getShelfPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ShelfRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出货架管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:shelf:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportShelfExcel(@Valid ShelfPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ShelfDO> list = shelfService.getShelfPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "货架管理.xls", "数据", ShelfRespVO.class,
                        BeanUtils.toBean(list, ShelfRespVO.class));
    }

    @PutMapping("/updatestatus")
    @Operation(summary = "更新货架状态")
    @Parameters({
            @Parameter(name = "id", description = "货架ID", required = true),
            @Parameter(name = "status", description = "状态(1启用/0停用)", required = true)
    })
    @PreAuthorize("@ss.hasPermission('mywms:shelf:update')")
    public CommonResult<Boolean> updateShelfStatus(
            @RequestParam("id") Long id,
            @RequestParam("status") @Min(0) @Max(1) Integer status) {

        shelfService.updateShelfStatus(id, status);
        return success(true);
    }

    @GetMapping("/listbyareaid")
    @Operation(summary = "根据库区ID获取货架列表")
    @Parameter(name = "areaId", description = "库区ID", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:shelf:query')")
    public CommonResult<List<ShelfDO>> getShelfListByAreaId(
            @RequestParam("areaId") Long areaId) {

        // 参数校验
        if (areaId == null || areaId <= 0) {
            throw new IllegalArgumentException("无效的库区ID");
        }

        List<ShelfDO> list = shelfService.getShelfListByAreaId(areaId);
        return success(list);
    }



}