package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.bouncycastle.cert.dane.DANEEntrySelector;

@Schema(description = "库存预警列表实体")
@Data
public class InventoryPageRespVO {

    @Schema(description = "货品ID")
    private Long productId;

    @Schema(description = "货品名称")
    private String productName;

    @Schema(description = "货品类别ID")
    private Long categoryId;

    @Schema(description = "货品类别名称")
    private String categoryName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "最大库存数")
    private Integer maxStock;

    @Schema(description = "当前库存数")
    private Integer currentStock;

    @Schema(description = "安全库存数")
    private Integer safetyStock;

    @Schema(description = "库存预警阀值")
    private Integer stockWarningThreshold;

    @Schema(description = "库存状态（0安全库存，1库存过高，2库存不足）")
    private Integer inventoryStatus;
}
