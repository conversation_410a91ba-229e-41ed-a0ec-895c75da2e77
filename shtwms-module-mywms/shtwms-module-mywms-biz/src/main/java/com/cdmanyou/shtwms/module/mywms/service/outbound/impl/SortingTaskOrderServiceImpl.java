package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.SortingTaskOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.SortingTaskOrderMapper;
import com.cdmanyou.shtwms.module.mywms.service.outbound.SortingTaskOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.SORTING_TASK_ORDER_NOT_EXISTS;

/**
 * 分拣任务单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SortingTaskOrderServiceImpl implements SortingTaskOrderService {

    @Resource
    private SortingTaskOrderMapper sortingTaskOrderMapper;
    @Resource
    private SortingTaskOrderDetailMapper sortingTaskOrderDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSortingTaskOrder(SortingTaskOrderSaveReqVO createReqVO) {
        // 插入
        SortingTaskOrderDO sortingTaskOrder = BeanUtils.toBean(createReqVO, SortingTaskOrderDO.class);
        sortingTaskOrderMapper.insert(sortingTaskOrder);

        // 插入子表
        createSortingTaskOrderDetailList(sortingTaskOrder.getId(), createReqVO.getSortingTaskOrderDetails());
        // 返回
        return sortingTaskOrder.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSortingTaskOrder(SortingTaskOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateSortingTaskOrderExists(updateReqVO.getId());
        // 更新
        SortingTaskOrderDO updateObj = BeanUtils.toBean(updateReqVO, SortingTaskOrderDO.class);
        sortingTaskOrderMapper.updateById(updateObj);

        // 更新子表
        updateSortingTaskOrderDetailList(updateReqVO.getId(), updateReqVO.getSortingTaskOrderDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSortingTaskOrder(Long id) {
        // 校验存在
        validateSortingTaskOrderExists(id);
        // 删除
        sortingTaskOrderMapper.deleteById(id);

        // 删除子表
        deleteSortingTaskOrderDetailBySortingOrderId(id);
    }

    private void validateSortingTaskOrderExists(Long id) {
        if (sortingTaskOrderMapper.selectById(id) == null) {
            throw exception(SORTING_TASK_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public SortingTaskOrderDO getSortingTaskOrder(Long id) {
        return sortingTaskOrderMapper.selectById(id);
    }

    @Override
    public PageResult<SortingTaskOrderDO> getSortingTaskOrderPage(SortingTaskOrderPageReqVO pageReqVO) {
        return sortingTaskOrderMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（分拣任务单明细） ====================

    @Override
    public List<SortingTaskOrderDetailDO> getSortingTaskOrderDetailListBySortingOrderId(Long sortingOrderId) {
        return sortingTaskOrderDetailMapper.selectListBySortingOrderId(sortingOrderId);
    }

    private void createSortingTaskOrderDetailList(Long sortingOrderId, List<SortingTaskOrderDetailDO> list) {
        list.forEach(o -> o.setSortingOrderId(sortingOrderId));
        sortingTaskOrderDetailMapper.insertBatch(list);
    }

    private void updateSortingTaskOrderDetailList(Long sortingOrderId, List<SortingTaskOrderDetailDO> list) {
        deleteSortingTaskOrderDetailBySortingOrderId(sortingOrderId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createSortingTaskOrderDetailList(sortingOrderId, list);
    }

    private void deleteSortingTaskOrderDetailBySortingOrderId(Long sortingOrderId) {
        sortingTaskOrderDetailMapper.deleteBySortingOrderId(sortingOrderId);
    }

}