package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound.OutboundOrderRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave.WaveOrderStatusReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailEasyRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.*;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.ProductWareHouseLocationMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.*;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderDetailService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderService;
import com.cdmanyou.shtwms.module.mywms.service.outbound.WaveOrderService;
import com.cdmanyou.shtwms.module.system.api.user.AdminUserApi;
import com.cdmanyou.shtwms.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.TIME_DIFFERENT;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.WAVE_ORDER_NOT_EXISTS;

/**
 * 波次单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WaveOrderServiceImpl implements WaveOrderService {

    @Resource
    private WaveOrderMapper waveOrderMapper;

    @Resource
    private WaveOrderDetailMapper waveOrderDetailMapper;

    @Resource
    private OutboundOrderMapper outboundOrderMapper;

    @Resource
    private OutboundOrderDetailService outboundOrderDetailService;

    @Resource
    private PickingOrderService pickingOrderService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private WaveOutboundOrderMapper waveOutboundOrderMapper;

    @Resource
    private OrderDetailProductWarehouseMapper orderDetailProductWarehouseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> createWaveOrder(List<Long> outboundIds) {
        List<OutboundOrderDO> orderDOS = outboundOrderMapper.selectByIds(outboundIds);
        List<Integer> collect = orderDOS.stream().map(OutboundOrderDO::getTimePeriod).distinct().collect(Collectors.toList());
        if (collect.size() > 1) {
            throw exception(TIME_DIFFERENT);
        }
        List<Long> result = new ArrayList<>();
        // 查询出库单详情
        List<OutboundOrderDetailDO> orderDetailList = outboundOrderDetailService.getDetailListByOutboundIds(outboundIds);
        // 查看总检单员工
        List<AdminUserRespDTO> userRespDTOS = adminUserApi.getUserByRoleName("总拣").getCheckedData();
        // 仓库id
        List<OrderDetailProductWarehouseDO> orderDetailProductWarehouseDOS = orderDetailProductWarehouseMapper.selectByOutboundDetailIds(orderDetailList.stream().map(OutboundOrderDetailDO::getId).collect(Collectors.toList()));
        Set<Long> warehouseIds = orderDetailProductWarehouseDOS.stream().map(OrderDetailProductWarehouseDO::getWarehouseId).collect(Collectors.toSet());
        for (Long warehouseId : warehouseIds) {
            int index = ThreadLocalRandom.current().nextInt(userRespDTOS.size());
            //随机总拣人
            AdminUserRespDTO adminUserRespDTO = userRespDTOS.get(index);
            // 插入波次单主表
            WaveOrderDO waveOrder = new WaveOrderDO();
            waveOrder.setCreateTime(LocalDateTime.now());
            waveOrder.setWaveCode(createWaveOrderCode());
            waveOrder.setWaveType(0);
            waveOrder.setOrderCount(outboundIds.size());
            waveOrder.setProductCount(orderDetailList.size());
            waveOrder.setRemark("");
            waveOrder.setPicker(adminUserRespDTO.getNickname());
            waveOrder.setPickerId(adminUserRespDTO.getId());
            waveOrder.setWarehouseId(warehouseId);
            waveOrderMapper.insert(waveOrder);
            result.add(waveOrder.getId());
            // 存入波次出库关系表
            List<Long> detailIds = orderDetailProductWarehouseMapper.selectDetailIdsByWareHouseId(warehouseId);
            List<OutboundOrderDetailDO> belongOutbound = orderDetailList.stream().filter(e -> detailIds.contains(e.getId())).collect(Collectors.toList());
            List<WaveOutboundOrderDO> waveOutbound = new ArrayList<>();
            Set<Long> outboundIdCollect = belongOutbound.stream().map(OutboundOrderDetailDO::getOutboundId).collect(Collectors.toSet());
            outboundIdCollect.forEach(outboundId -> {
                WaveOutboundOrderDO waveOutboundOrder = new WaveOutboundOrderDO();
                waveOutboundOrder.setOutboundOrderId(outboundId);
                waveOutboundOrder.setWellenId(waveOrder.getId());
                waveOutbound.add(waveOutboundOrder);
            });
            waveOutboundOrderMapper.insertBatch(waveOutbound);
            // 插入波次单子表
            List<WaveOrderDetailDO> waveOrderDetailDOS = new ArrayList<>();
            for (OutboundOrderDetailDO orderDetailDO : belongOutbound) {
                OutboundOrderDO outboundOrderDO = outboundOrderMapper.selectById(orderDetailDO.getOutboundId());
                List<OrderDetailProductWarehouseDO> orderDetailProductWarehouse = orderDetailProductWarehouseMapper.selectByOutboundDetailIds(Collections.singletonList(orderDetailDO.getId()));
                for (OrderDetailProductWarehouseDO productWarehouseDO : orderDetailProductWarehouse) {
                    WaveOrderDetailDO waveOrderDetailDO = BeanUtils.toBean(orderDetailDO, WaveOrderDetailDO.class);
                    waveOrderDetailDO.setId(null);
                    waveOrderDetailDO.setWaveId(waveOrder.getId());
                    waveOrderDetailDO.setOutboundCode(outboundOrderDO.getOutboundCode());
                    waveOrderDetailDO.setCustomerId(outboundOrderDO.getCustomerId());
                    waveOrderDetailDO.setCustomerName(outboundOrderDO.getCustomerName());
                    waveOrderDetailDO.setPendingPicking(productWarehouseDO.getProductOverallCount());
                    waveOrderDetailDO.setPendingSimplePicking(productWarehouseDO.getProductScatteredCount());
                    waveOrderDetailDO.setLocationId(productWarehouseDO.getWarehouseLocationId());
                    waveOrderDetailDO.setOutboundDetailProductWarehouseId(productWarehouseDO.getId());
                    waveOrderDetailDOS.add(waveOrderDetailDO);
                }
            }
            waveOrderDetailMapper.insertBatch(waveOrderDetailDOS);
        }

        // 返回
        return result;
    }

    private String createWaveOrderCode() {
        // 生成日期部分
        String datePart = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE); // yyyyMMdd

        // 生成Redis key（包含日期）
        String redisKey = "waveOrderCode:" + datePart;

        // 原子递增（从1开始）
        Long sequence = redisTemplate.opsForValue().increment(redisKey, 1);
        if (sequence == null) {
            // 递增失败时重试一次
            sequence = Optional.ofNullable(redisTemplate.opsForValue().increment(redisKey, 1))
                    .orElseThrow(() -> new RuntimeException("Redis increment failed"));
        }

        // 首次递增时设置过期时间
        if (sequence == 1) {
            setKeyWithDailyExpire(redisKey);
        }

        // 拼接完整订单号
        return "BCD-" + datePart + "-" + String.format("%03d", sequence);
    }

    private void setKeyWithDailyExpire(String key) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().plusDays(1).atStartOfDay();
        long secondsUntilMidnight = now.until(midnight, ChronoUnit.SECONDS);

        redisTemplate.expire(key, secondsUntilMidnight, TimeUnit.SECONDS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWaveOrder(WaveOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateWaveOrderExists(updateReqVO.getId());
        // 更新
        WaveOrderDO updateObj = BeanUtils.toBean(updateReqVO, WaveOrderDO.class);
        waveOrderMapper.updateById(updateObj);

        // 更新子表
        updateWaveOrderDetailList(updateReqVO.getId(), updateReqVO.getWaveOrderDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWaveOrder(Long id) {
        // 校验存在
        validateWaveOrderExists(id);
        // 删除
        waveOrderMapper.deleteById(id);

        // 删除子表
        deleteWaveOrderDetailByWaveId(id);
    }

    private void validateWaveOrderExists(Long id) {
        if (waveOrderMapper.selectById(id) == null) {
            throw exception(WAVE_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public WaveOrderDO getWaveOrder(Long id) {
        return waveOrderMapper.selectById(id);
    }

    @Override
    public PageResult<WaveOrderPageRespVO> getWaveOrderPage(WaveOrderPageReqVO pageReqVO) {
        Page<WaveOrderPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = waveOrderMapper.getWaveOrderPage(page, pageReqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    // ==================== 子表（波次单明细） ====================

    @Override
    public List<WaveOrderDetailEasyRespVO> getWaveOrderDetailListByWaveId(Long waveId) {
        return waveOrderDetailMapper.getWaveOrderDetailListByWaveId(waveId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWaveOrderStatus(WaveOrderStatusReqVO updateReqVO) {
        if (updateReqVO.getWaveStatus() == 1) {
            //波次单确认，生成总检单
            pickingOrderService.createPickingOrder(Collections.singletonList(updateReqVO.getWaveOrderId()), 0, null, null);
        }
        if (updateReqVO.getWaveStatus() == 2) {
            //作废波次单，修改出库单状态为草稿
            List<WaveOutboundOrderDO> waveOutboundOrderDOS = waveOutboundOrderMapper.selectOutboundByWaveId(updateReqVO.getWaveOrderId());
            List<Long> collect = waveOutboundOrderDOS.stream().map(WaveOutboundOrderDO::getOutboundOrderId).collect(Collectors.toList());

            for (Long outboundId : collect) {
                //回滚库存量
                outboundOrderDetailService.deleteOrAbrogateOutboundOrderDetailByOutboundId(outboundId, false);
            }

            outboundOrderMapper.updateStatusToDraftByIds(collect);
            waveOutboundOrderMapper.deleteByWaveId(updateReqVO.getWaveOrderId());
        }

        LambdaUpdateWrapper<WaveOrderDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Objects.nonNull(updateReqVO.getWaveStatus()), WaveOrderDO::getWaveStatus, updateReqVO.getWaveStatus());
        updateWrapper.eq(WaveOrderDO::getId, updateReqVO.getWaveOrderId());
        waveOrderMapper.update(updateWrapper);
    }

    private void createWaveOrderDetailList(Long waveId, List<WaveOrderDetailDO> list) {
        list.forEach(o -> o.setWaveId(waveId));
        waveOrderDetailMapper.insertBatch(list);
    }

    private void updateWaveOrderDetailList(Long waveId, List<WaveOrderDetailDO> list) {
        deleteWaveOrderDetailByWaveId(waveId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createWaveOrderDetailList(waveId, list);
    }

    private void deleteWaveOrderDetailByWaveId(Long waveId) {
        waveOrderDetailMapper.deleteByWaveId(waveId);
    }

}