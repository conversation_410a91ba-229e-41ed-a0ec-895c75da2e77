package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick.PickingOrderSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.PickingOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.PickingOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.PickingOrderMapper;
import com.cdmanyou.shtwms.module.mywms.service.outbound.PickingOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.PICKING_ORDER_NOT_EXISTS;

/**
 * 总拣单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PickingOrderServiceImpl implements PickingOrderService {

    @Resource
    private PickingOrderMapper pickingOrderMapper;
    @Resource
    private PickingOrderDetailMapper pickingOrderDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPickingOrder(PickingOrderSaveReqVO createReqVO) {
        // 插入
        PickingOrderDO pickingOrder = BeanUtils.toBean(createReqVO, PickingOrderDO.class);
        pickingOrderMapper.insert(pickingOrder);

        // 插入子表
        createPickingOrderDetailList(pickingOrder.getId(), createReqVO.getPickingOrderDetails());
        // 返回
        return pickingOrder.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePickingOrder(PickingOrderSaveReqVO updateReqVO) {
        // 校验存在
        validatePickingOrderExists(updateReqVO.getId());
        // 更新
        PickingOrderDO updateObj = BeanUtils.toBean(updateReqVO, PickingOrderDO.class);
        pickingOrderMapper.updateById(updateObj);

        // 更新子表
        updatePickingOrderDetailList(updateReqVO.getId(), updateReqVO.getPickingOrderDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePickingOrder(Long id) {
        // 校验存在
        validatePickingOrderExists(id);
        // 删除
        pickingOrderMapper.deleteById(id);

        // 删除子表
        deletePickingOrderDetailByPickingOrderId(id);
    }

    private void validatePickingOrderExists(Long id) {
        if (pickingOrderMapper.selectById(id) == null) {
            throw exception(PICKING_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public PickingOrderDO getPickingOrder(Long id) {
        return pickingOrderMapper.selectById(id);
    }

    @Override
    public PageResult<PickingOrderDO> getPickingOrderPage(PickingOrderPageReqVO pageReqVO) {
        return pickingOrderMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（总拣单明细） ====================

    @Override
    public List<PickingOrderDetailDO> getPickingOrderDetailListByPickingOrderId(Long pickingOrderId) {
        return pickingOrderDetailMapper.selectListByPickingOrderId(pickingOrderId);
    }

    private void createPickingOrderDetailList(Long pickingOrderId, List<PickingOrderDetailDO> list) {
        list.forEach(o -> o.setPickingOrderId(pickingOrderId));
        pickingOrderDetailMapper.insertBatch(list);
    }

    private void updatePickingOrderDetailList(Long pickingOrderId, List<PickingOrderDetailDO> list) {
        deletePickingOrderDetailByPickingOrderId(pickingOrderId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPickingOrderDetailList(pickingOrderId, list);
    }

    private void deletePickingOrderDetailByPickingOrderId(Long pickingOrderId) {
        pickingOrderDetailMapper.deleteByPickingOrderId(pickingOrderId);
    }

}