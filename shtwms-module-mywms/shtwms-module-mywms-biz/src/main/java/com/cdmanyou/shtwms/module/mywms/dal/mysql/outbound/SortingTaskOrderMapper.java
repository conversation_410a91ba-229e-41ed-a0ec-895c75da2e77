package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting.SortingTaskOrderPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.SortingTaskOrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 分拣任务单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SortingTaskOrderMapper extends BaseMapperX<SortingTaskOrderDO> {

    default PageResult<SortingTaskOrderDO> selectPage(SortingTaskOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SortingTaskOrderDO>()
                .eqIfPresent(SortingTaskOrderDO::getSortingOrderCode, reqVO.getSortingOrderCode())
                .likeIfPresent(SortingTaskOrderDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(SortingTaskOrderDO::getWarehouseId, reqVO.getWarehouseId())
                .eqIfPresent(SortingTaskOrderDO::getSorter, reqVO.getSorter())
                .eqIfPresent(SortingTaskOrderDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SortingTaskOrderDO::getSortingTime, reqVO.getSortingTime())
                .betweenIfPresent(SortingTaskOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SortingTaskOrderDO::getId));
    }

}