package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.*;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ShelvingManagementDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 上架管理单明细")
@RestController
@RequestMapping("/mywms/shelving-management-detail")
@Validated
public class ShelvingManagementDetailController {

    @Resource
    private ShelvingManagementDetailService shelvingManagementDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建上架管理单明细")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management-detail:create')")
    public CommonResult<Long> createShelvingManagementDetail(@Valid @RequestBody ShelvingManagementDetailSaveReqVO createReqVO) {
        return success(shelvingManagementDetailService.createShelvingManagementDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新上架管理单明细")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management-detail:update')")
    public CommonResult<Boolean> updateShelvingManagementDetail(@Valid @RequestBody ShelvingManagementDetailSaveReqVO updateReqVO) {
        shelvingManagementDetailService.updateShelvingManagementDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除上架管理单明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management-detail:delete')")
    public CommonResult<Boolean> deleteShelvingManagementDetail(@RequestParam("id") Long id) {
        shelvingManagementDetailService.deleteShelvingManagementDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得上架管理单明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management-detail:query')")
    public CommonResult<ShelvingManagementDetailPageRespVO> getShelvingManagementDetail(@RequestParam("id") Long id) {
        ShelvingManagementDetailDO shelvingManagementDetail = shelvingManagementDetailService.getShelvingManagementDetail(id);
        return success(BeanUtils.toBean(shelvingManagementDetail, ShelvingManagementDetailPageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得上架管理单明细分页")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management-detail:query')")
    public CommonResult<PageResult<ShelvingManagementDetailPageRespVO>> getShelvingManagementDetailPage(@Valid ShelvingManagementDetailPageReqVO pageReqVO) {
        PageResult<ShelvingManagementDetailDO> pageResult = shelvingManagementDetailService.getShelvingManagementDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ShelvingManagementDetailPageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出上架管理单明细 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:shelving-management-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportShelvingManagementDetailExcel(@Valid ShelvingManagementDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ShelvingManagementDetailDO> list = shelvingManagementDetailService.getShelvingManagementDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "上架管理单明细.xls", "数据", ShelvingManagementDetailPageRespVO.class,
                        BeanUtils.toBean(list, ShelvingManagementDetailPageRespVO.class));
    }

}