package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 入库异常处理单明细新增/修改 Request VO")
@Data
public class InboundExceptionDetailSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26320")
    private Long id;

    @Schema(description = "异常类型（0入库异常，1上架异常）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "异常类型（0入库异常，1上架异常）不能为空")
    private Integer exceptionType;

    @Schema(description = "关联的明细表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5262")
    @NotNull(message = "关联的明细表ID不能为空")
    private Long relatedDetailId;

    @Schema(description = "业务主表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5058")
    @NotNull(message = "业务主表id不能为空")
    private Long businessId;

    @Schema(description = "处理方案")
    private String handlingScenarios;

    @Schema(description = "处理结果")
    private String handlingResult;

    @Schema(description = "处理状态（0未处理，1已处理）", example = "2")
    private Integer handlingStatus;

    @Schema(description = "处理人ID")
    private Long handlingUser;

    @Schema(description = "处理时间")
    private LocalDateTime handlingTime;

}