package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 出库订单明细 DO
 *
 * <AUTHOR>
 */
@TableName("outbound_order_detail")
@KeySequence("outbound_order_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundOrderDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 出库单ID（关联主表）
     */
    private Long outboundId;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品id
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格（非必填）
     */
    private String specification;
    /**
     * 单位（非必填）
     */
    private String unit;
    /**
     * 待出库数量
     */
    private Integer pendingQuantity;
    /**
     * 波次单锁定数量
     */
    private Integer waveLockQuantity;
    /**
     * 已出库数量
     */
    private Integer receivedQuantity;
    /**
     * 差异数量
     */
    private Integer differenceQuantity;
    /**
     * 批次号（非必填）
     */
    private String batchNumber;
    /**
     * 生产日期（非必填）
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate productionDate;
    /**
     * 出库价格
     */
    private BigDecimal outboundPrice;
    /**
     * 出库金额(含税额)
     */
    private BigDecimal outboundAmount;
    /**
     * 分拣完成时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime sortingCompleteTime;
    /**
     * 是否越库(0否，1是)
     */
    private Integer straddleWarehouse;
    /**
     * 出库体积
     */
    private Long volume;
    /**
     * 出库重量
     */
    private Long grossWeight;
    /**
     * 到期日期
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate expirationDate;


    /**
     * 零单位数量
     */

    private Integer simpleCount;
    /**
     * 产品批号
     */

    private String productBatchNumber;

    /**
     * 仓库id,用,分割
     */
    private String warehouseId;
}