package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.*;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "入库单导出实体")
@Data
public class InboundOrderExportVO {
    
    @Schema(description = "入库单号")
    private String orderCode;

    @Schema(description = "入库类型（0采购入库，1退货入库，2其他入库）", example = "2")
    private Integer inboundType;

    @Schema(description = "入库单状态（0草稿,1已提交,2作废）", example = "2")
    private Integer inboundOrderStatus;

    @Schema(description = "入库单总额（含税额）")
    private BigDecimal totalAmount;

    @Schema(description = "货主单号")
    private String producerOrderCode;

    @Schema(description = "货主名称")
    private String producerName;

    @Schema(description = "货主编号")
    private String producerCode;
    
    @Schema(description = "供应商名称")
    private String supplierName;
    
    @Schema(description = "供应商编号")
    private String supplierCode;
    
    @Schema(description = "客户名称")
    private String customerName;
    
    @Schema(description = "客户编号")
    private String customerCode;
    
    @Schema(description = "货品编码")
    private String productCode;

    @Schema(description = "货品名称")
    private String productName;

    @Schema(description = "货品规格")
    private String specification;

    @Schema(description = "货品单位")
    private String unit;

    @Schema(description = "货品批次号")
    private String productBatchNumber;

    @Schema(description = "货品单价")
    private BigDecimal productPrice;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "货品保质期（天）")
    private Integer shelfLife;

    @Schema(description = "到期日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate expirationDate;

    @Schema(description = "温区（0冷藏，1冷冻）")
    private Integer inboundWeightUnit;

    @Schema(description = "入库数量")
    private Integer pendingQuantity;

    @Schema(description = "拆零数量")
    private Integer pendingScatteredQuantity;

    @Schema(description = "验收数量")
    private Integer receivedQuantity;

    @Schema(description = "验收零散数量")
    private Integer receivedScatteredQuantity;

    @Schema(description = "上架数量")
    private Integer shelvedQuantity;

    @Schema(description = "上架零散数量")
    private Integer shelvedScatteredQuantity;

    @Schema(description = "入库重量")
    private String inboundWeight;

    @Schema(description = "入库体积")
    private String inboundVolume;

    @Schema(description = "提交人名称")
    private String submitOperatorName;

    @Schema(description = "提交时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime submitTime;

    @Schema(description = "到货登记时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime acceptanceCompleteTime;

    @Schema(description = "验收完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime receiptCompleteTime;

    @Schema(description = "上架完成时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime shelvingCompleteTime;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "备注")
    private String remark;
}
