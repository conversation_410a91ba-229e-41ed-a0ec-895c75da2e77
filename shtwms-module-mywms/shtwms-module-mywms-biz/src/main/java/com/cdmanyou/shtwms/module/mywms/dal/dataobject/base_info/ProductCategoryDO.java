package com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品类别管理 DO
 *
 * <AUTHOR>
 */
@TableName("product_category")
@KeySequence("product_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategoryDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 类别编码
     */
    private String code;
    /**
     * 类别名称
     */
    private String name;
    /**
     * 类别描述
     */
    private String description;
    /**
     * 父类别ID
     */
    private Long parentId;
    /**
     * 是否启用(1启用/0停用)
     */
    private Integer status;

}