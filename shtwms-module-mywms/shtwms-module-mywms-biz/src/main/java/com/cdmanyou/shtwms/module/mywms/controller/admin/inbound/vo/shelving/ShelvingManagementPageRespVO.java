package com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "管理后台 - 上架管理单主 Response VO")
@Data
public class ShelvingManagementPageRespVO {

    @Schema(description = "上架单ID")
    private Long id;

    @Schema(description = "上架单号")
    private String shelvingCode;

    @Schema(description = "上架单状态（0未上架，1已上架）")
    private Integer shelvingStatus;

    @Schema(description = "待上架总数（实收货总数）")
    private Integer receivedQuantityTotal;

    @Schema(description = "实际上架总数")
    private Integer shelvedQuantityTotal;

    @Schema(description = "上架数量差值")
    private Integer diffQuantity;

    @Schema(description = "待上架零散总数（实收货品零散总数）")
    private Integer receivedScatteredQuantityTotal;

    @Schema(description = "实际上架零散总数")
    private Integer shelvedScatteredQuantityTotal;

    @Schema(description = "上架零散数量差值")
    private Integer diffScatteredQuantity;

    @Schema(description = "待上架总重量（实收货品总重量）")
    private BigDecimal receivedWeightTotal;

    @Schema(description = "实际上架总重量")
    private BigDecimal shelvedWeightTotal;

    @Schema(description = "上架重量差值")
    private BigDecimal diffWeight;

    @Schema(description = "上架员ID")
    private Long shelvingOperator;

    @Schema(description = "上架员名称")
    private String shelvingOperatorName;

    @Schema(description = "上架时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate shelvingTime;

    @Schema(description = "备注（非必填）", example = "你说的对")
    private String remark;

}