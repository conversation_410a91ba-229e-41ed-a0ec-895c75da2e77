<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.InboundOrderMapper">

    <resultMap id="InboundOrderInfoMap" type="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.InboundOrderInfoVO">
        <id property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="inboundType" column="inbound_type"/>
        <result property="inboundOrderStatus" column="inbound_order_status"/>
        <result property="producerId" column="producer_id"/>
        <result property="producerName" column="producer_name"/>
        <result property="producerCode" column="producer_code"/>
        <result property="producerOrderCode" column="producer_order_code"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createType" column="create_type"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="acceptanceCompleteTime" column="acceptance_complete_time"/>
        <result property="receiptCompleteTime" column="receipt_complete_time"/>
        <result property="shelvingCompleteTime" column="shelving_complete_time"/>
        <result property="pendingQuantityTotal" column="pending_quantity_total"/>
        <result property="pendingScatteredQuantityTotal" column="pending_scattered_quantity_total"/>
        <collection property="inboundOrderDetails"
                    ofType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailInfoVO">
            <id property="id" column="iod_id"/>
            <result property="productId" column="product_id"/>
            <result property="productCode" column="product_code"/>
            <result property="productName" column="product_name"/>
            <result property="specification" column="specification"/>
            <result property="masterMeasure" column="master_measure"/>
            <result property="unit" column="unit"/>
            <result property="supplierId" column="supplier_id"/>
            <result property="supplierName" column="supplier_name"/>
            <result property="productBatchNumber" column="product_batch_number"/>
            <result property="productionDate" column="production_date"/>
            <result property="pendingQuantity" column="pending_quantity"/>
            <result property="looseQuantity" column="loose_quantity"/>
            <result property="inboundWeight" column="inbound_weight"/>
            <result property="inboundVolume" column="inbound_volume"/>
            <result property="temperatureZone" column="temperature_zone"/>
            <result property="inboundQuantity" column="inbound_quantity"/>
        </collection>
    </resultMap>
    <select id="getInboundOrderByCode" resultMap="InboundOrderInfoMap">
        SELECT
            io.id,
            io.order_code,
            io.inbound_type,
            io.inbound_order_status,
            io.producer_id,
            pr.name AS producer_name,
            pr.code AS producer_code,
            io.producer_order_code,
            su2.nickname AS creator_name,
            io.create_type,
            io.create_time,
            NULL AS status, -- 获取入库状态 暂时为空
            io.remark,
            io.acceptance_complete_time,
            io.receipt_complete_time,
            io.shelving_complete_time,
            (
                SELECT
                    SUM(od1.pending_quantity)
                FROM
                    inbound_order_detail od1
                WHERE
                    od1.deleted = 0
                AND
                    od1.inbound_order_id = io.id
            ) AS pending_quantity_total,
            (
                SELECT
                    SUM(od2.pending_scattered_quantity)
                FROM
                    inbound_order_detail od2
                WHERE
                    od2.deleted = 0
                AND
                    od2.inbound_order_id = io.id
            ) AS pending_scattered_quantity_total,
            iod.id AS iod_id,
            iod.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.master_measure,
            p.unit,
            iod.supplier_id,
            su.name AS supplier_name,
            iod.product_batch_number,
            iod.production_date,
            iod.pending_quantity,
            p.loose_quantity,
            iod.inbound_weight,
            iod.inbound_volume,
            p.temperature_zone,
            (
                SELECT
                    COUNT(DISTINCT pwl.warehouse_location_id)
                FROM
                    product_warehouse_location pwl
                WHERE
                    pwl.inbound_order_id = io.id
                AND
                    pwl.producer_id = io.producer_id
                AND
                    pwl.product_id = iod.product_id
                AND
                    (pwl.count > 0 OR pwl.scattered_count > 0)
                AND
                    pwl.deleted = 0
            ) AS inbound_quantity
        FROM
            inbound_order io
        LEFT JOIN
            producer pr ON pr.id = io.producer_id AND pr.deleted = 0
        LEFT JOIN
            inbound_order_detail iod ON iod.inbound_order_id = io.id AND iod.deleted = 0
        LEFT JOIN
            product p ON p.id = iod.product_id AND p.deleted = 0
        LEFT JOIN
            supplier su ON su.id = iod.supplier_id AND su.deleted = 0
        LEFT JOIN
            system_users su1 ON su1.id = io.submit_operator
        LEFT JOIN
            system_users su2 ON su2.id = io.creator
        WHERE
            io.deleted = 0
        AND
            io.order_code = #{code}
    </select>

    <select id="selectPage"
            resultType="com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order.InboundOrderPageRespVO">
        SELECT
            io.id,
            io.order_code,
            io.inbound_type,
            io.inbound_order_status,
            io.total_amount,
            io.producer_order_code,
            io.producer_id,
            pr.name AS producer_name,
            pr.code AS producer_code,
            iod.product_id,
            p.code AS product_code,
            p.name AS product_name,
            p.specification,
            p.unit,
            iod.supplier_id,
            su.name AS supplier_name,
            su.code AS supplier_code,
            iod.product_batch_number,
            iod.product_price,
            iod.production_date,
            CASE
                WHEN iod.production_date IS NOT NULL AND p.shelf_life IS NOT NULL
                THEN DATE_ADD(iod.production_date, INTERVAL p.shelf_life DAY)
                ELSE NULL
            END AS expiration_date,
            iod.pending_quantity,
            iod.loose_quantity,
            iod.inbound_weight,
            iod.inbound_volume,
            NULL AS inbound_weight_unit,
            (
                SELECT
                    COUNT(DISTINCT pwl.warehouse_location_id)
                FROM
                    product_warehouse_location pwl
                WHERE
                    pwl.inbound_order_id = io.id
                AND
                    pwl.producer_id = io.producer_id
                AND
                    pwl.product_id = iod.product_id
                AND
                    (pwl.count > 0 OR pwl.scattered_count > 0)
                AND
                    pwl.deleted = 0
            ) AS inbound_quantity,
            (
                SELECT
                    SUM(od1.pending_quantity)
                FROM
                    inbound_order_detail od1
                WHERE
                    od1.deleted = 0
                AND
                    od1.inbound_order_id = io.id
            ) AS pending_quantity_total,
            (
                SELECT
                    SUM(od2.pending_scattered_quantity)
                FROM
                    inbound_order_detail od2
                WHERE
                    od2.deleted = 0
                AND
                    od2.inbound_order_id = io.id
            ) AS pending_scattered_quantity_total,
            io.submit_operator,
            su1.nickname AS submit_operator_name,
            io.submit_time,
            io.acceptance_complete_time,
            io.receipt_complete_time,
            io.shelving_complete_time,
            io.creator,
            su2.nickname AS creator_name,
            io.create_time,
            io.remark
        FROM
            inbound_order io
        LEFT JOIN
            producer pr ON pr.id = io.producer_id AND pr.deleted = 0
        LEFT JOIN
            inbound_order_detail iod ON iod.inbound_order_id = io.id AND iod.deleted = 0
        LEFT JOIN
            product p ON p.id = iod.product_id AND p.deleted = 0
        LEFT JOIN
            supplier su ON su.id = iod.supplier_id AND su.deleted = 0
        LEFT JOIN
            system_users su1 ON su1.id = io.submit_operator
        LEFT JOIN
            system_users su2 ON su2.id = io.creator
        WHERE
            io.deleted = 0
        <if test="reqVO.orderCode != null and reqVO.orderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{reqVO.orderCode}, '%')
        </if>
        <if test="reqVO.producerOrderCode != null and reqVO.producerOrderCode != ''">
            AND io.producer_order_code LIKE CONCAT('%', #{reqVO.producerOrderCode}, '%')
        </if>
        <if test="reqVO.inboundOrderStatus != null">
            AND io.inbound_order_status = #{reqVO.inboundOrderStatus}
        </if>
        <if test="reqVO.producerName != null and reqVO.producerName != ''">
            AND pr.name LIKE CONCAT('%', #{reqVO.producerName}, '%')
        </if>
        <if test="reqVO.productId != null">
            AND iod.product_id = #{reqVO.productId}
        </if>
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        <if test="reqVO.searchStartTime != null and reqVO.searchStartTime != ''">
            AND io.create_time &gt;= #{reqVO.searchStartTime}
        </if>
        <if test="reqVO.searchEndTime != null and reqVO.searchEndTime != ''">
            AND io.create_time &lt;= #{reqVO.searchEndTime}
        </if>
        ORDER BY
            io.create_time DESC
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT
            COUNT(io.id)
        FROM
            inbound_order io
        LEFT JOIN
            producer pr ON pr.id = io.producer_id AND pr.deleted = 0
        LEFT JOIN
            inbound_order_detail iod ON iod.inbound_order_id = io.id AND iod.deleted = 0
        LEFT JOIN
            product p ON p.id = iod.product_id AND p.deleted = 0
        WHERE
            io.deleted = 0
        <if test="reqVO.orderCode != null and reqVO.orderCode != ''">
            AND io.order_code LIKE CONCAT('%', #{reqVO.orderCode}, '%')
        </if>
        <if test="reqVO.producerOrderCode != null and reqVO.producerOrderCode != ''">
            AND io.producer_order_code LIKE CONCAT('%', #{reqVO.producerOrderCode}, '%')
        </if>
        <if test="reqVO.inboundOrderStatus != null">
            AND io.inbound_order_status = #{reqVO.inboundOrderStatus}
        </if>
        <if test="reqVO.producerName != null and reqVO.producerName != ''">
            AND pr.name LIKE CONCAT('%', #{reqVO.producerName}, '%')
        </if>
        <if test="reqVO.productId != null">
            AND iod.product_id = #{reqVO.productId}
        </if>
        <if test="reqVO.productName != null and reqVO.productName != ''">
            AND p.name LIKE CONCAT('%', #{reqVO.productName}, '%')
        </if>
        <if test="reqVO.searchStartTime != null and reqVO.searchStartTime != ''">
            AND io.create_time &gt;= #{reqVO.searchStartTime}
        </if>
        <if test="reqVO.searchEndTime != null and reqVO.searchEndTime != ''">
            AND io.create_time &lt;= #{reqVO.searchEndTime}
        </if>
    </select>

    <select id="getMaxSequence" resultType="java.lang.Long">
        SELECT
            MAX(CAST(SUBSTRING(order_code, -6) AS UNSIGNED))
        FROM
            inbound_order
        WHERE
            order_code LIKE CONCAT('IB', #{dateStr})
    </select>


</mapper>