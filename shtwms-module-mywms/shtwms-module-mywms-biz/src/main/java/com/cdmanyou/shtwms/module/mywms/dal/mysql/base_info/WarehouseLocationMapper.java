package com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_location.WarehouseLocationPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WarehouseLocationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 货位管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WarehouseLocationMapper extends BaseMapperX<WarehouseLocationDO> {

    default PageResult<WarehouseLocationDO> selectPage(WarehouseLocationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WarehouseLocationDO>()
                .eqIfPresent(WarehouseLocationDO::getCode, reqVO.getCode())
                .likeIfPresent(WarehouseLocationDO::getName, reqVO.getName())
                .eqIfPresent(WarehouseLocationDO::getWarehouseId, reqVO.getWarehouseId())
                .eqIfPresent(WarehouseLocationDO::getAreaId, reqVO.getAreaId())
                .eqIfPresent(WarehouseLocationDO::getShelfId, reqVO.getShelfId())
                .eqIfPresent(WarehouseLocationDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WarehouseLocationDO::getLength, reqVO.getLength())
                .eqIfPresent(WarehouseLocationDO::getWidth, reqVO.getWidth())
                .eqIfPresent(WarehouseLocationDO::getHeight, reqVO.getHeight())
                .eqIfPresent(WarehouseLocationDO::getWeight, reqVO.getWeight())
                .eqIfPresent(WarehouseLocationDO::getVolume, reqVO.getVolume())
                .eqIfPresent(WarehouseLocationDO::getStatus, reqVO.getStatus())
                .eqIfPresent(WarehouseLocationDO::getUnloadingMethod, reqVO.getUnloadingMethod())
                .betweenIfPresent(WarehouseLocationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WarehouseLocationDO::getId));
    }

}