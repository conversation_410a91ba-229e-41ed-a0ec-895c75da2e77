package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.sorting_detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "管理后台 - 分拣任务单明细新增/修改 Request VO")
public class SortingTaskOrderDetailStatusReqVO {

    @NotNull
    @Schema(description = "分拣单id")
    private Long sortingOrderId;

    @NotNull
    @Schema(description = "分拣单详情id")
    private Long sortingTaskOrderDetailId;

    @Schema(description = "拣货数量")
    private Integer pickedQuantity;

    @Schema(description = "拣货差异数量")
    private Integer differenceQuantity;

    @Schema(description = "零散拣货数量")
    private Integer pickedSimpleQuantity;

    @Schema(description = "零散拣货差异数量")
    private Integer differenceSimpleQuantity;

    @NotNull
    @Schema(description = "状态(0未确认,1已确认)", example = "1")
    private Integer sortingDetailStatus;
}