package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.math.BigInteger;

@Schema(description = "管理后台 - 商品管理新增/修改 Request VO")
@Data
public class ProductSaveReqVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4709")
    private Long id;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "客户编码")
    private String customerCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "商品名称不能为空")
    private String name;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "条形码")
    private String barcode;

    @Schema(description = "计量单位")
    private String unit;

    @Schema(description = "保质期(天)")
    private Integer shelfLife;

    @Schema(description = "温区（0冷藏，1冷冻）")
    private Integer temperatureZone;

    @Schema(description = "货主ID（关联客户表）", example = "7790")
    private Long producerId;

    @Schema(description = "商品类别ID（关联商品类别表）", example = "17097")
    private Long categoryId;

    @Schema(description = "主图")
    private String mainImage;

    @Schema(description = "附图")
    private String subImages;

    @Schema(description = "商品描述", example = "随便")
    private String description;

    @Schema(description = "使用说明")
    private String usageInstruction;

    @Schema(description = "包装说明")
    private String packingInstruction;

    @Schema(description = "注意事项")
    private String precautions;

    @Schema(description = "安全库存")
    private Integer safetyStock;

    @Schema(description = "最大库存")
    private Integer maxStock;

    @Schema(description = "库存预警阈值")
    private Integer stockWarningThreshold;

    @Schema(description = "码盘单层数量")
    private Integer palletLayerQty;

    @Schema(description = "码盘层高")
    private BigDecimal palletLayerHeight;

    @Schema(description = "主计量单位类别（0件，1公斤）")
    @NotNull(message = "计量单位不能为空")
    private Integer masterMeasure;

    @Schema(description = "计量方式（0固定重量，1称重计量）", example = "1")
    private Integer measureType;

    @Schema(description = "毛重（公斤）")
    private Double grossWeight;

    @Schema(description = "副计量类别（0袋，1其他）")
    private Integer deputyMeasure;

    @Schema(description = "长度（厘米）")  // 修复：字段名改为length，描述同步调整
    private Double length;

    @Schema(description = "宽（厘米）")
    private Double wide;

    @Schema(description = "高（厘米） ")
    private Double high;

    @Schema(description = "体积（立方米）")
    private Double volume;

    @Schema(description = "换算率（拆零数量）")
    private Integer looseQuantity;

    @Schema(description = "状态(1启用/0停用)", example = "1")
    private Integer status;

    @Schema(description = "供应商id")
    private BigInteger supplierId;

    @Schema(description = "冻结状态(0冻结/1解冻)", example = "1")
    private Integer FreezeStatus;

    @Schema(description = "分类名称")
    private String categoryName;

}