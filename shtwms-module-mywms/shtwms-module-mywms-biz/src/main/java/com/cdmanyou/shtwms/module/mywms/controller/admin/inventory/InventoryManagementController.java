package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory;

import com.cdmanyou.shtwms.framework.apilog.core.annotation.ApiAccessLog;
import com.cdmanyou.shtwms.framework.common.pojo.CommonResult;
import com.cdmanyou.shtwms.framework.common.pojo.PageParam;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.excel.core.util.ExcelUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.inventory.InventoryManagementSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.InventoryManagementDO;
import com.cdmanyou.shtwms.module.mywms.service.inventory.InventoryManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cdmanyou.shtwms.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.cdmanyou.shtwms.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 库存管理")
@RestController
@RequestMapping("/mywms/inventory-management")
@Validated
public class InventoryManagementController {

    @Resource
    private InventoryManagementService inventoryManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建库存管理")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-management:create')")
    public CommonResult<Long> createInventoryManagement(@Valid @RequestBody InventoryManagementSaveReqVO createReqVO) {
        return success(inventoryManagementService.createInventoryManagement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新库存管理")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-management:update')")
    public CommonResult<Boolean> updateInventoryManagement(@Valid @RequestBody InventoryManagementSaveReqVO updateReqVO) {
        inventoryManagementService.updateInventoryManagement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库存管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mywms:inventory-management:delete')")
    public CommonResult<Boolean> deleteInventoryManagement(@RequestParam("id") Long id) {
        inventoryManagementService.deleteInventoryManagement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库存管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-management:query')")
    public CommonResult<InventoryManagementRespVO> getInventoryManagement(@RequestParam("id") Long id) {
        InventoryManagementDO inventoryManagement = inventoryManagementService.getInventoryManagement(id);
        return success(BeanUtils.toBean(inventoryManagement, InventoryManagementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库存管理分页")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-management:query')")
    public CommonResult<PageResult<InventoryManagementRespVO>> getInventoryManagementPage(@Valid InventoryManagementPageReqVO pageReqVO) {
        PageResult<InventoryManagementDO> pageResult = inventoryManagementService.getInventoryManagementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InventoryManagementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库存管理 Excel")
    @PreAuthorize("@ss.hasPermission('mywms:inventory-management:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInventoryManagementExcel(@Valid InventoryManagementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InventoryManagementDO> list = inventoryManagementService.getInventoryManagementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "库存管理.xls", "数据", InventoryManagementRespVO.class,
                        BeanUtils.toBean(list, InventoryManagementRespVO.class));
    }

}