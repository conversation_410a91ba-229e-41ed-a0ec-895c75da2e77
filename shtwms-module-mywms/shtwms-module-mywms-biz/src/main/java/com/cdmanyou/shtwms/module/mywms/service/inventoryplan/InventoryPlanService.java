package com.cdmanyou.shtwms.module.mywms.service.inventoryplan;

import java.time.LocalDate;
import java.util.*;
import javax.validation.*;

import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanDetailRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventoryPlan.vo.InventoryPlanSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDO;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan.InventoryPlanDetailDO;

/**
 * 盘点计划主 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryPlanService {

    /**
     * 创建盘点计划主
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryPlan(@Valid InventoryPlanSaveReqVO createReqVO);

    /**
     * 更新盘点计划主
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryPlan(@Valid InventoryPlanSaveReqVO updateReqVO);

    /**
     * 删除盘点计划主
     *
     * @param id 编号
     */
    void deleteInventoryPlan(Long id);

    /**
     * 获得盘点计划主
     *
     * @param id 编号
     * @return 盘点计划主
     */
    InventoryPlanDO getInventoryPlan(Long id);

    /**
     * 获得盘点计划主分页
     *
     * @param pageReqVO 分页查询
     * @return 盘点计划主分页
     */
    PageResult<InventoryPlanDO> getInventoryPlanPage(InventoryPlanPageReqVO pageReqVO);

    // ==================== 子表（盘点计划明细） ====================

    /**
     * 获得盘点计划明细列表
     *
     * @param inventoryPlanId 盘点计划ID（关联主表）
     * @return 盘点计划明细列表
     */
    List<InventoryPlanDetailDO> getPlanDetailListByInventoryPlanId(Long inventoryPlanId);

    /**
     * 创建盘点
     * @param updateReqVO
     */
    void createInventory(InventoryPlanSaveReqVO updateReqVO) throws Exception;

    /**
     * 获取盘点详情
     * @param updateReqVO
     * @return
     */
    PageResult<InventoryPlanDetailRespVO> getInventoryDetail(InventoryPlanDetailPageReqVO updateReqVO);

    /**
     * 批量修改
     * @param updateReqVO
     */
    void batchUpdateInventoryPlan(List<InventoryPlanSaveReqVO> updateReqVO);

    List<InventoryPlanDO> selectStatusByDate(LocalDate now,Integer inventoryType);
}