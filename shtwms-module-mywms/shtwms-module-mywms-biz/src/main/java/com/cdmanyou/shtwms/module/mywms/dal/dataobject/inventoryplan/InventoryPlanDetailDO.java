package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 盘点计划明细 DO
 *
 * <AUTHOR>
 */
@TableName("inventory_plan_detail")
@KeySequence("inventory_plan_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryPlanDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 盘点计划ID（关联主表）
     */
    private Long inventoryPlanId;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格（非必填）
     */
    private String specification;
    /**
     * 账面数量
     */
    private Integer bookQuantity;
    /**
     * 实盘数量（非必填）
     */
    private Integer actualQuantity;
    /**
     * 差异数量（非必填）
     */
    private Integer differenceQuantity;

    /**
     * 零散账面数量
     */
    private Integer scatteredBookQuantity;
    /**
     * 零散实盘数量（非必填）
     */
    private Integer scatteredActualQuantity;
    /**
     * 零散差异数量（非必填）
     */
    private Integer scatteredDifferenceQuantity;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate manufactureDate;
    /**
     * 有效期
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate effectiveDate;
    /**
     * 库位id
     */
    private Long locationId;
    /**
     * 货位号
     */
    private String locationCode;
    /**
     * 货物id
     */
    private Long productId;
    /**
     * 商品批次号
     */
    private String productBatchNumber;
    /**
     * 主计量单位类别（0件，1公斤）
     */
    private Integer masterMeasure;

    /**
     * 副计量类别（0袋，1其他）
     */
    private Integer deputyMeasure;
    /**
     * 新库位号
     */
    private String newLocationCode;




}