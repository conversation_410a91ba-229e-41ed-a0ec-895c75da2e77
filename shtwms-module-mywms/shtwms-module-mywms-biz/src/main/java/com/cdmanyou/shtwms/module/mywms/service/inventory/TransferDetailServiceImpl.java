package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail.TransferDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory.TransferDetailMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.TRANSFER_DETAIL_NOT_EXISTS;

/**
 * 货品转移明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransferDetailServiceImpl implements TransferDetailService {

    @Resource
    private TransferDetailMapper transferDetailMapper;

    @Override
    public Long createTransferDetail(TransferDetailSaveReqVO createReqVO) {
        // 插入
        TransferDetailDO transferDetail = BeanUtils.toBean(createReqVO, TransferDetailDO.class);
        transferDetailMapper.insert(transferDetail);
        // 返回
        return transferDetail.getId();
    }

    @Override
    public void updateTransferDetail(TransferDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateTransferDetailExists(updateReqVO.getId());
        // 更新
        TransferDetailDO updateObj = BeanUtils.toBean(updateReqVO, TransferDetailDO.class);
        transferDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransferDetail(Long id) {
        // 校验存在
        validateTransferDetailExists(id);
        // 删除
        transferDetailMapper.deleteById(id);
    }

    private void validateTransferDetailExists(Long id) {
        if (transferDetailMapper.selectById(id) == null) {
            throw exception(TRANSFER_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public TransferDetailDO getTransferDetail(Long id) {
        return transferDetailMapper.selectById(id);
    }

    @Override
    public PageResult<TransferDetailDO> getTransferDetailPage(TransferDetailPageReqVO pageReqVO) {
        return transferDetailMapper.selectPage(pageReqVO);
    }

}