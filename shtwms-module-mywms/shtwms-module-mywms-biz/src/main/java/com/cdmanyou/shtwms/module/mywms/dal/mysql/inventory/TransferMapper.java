package com.cdmanyou.shtwms.module.mywms.dal.mysql.inventory;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.QueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 货品转移主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransferMapper extends BaseMapperX<TransferDO> {

    Page<TransferPageRespVO> selectPage(Page<TransferPageRespVO> page,
                                        @Param("reqVO") TransferPageReqVO reqVO);

    Long selectPageCount(@Param("reqVO") TransferPageReqVO pageReqVO);

    Long getMaxSequence(@Param("dateStr") String dateStr);

    default TransferDO selectIdByCode(String code) {
        return selectOne(new QueryWrapperX<TransferDO>().eq("transfer_code", code).eq("deleted", false));
    }

    default void voidByCode(String code) {
        update(new UpdateWrapper<TransferDO>().eq("transfer_code", code).set("status", 2));
    }

    TransferRespVO getTransferByCode(@Param("code") String code);


}