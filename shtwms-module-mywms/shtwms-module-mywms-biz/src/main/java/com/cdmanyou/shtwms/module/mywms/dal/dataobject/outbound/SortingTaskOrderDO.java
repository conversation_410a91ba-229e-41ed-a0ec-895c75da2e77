package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 分拣任务单主 DO
 *
 * <AUTHOR>
 */
@TableName("sorting_task_order")
@KeySequence("sorting_task_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SortingTaskOrderDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 分拣单号
     */
    private String sortingOrderCode;
    /**
     * 出库订单号（关联出库订单主表）
     */
    private String outboundOrderCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 商品总数量
     */
    private Integer totalProductQuantity;
    /**
     * 仓库ID（关联仓库表）
     */
    private Long warehouseId;
    /**
     * 分拣员
     */
    private String sorter;
    /**
     * 分拣容器（非必填）
     */
    private String containerType;
    /**
     * 分拣容器号（非必填）
     */
    private String containerCode;
    /**
     * 状态
     */
    private String status;
    /**
     * 分拣时间
     */
    private LocalDateTime sortingTime;
    /**
     * 备注
     */
    private String remark;

}