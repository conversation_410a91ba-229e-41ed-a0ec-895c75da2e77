package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.collection.CollectionUtils;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.outbound_detail.OutboundOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OutboundOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.OutboundOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.outbound.OutboundOrderDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.OUTBOUND_ORDER_DETAIL_NOT_EXISTS;

/**
 * 出库订单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutboundOrderDetailServiceImpl implements OutboundOrderDetailService {

    @Resource
    private OutboundOrderDetailMapper outboundOrderDetailMapper;

    @Override
    public Long createOutboundOrderDetail(OutboundOrderDetailSaveReqVO createReqVO) {
        // 插入
        OutboundOrderDetailDO outboundOrderDetail = BeanUtils.toBean(createReqVO, OutboundOrderDetailDO.class);
        outboundOrderDetailMapper.insert(outboundOrderDetail);
        // 返回
        return outboundOrderDetail.getId();
    }

    @Override
    public void updateOutboundOrderDetail(OutboundOrderDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateOutboundOrderDetailExists(updateReqVO.getId());
        // 更新
        OutboundOrderDetailDO updateObj = BeanUtils.toBean(updateReqVO, OutboundOrderDetailDO.class);
        outboundOrderDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteOutboundOrderDetail(Long id) {
        // 校验存在
        validateOutboundOrderDetailExists(id);
        // 删除
        outboundOrderDetailMapper.deleteById(id);
    }

    private void validateOutboundOrderDetailExists(Long id) {
        if (outboundOrderDetailMapper.selectById(id) == null) {
            throw exception(OUTBOUND_ORDER_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public OutboundOrderDetailDO getOutboundOrderDetail(Long id) {
        return outboundOrderDetailMapper.selectById(id);
    }

    @Override
    public PageResult<OutboundOrderDetailDO> getOutboundOrderDetailPage(OutboundOrderDetailPageReqVO pageReqVO) {
        return outboundOrderDetailMapper.selectPage(pageReqVO, null);
    }

    @Override
    public List<OutboundOrderDetailDO> getDetailListByOutboundIds(Collection<Long> outboundIds) {
        if (CollectionUtils.isAnyEmpty(outboundIds)) {
            return Collections.emptyList();
        }
        return outboundOrderDetailMapper.selectListByOutboundIds(outboundIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWaveLockQuantity(Long id, int lockNumber) {
        LambdaUpdateWrapper<OutboundOrderDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OutboundOrderDetailDO::getWaveLockQuantity, lockNumber);
        updateWrapper.eq(OutboundOrderDetailDO::getId, id);
        outboundOrderDetailMapper.update(null, updateWrapper);
    }

}