package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 上架管理单明细 DO
 *
 * <AUTHOR>
 */
@TableName("shelving_management_detail")
@KeySequence("shelving_management_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShelvingManagementDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 父级ID（分拆ID）
     */
    private Long parentId;
    /**
     * 上架单ID
     */
    private Long shelvingId;
    /**
     * 货主ID
     */
    private Long producerId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品批次号
     */
    private String productBatchNumber;
    /**
     * 系统批次号
     */
    private String systemBatchNumber;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 待入库数量
     */
    private Integer pendingQuantity;
    /**
     * 待入库零散数量
     */
    private Integer pendingScatteredQuantity;
    /**
     * 收货数量（待上架数量）
     */
    private Integer receivedQuantity;
    /**
     * 收货零散数量（待上架零散数量）
     */
    private Integer receivedScatteredQuantity;
    /**
     * 上架数量
     */
    private Integer shelvedQuantity;
    /**
     * 上架零散数量
     */
    private Integer shelvedScatteredQuantity;
    /**
     * 实收重量
     */
    private BigDecimal receivedWeight;
    /**
     * 上架重量
     */
    private BigDecimal shelvedWeight;
    /**
     * 托盘ID（关联托盘表，提交并上架时必填）
     */
    private Long trayId;
    /**
     * 货位ID（关联货位表，提交并上架时必填）
     */
    private Long warehouseLocationId;

}