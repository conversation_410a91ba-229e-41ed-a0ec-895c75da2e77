package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 货品转移主 Service 接口
 *
 * <AUTHOR>
 */
public interface TransferService {

    /**
     * 创建货品转移主
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransfer(@Valid TransferSaveReqVO createReqVO);

    /**
     * 更新货品转移主
     *
     * @param updateReqVO 更新信息
     */
    void updateTransfer(@Valid TransferSaveReqVO updateReqVO);

    /**
     * 删除货品转移主
     *
     * @param id 编号
     */
    void deleteTransfer(Long id);

    /**
     * 获得货品转移主
     *
     * @param id 编号
     * @return 货品转移主
     */
    TransferDO getTransfer(Long id);

    /**
     * 获得货品转移主分页
     *
     * @param pageReqVO 分页查询
     * @return 货品转移主分页
     */
    PageResult<TransferDO> getTransferPage(TransferPageReqVO pageReqVO);

    // ==================== 子表（货品转移明细） ====================

    /**
     * 获得货品转移明细列表
     *
     * @param transferId 转移单ID（关联主表）
     * @return 货品转移明细列表
     */
    List<TransferDetailDO> getTransferDetailListByTransferId(Long transferId);

}