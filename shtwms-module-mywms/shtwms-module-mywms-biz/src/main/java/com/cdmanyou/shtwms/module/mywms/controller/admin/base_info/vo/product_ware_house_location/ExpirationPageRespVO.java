package com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.product_ware_house_location;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "效期预警列表实体")
@Data
public class ExpirationPageRespVO {

    @Schema(description = "货品ID")
    private Long productId;

    @Schema(description = "货品编号")
    private String productCode;

    @Schema(description = "货品名称")
    private String productName;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "货品批次号")
    private String productBatchNumber;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate productionDate;

    @Schema(description = "保质期（天）")
    private Integer shelfLife;

    @Schema(description = "到期日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    private LocalDate effectiveDate;

    @Schema(description = "剩余天数")
    private Integer remainingDays;

    @Schema(description = "库存数量")
    private Integer inventoryCount;

    @Schema(description = "效期状态（0安全效期，1临期货品，2过期货品）")
    private Integer expirationStatus;

    @Schema(description = "货位编号")
    private String warehouseLocationCodes;
}
