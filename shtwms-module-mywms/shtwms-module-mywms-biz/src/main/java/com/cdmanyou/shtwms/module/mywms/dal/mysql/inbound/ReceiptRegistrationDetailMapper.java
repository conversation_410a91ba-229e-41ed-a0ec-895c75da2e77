package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import java.util.*;

import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ReceiptRegistrationDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 收货登记单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReceiptRegistrationDetailMapper extends BaseMapperX<ReceiptRegistrationDetailDO> {

    default List<ReceiptRegistrationDetailDO> selectListByReceiptId(Long receiptId) {
        return selectList(ReceiptRegistrationDetailDO::getReceiptId, receiptId);
    }

    default void deleteByReceiptId(Long receiptId) {
        delete(ReceiptRegistrationDetailDO::getReceiptId, receiptId);
    }

    default List<ReceiptRegistrationDetailDO> selectListByReceiptIds(List<Long> receiptIds) {
        return selectList(new LambdaQueryWrapperX<ReceiptRegistrationDetailDO>()
                .inIfPresent(ReceiptRegistrationDetailDO::getReceiptId, receiptIds)
                .eqIfPresent(ReceiptRegistrationDetailDO::getDeleted, false));
    }

    default List<ReceiptRegistrationDetailDO> selectListByShelvingDetailId(Long shelvingDetailId) {
        return selectList(new LambdaQueryWrapperX<ReceiptRegistrationDetailDO>()
                .eqIfPresent(ReceiptRegistrationDetailDO::getShelvingDetailId, shelvingDetailId)
                .eqIfPresent(ReceiptRegistrationDetailDO::getDeleted, false));
    }
}