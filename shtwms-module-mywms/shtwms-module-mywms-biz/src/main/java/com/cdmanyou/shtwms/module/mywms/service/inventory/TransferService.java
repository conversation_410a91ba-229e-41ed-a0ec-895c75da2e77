package com.cdmanyou.shtwms.module.mywms.service.inventory;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferInputVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer.TransferRespVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventory.TransferDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 货品转移 Service 接口
 *
 * <AUTHOR>
 */
public interface TransferService {

    /**
     * 创建货品转移
     *
     * @param inputVO 创建信息
     * @return 编号
     */
    Long createTransfer(@Valid TransferInputVO inputVO);

    /**
     * 更新货品转移
     *
     * @param inputVO 更新信息
     */
    void updateTransfer(@Valid TransferInputVO inputVO);

    /**
     * 作废转移单
     *
     * @param code 转移单单号
     */
    void voidByCode(String code);

    /**
     * 获得货品转移
     *
     * @param code 转移单号
     * @return 转移单详情
     */
    TransferRespVO getTransferByCode(String code);

    /**
     * 获得货品转移分页
     *
     * @param pageReqVO 分页查询
     * @return 货品转移分页
     */
    PageResult<TransferPageRespVO> getTransferPage(TransferPageReqVO pageReqVO);

    // ==================== 子表（货品转移明细） ====================

    /**
     * 获得货品转移明细列表
     *
     * @param transferId 转移单ID（关联主表）
     * @return 货品转移明细列表
     */
    List<TransferDetailDO> getTransferDetailListByTransferId(Long transferId);

    /**
     * 通过转移单单号完成转移单
     *
     * @param code 转移单单号
     */
    void completeByCode(String code);
}