package com.cdmanyou.shtwms.module.mywms.service.inbound;


import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_order_detail.InboundOrderDetailSubmitVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailPageRespVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.receipt_registration_detail.ReceiptRegistrationDetailSaveVO;

import java.util.List;

/**
 * 收货登记单明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ReceiptRegistrationDetailService {

    /**
     * 创建收货单明细 - 入库单提交
     *
     * @param receiptId 收货单ID
     * @param list 入库单提交列表
     */
    void createDetailsByInboundOrderSubmit(Long receiptId, List<InboundOrderDetailSubmitVO> list);

    /**
     * 更新收货单明细 - 收货单保存/提交
     *
     * @param receiptId 收货单ID
     * @param list 收货单明细列表
     */
    void updateDetailsByReceiptRegistrationSubmit(Long receiptId, List<ReceiptRegistrationDetailSaveVO> list);

    /**
     * 获取收货登记明细分页
     *
     * @param pageReqVO 分页参数
     * @return 分页列表
     */
    PageResult<ReceiptRegistrationDetailPageRespVO> getReceiptRegistrationPage(ReceiptRegistrationDetailPageReqVO pageReqVO);
}