package com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound;

import java.util.*;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.inbound_exception.InboundExceptionPageReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.InboundExceptionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 入库异常处理单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InboundExceptionMapper extends BaseMapperX<InboundExceptionDO> {

    default PageResult<InboundExceptionDO> selectPage(InboundExceptionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InboundExceptionDO>()
                .eqIfPresent(InboundExceptionDO::getRelatedOrderCode, reqVO.getRelatedOrderCode())
                .eqIfPresent(InboundExceptionDO::getExceptionType, reqVO.getExceptionType())
                .eqIfPresent(InboundExceptionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(InboundExceptionDO::getHandler, reqVO.getHandler())
                .betweenIfPresent(InboundExceptionDO::getHandleTime, reqVO.getHandleTime())
                .betweenIfPresent(InboundExceptionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InboundExceptionDO::getId));
    }

}