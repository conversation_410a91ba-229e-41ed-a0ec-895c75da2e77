package com.cdmanyou.shtwms.module.mywms.dal.dataobject.inventoryplan;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;

/**
 * 盘点计划主 DO
 *
 * <AUTHOR>
 */
@TableName("inventory_plan")
@KeySequence("inventory_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryPlanDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 盘点计划单号
     */
    private String inventoryPlanCode;
    /**
     * 计划名称
     */
    private String planName;
    /**
     * 盘点类型
     */
    private String inventoryType;
    /**
     * 仓库ID（关联仓库表）
     */
    private Long warehouseId;
    /**
     * 盘点日期
     */
    private LocalDateTime inventoryDate;
    /**
     * 状态
     */
    private String status;

}