package com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdmanyou.shtwms.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 总拣单明细 DO
 *
 * <AUTHOR>
 */
@TableName("total_picking_order_detail")
@KeySequence("total_picking_order_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PickingOrderDetailDO extends BaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;
    /**
     * 拣货单ID（关联主表）
     */
    private Long pickingOrderId;
    /**
     * 商品编码（关联商品表）
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格（非必填）
     */
    private String specification;
    /**
     * 单位（非必填）
     */
    private String unit;
    /**
     * 待拣货数量
     */
    private Integer pendingQuantity;
    /**
     * 已拣货数量
     */
    private Integer pickedQuantity;
    /**
     * 差异数量
     */
    private Integer differenceQuantity;
    /**
     * 批次号（非必填）
     */
    private String batchNumber;
    /**
     * 生产日期（非必填）
     */
    private LocalDate productionDate;
    /**
     * 拣货库区ID（关联库区表，非必填）
     */
    private Long areaId;
    /**
     * 拣货库位ID（关联货位表，非必填）
     */
    private Long locationId;
    /**
     * 拣货库位ID（关联货位表，非必填）
     */
    private Integer pendingSimpleQuantity;
    /**
     * 总检单详情状态(0未确认,1已确认)
     */
    private Integer pickingDetailStatus;

    private Integer pickedSimpleQuantity;

    private Integer differenceSimpleQuantity;

    /**
     * 波次单详情id
     */
    private Long waveDetailId;

    /**
     * 出库单详情库存关联id（关联主表）
     */
    private Long outboundDetailProductWarehouseId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 仓类型(0成品仓1半成品仓,2原料仓)
     */
    private Integer houseType;

}