package com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.pick;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 总拣单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PickingOrderRespVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5718")
    @ExcelProperty("自增ID")
    private Long id;

    @Schema(description = "拣货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("拣货单号")
    private String pickingOrderCode;

    @Schema(description = "波次单编号（关联波次单主表）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("波次单编号（关联波次单主表）")
    private String waveCode;

    @Schema(description = "待拣货总数")
    @ExcelProperty("待拣货总数")
    private Integer totalPending;

    @Schema(description = "已拣货总数")
    @ExcelProperty("已拣货总数")
    private Integer totalPicked;

    @Schema(description = "仓库ID（关联仓库表）", example = "859")
    @ExcelProperty("仓库ID（关联仓库表）")
    private Long warehouseId;

    @Schema(description = "拣货员")
    @ExcelProperty("拣货员")
    private String picker;

    @Schema(description = "托盘号（非必填）")
    @ExcelProperty("托盘号（非必填）")
    private String trayCode;

    @Schema(description = "拣货容器（非必填）", example = "1")
    @ExcelProperty("拣货容器（非必填）")
    private String containerType;

    @Schema(description = "拣货容器号（非必填）")
    @ExcelProperty("拣货容器号（非必填）")
    private String containerCode;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}