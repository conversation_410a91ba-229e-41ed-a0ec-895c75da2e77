package com.cdmanyou.shtwms.module.mywms.controller.admin.inventory.vo.transfer_detail;

import com.cdmanyou.shtwms.framework.common.util.date.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.cdmanyou.shtwms.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "货品转移 明细创建实体")
@Data
public class TransferDetailInputVO {

    @Schema(description = "转移单明细ID（新增时为空）")
    private Long id;

    @Schema(description = "货品ID")
    @NotNull(message = "货品ID不能为空")
    private Long productId;

    @Schema(description = "货品编码")
    private String productCode;

    @Schema(description = "生产日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY, timezone = TIME_ZONE_DEFAULT)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate productionDate;

    @Schema(description = "商品批次号")
    private String productBatchNumber;

    @Schema(description = "转出货位的所属仓类别（0成品，1半成品，2原料）")
    private Integer fromWarehouseLocationHouseType;

    @Schema(description = "转出货位ID")
    private Long fromWarehouseLocationId;

    @Schema(description = "转入货位的所属仓类别(0成品，1半成品，2原料)")
    private Integer toWarehouseLocationHouseType;

    @Schema(description = "转入货位ID")
    private Long toWarehouseLocationId;

    @Schema(description = "转移数量")
    private Integer quantity;

    @Schema(description = "转移零散数量")
    private Integer scatteredQuantity;
}
