package com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound;


import com.cdmanyou.shtwms.framework.common.util.collection.CollectionUtils;
import com.cdmanyou.shtwms.framework.mybatis.core.mapper.BaseMapperX;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.OrderDetailProductWarehouseDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * 出库单详情和产品货位关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderDetailProductWarehouseMapper extends BaseMapperX<OrderDetailProductWarehouseDO> {


    default List<OrderDetailProductWarehouseDO> selectByOutboundDetailIds(List<Long> outboundDetailIds) {
        if (!CollectionUtils.isAnyEmpty(outboundDetailIds)) {
            LambdaQueryWrapperX<OrderDetailProductWarehouseDO> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.in(OrderDetailProductWarehouseDO::getOutboundDetailId, outboundDetailIds);
            return selectList(queryWrapperX);
        }
        return new ArrayList<>();
    }
}