package com.cdmanyou.shtwms.module.mywms.service.inbound.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.security.core.util.SecurityFrameworkUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving.*;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailCreateVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.inbound.vo.shelving_detail.ShelvingManagementDetailSaveVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ReceiptRegistrationDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.inbound.ShelvingManagementDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ReceiptRegistrationDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ShelvingManagementDetailMapper;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.inbound.ShelvingManagementMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.ProductWareHouseLocationService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ReceiptRegistrationService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ShelvingManagementDetailService;
import com.cdmanyou.shtwms.module.mywms.service.inbound.ShelvingManagementService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.*;
import static com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil.getDecimalFieldSum;
import static com.cdmanyou.shtwms.module.mywms.util.NumberFieldUtil.getIntFieldSum;

/**
 * 上架管理单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShelvingManagementServiceImpl implements ShelvingManagementService {

    @Resource
    private ShelvingManagementMapper shelvingManagementMapper;

    @Resource
    private ShelvingManagementDetailMapper shelvingManagementDetailMapper;

    @Resource
    private ShelvingManagementDetailService shelvingManagementDetailService;

    @Resource
    private ReceiptRegistrationService receiptRegistrationService;

    @Resource
    private ReceiptRegistrationDetailMapper receiptRegistrationDetailMapper;

    @Resource
    private ProductWareHouseLocationService productWareHouseLocationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createManagement(Long warehouseId, ShelvingManagementCreateVO createReqVO) {
        // 创建上架单
        ShelvingManagementDO management = BeanUtils.toBean(createReqVO, ShelvingManagementDO.class);
        // 生成上架单号
        management.setShelvingCode(generateShelvingCode());
        management.setWarehouseId(warehouseId);
        shelvingManagementMapper.insert(management);
        // 创建上架单明细
        createShelvingManagementDetailListByReceiptCreate(management.getId(), createReqVO.getShelvingManagementDetails());
        // 返回
        return management.getId();
    }

    @Lock4j(keys = {"'shelving_management_code'"}, autoRelease = true)
    private String generateShelvingCode() {
        // 生成收货单日期号
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 生成序列号
        Long maxSequence = shelvingManagementMapper.getMaxSequence(dateStr);
        Long newMaxSequence = (maxSequence == null) ? 1L : maxSequence + 1;
        // 格式化序列号
        String sequence = String.format("%06d", newMaxSequence);
        return "SJ" + dateStr + sequence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateManagement(ShelvingManagementSaveVO updateReqVO) {
        // 校验存在
        validateManagementExists(updateReqVO.getId());
        // 更新
        ShelvingManagementDO updateObj = BeanUtils.toBean(updateReqVO, ShelvingManagementDO.class);
        shelvingManagementMapper.updateById(updateObj);

        // 更新子表
//        updateShelvingManagementDetailList(updateReqVO.getId(), updateReqVO.getShelvingManagementDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteManagement(Long id) {
        // 校验存在
        validateManagementExists(id);
        // 删除
        shelvingManagementMapper.deleteById(id);

        // 删除子表
        deleteShelvingManagementDetailByShelvingId(id);
    }

    private ShelvingManagementDO validateManagementExists(Long id) {
        ShelvingManagementDO shelvingManagementDO = shelvingManagementMapper.selectById(id);
        if (shelvingManagementDO == null) {
            throw exception(SHELVING_MANAGEMENT_NOT_EXISTS);
        }
        return shelvingManagementDO;
    }

    @Override
    public ShelvingManagementInfoVO getShelvingManagementByCode(String code) {
        return shelvingManagementMapper.getShelvingManagementByCode(code);
    }

    @Override
    public PageResult<ShelvingManagementPageRespVO> getManagementPage(ShelvingManagementPageReqVO pageReqVO) {
        Page<ShelvingManagementPageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        page = shelvingManagementMapper.selectPage(page, pageReqVO);
        Long total = shelvingManagementMapper.selectPageCount(pageReqVO);
        return new PageResult<>(page.getRecords(), total);
    }

    // ==================== 子表（上架管理单明细） ====================

    @Override
    public List<ShelvingManagementDetailDO> getShelvingManagementDetailListByShelvingId(Long shelvingId) {
        return shelvingManagementDetailMapper.selectListByShelvingId(shelvingId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveShelvingManagement(ShelvingManagementSaveVO saveReqVO) {
        // 校验
        ShelvingManagementDO shelvingManagementDO = validateManagementExists(saveReqVO.getId());
        // 校验提交状态
        if (shelvingManagementDO.getShelvingStatus() != null && shelvingManagementDO.getShelvingStatus() == 1) {
            throw exception(SHELVING_MANAGEMENT_ALREADY_SUBMIT);
        }
        List<ShelvingManagementDetailSaveVO> shelvingManagementDetails = saveReqVO.getShelvingManagementDetails();
        if (CollUtil.isEmpty(shelvingManagementDetails)) {
            throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_DETAILS_EMPTY);
        }
        // 校验是否重复上架
        validateShelvingDuplicate(shelvingManagementDetails);

        ShelvingManagementDO updateObj = new ShelvingManagementDO();
        updateObj.setId(saveReqVO.getId());
        updateObj.setShelvingTime(saveReqVO.getShelvingTime());

        if (saveReqVO.getSaveFlag() == 1) {
            // 校验必填字段
            validateShelvingManagementDetailsForSubmit(shelvingManagementDetails);
            // 更新
            updateObj.setShelvingStatus(1);
            updateObj.setShelvingOperator(SecurityFrameworkUtils.getLoginUserId());
            updateObj.setSubmitOperator(SecurityFrameworkUtils.getLoginUserId());
            updateObj.setSubmitTime(LocalDateTime.now());
            // 获取上架单上架数量、上架零散数量、上架总重量
            updateObj.setShelvedQuantityTotal(getIntFieldSum(shelvingManagementDetails, ShelvingManagementDetailSaveVO::getShelvedQuantity));
            updateObj.setShelvedScatteredQuantityTotal(getIntFieldSum(shelvingManagementDetails, ShelvingManagementDetailSaveVO::getShelvedScatteredQuantity));
            updateObj.setShelvedWeightTotal(getDecimalFieldSum(shelvingManagementDetails, ShelvingManagementDetailSaveVO::getShelvedWeight));
            // 更新收货单、入库单上架数量
            updateBackReceiptRegistrationDetails(shelvingManagementDetails);
            // 更新库存
            productWareHouseLocationService.createOrUpdateByShelving(shelvingManagementDetails);
        }

        // 更新上架单
        shelvingManagementMapper.updateById(updateObj);
        // 更新上架单明细
        shelvingManagementDetailService.updateDetailsByShelvingManagementSubmit(saveReqVO.getId(), shelvingManagementDetails);
    }

    private void validateShelvingDuplicate(List<ShelvingManagementDetailSaveVO> shelvingManagementDetails) {
        shelvingManagementDetails.forEach(shelvingManagement -> {
            String productCode = shelvingManagement.getProductCode();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String dateStr = shelvingManagement.getProductionDate().format(formatter);
            String producerStr = shelvingManagement.getProducerId().toString();
            if (shelvingManagement.getWarehouseLocationId() != null) {
                String locationStr = shelvingManagement.getWarehouseLocationId().toString();
                shelvingManagement.setDuplicateCode(productCode + dateStr + "_" + producerStr + "_" + locationStr);
            }else {
                shelvingManagement.setDuplicateCode(productCode + dateStr + "_" + producerStr + "_" + "NULL");
            }
        });
        HashSet<String> duplicateCodeSet = new HashSet<>();
        for (ShelvingManagementDetailSaveVO detail : shelvingManagementDetails) {
            if (!duplicateCodeSet.add(detail.getDuplicateCode())) {
                throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_DUPLICATE_CODE);
            }
        }
    }

    private void validateShelvingManagementDetailsForSubmit(List<ShelvingManagementDetailSaveVO> shelvingManagementDetails) {
        for (int i = 0; i < shelvingManagementDetails.size(); i++) {
            ShelvingManagementDetailSaveVO detailSaveVO = shelvingManagementDetails.get(i);
            String detailInfo = String.format("第%d条", i + 1);
            if (detailSaveVO.getWarehouseLocationId() == null) {
                throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_LOCATION_ID_EMPTY, detailInfo);
            }
            if (detailSaveVO.getShelvedQuantity() == null) {
                throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_SHELVING_QUANTITY_EMPTY, detailInfo);
            }
            if (detailSaveVO.getShelvedScatteredQuantity() == null) {
                throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_SHELVING_SCATTERED_QUANTITY_EMPTY, detailInfo);
            }
            if (detailSaveVO.getShelvedWeight() == null) {
                throw exception(SHELVING_MANAGEMENT_SAVE_ERROR_BY_SHELVING_WEIGHT_EMPTY, detailInfo);
            }
        }
    }

    public void updateBackReceiptRegistrationDetails(List<ShelvingManagementDetailSaveVO> shelvingManagementDetails) {
        // 按照parentId进行分组
        Map<Long, List<ShelvingManagementDetailSaveVO>> groupedDetails = shelvingManagementDetails.stream()
                .filter(detail -> detail.getParentId() != null)
                .collect(Collectors.groupingBy(ShelvingManagementDetailSaveVO::getParentId));
        // 修改收货单和入库单明细
        for (Map.Entry<Long, List<ShelvingManagementDetailSaveVO>> entry : groupedDetails.entrySet()) {
            Long parentId = entry.getKey();
            List<ShelvingManagementDetailSaveVO> groupDetails = entry.getValue();
            // 校验实收总数
            List<ReceiptRegistrationDetailDO> receiptDetails = receiptRegistrationDetailMapper.selectListByShelvingDetailId(parentId);
            ShelvingManagementDetailSaveVO detailSaveVO = groupDetails.get(0);
            int oldReceivedQuantityTotal = getIntFieldSum(receiptDetails, ReceiptRegistrationDetailDO::getReceivedQuantity);
            int newReceivedQuantityTotal = getIntFieldSum(groupDetails, ShelvingManagementDetailSaveVO::getReceivedQuantity);
            int oldReceivedScatteredQuantityTotal = getIntFieldSum(receiptDetails, ReceiptRegistrationDetailDO::getReceivedScatteredQuantity);
            int newReceivedScatteredQuantityTotal = getIntFieldSum(groupDetails, ShelvingManagementDetailSaveVO::getReceivedScatteredQuantity);
            BigDecimal oldReceivedWeightTotal = getDecimalFieldSum(receiptDetails, ReceiptRegistrationDetailDO::getReceivedWeight);
            BigDecimal newShelvedWeightTotal = getDecimalFieldSum(groupDetails, ShelvingManagementDetailSaveVO::getReceivedWeight);
            if (oldReceivedQuantityTotal != newReceivedQuantityTotal ||
                    oldReceivedScatteredQuantityTotal != newReceivedScatteredQuantityTotal ||
                    oldReceivedWeightTotal.compareTo(newShelvedWeightTotal) != 0) {
                throw exception(SHELVING_MANAGEMENT_DETAIL_RECEIVED_QUANTITY_MISMATCH, detailSaveVO.getProductCode(), detailSaveVO.getProductionDate());
            }
            int shelvedQuantityTotal = getIntFieldSum(groupDetails, ShelvingManagementDetailSaveVO::getShelvedQuantity);
            int shelvedScatteredQuantityTotal = getIntFieldSum(groupDetails, ShelvingManagementDetailSaveVO::getShelvedScatteredQuantity);
            BigDecimal shelvedWeightTotal = getDecimalFieldSum(groupDetails, ShelvingManagementDetailSaveVO::getShelvedWeight);
            receiptRegistrationService.updateShelvedQuantityByShelvingDetailId(parentId, shelvedQuantityTotal, shelvedScatteredQuantityTotal, shelvedWeightTotal);
        }
    }

    private void createShelvingManagementDetailListByReceiptCreate(Long shelvingId, List<ShelvingManagementDetailCreateVO> list) {
        List<ShelvingManagementDetailDO> bean = BeanUtils.toBean(list, ShelvingManagementDetailDO.class);
        bean.forEach(o -> o.setShelvingId(shelvingId));
        shelvingManagementDetailMapper.insertBatch(bean);
    }

//    private void updateShelvingManagementDetailList(Long shelvingId, List<ShelvingManagementDetailDO> list) {
//        deleteShelvingManagementDetailByShelvingId(shelvingId);
//        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
//        createShelvingManagementDetailListByReceiptCreate(shelvingId, list);
//    }

    private void deleteShelvingManagementDetailByShelvingId(Long shelvingId) {
        shelvingManagementDetailMapper.deleteByShelvingId(shelvingId);
    }

}