package com.cdmanyou.shtwms.module.mywms.service.outbound.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.outbound.vo.wave_detail.WaveOrderDetailSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.outbound.WaveOrderDetailDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.outbound.WaveOrderDetailMapper;
import com.cdmanyou.shtwms.module.mywms.service.outbound.WaveOrderDetailService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.WAVE_ORDER_DETAIL_NOT_EXISTS;

/**
 * 波次单明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WaveOrderDetailServiceImpl implements WaveOrderDetailService {

    @Resource
    private WaveOrderDetailMapper waveOrderDetailMapper;

    @Override
    public Long createWaveOrderDetail(WaveOrderDetailSaveReqVO createReqVO) {
        // 插入
        WaveOrderDetailDO waveOrderDetail = BeanUtils.toBean(createReqVO, WaveOrderDetailDO.class);
        waveOrderDetailMapper.insert(waveOrderDetail);
        // 返回
        return waveOrderDetail.getId();
    }

    @Override
    public void updateWaveOrderDetail(WaveOrderDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateWaveOrderDetailExists(updateReqVO.getId());
        // 更新
        WaveOrderDetailDO updateObj = BeanUtils.toBean(updateReqVO, WaveOrderDetailDO.class);
        waveOrderDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteWaveOrderDetail(Long id) {
        // 校验存在
        validateWaveOrderDetailExists(id);
        // 删除
        waveOrderDetailMapper.deleteById(id);
    }

    private void validateWaveOrderDetailExists(Long id) {
        if (waveOrderDetailMapper.selectById(id) == null) {
            throw exception(WAVE_ORDER_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public WaveOrderDetailDO getWaveOrderDetail(Long id) {
        return waveOrderDetailMapper.selectById(id);
    }

    @Override
    public PageResult<WaveOrderDetailDO> getWaveOrderDetailPage(WaveOrderDetailPageReqVO pageReqVO) {
        return waveOrderDetailMapper.selectPage(pageReqVO,null);
    }

}