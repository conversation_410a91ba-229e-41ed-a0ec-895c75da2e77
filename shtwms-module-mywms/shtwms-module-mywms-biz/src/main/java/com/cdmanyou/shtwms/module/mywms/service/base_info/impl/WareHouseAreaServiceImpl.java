package com.cdmanyou.shtwms.module.mywms.service.base_info.impl;

import com.cdmanyou.shtwms.framework.common.pojo.PageResult;
import com.cdmanyou.shtwms.framework.common.util.object.BeanUtils;
import com.cdmanyou.shtwms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area.WareHouseAreaPageReqVO;
import com.cdmanyou.shtwms.module.mywms.controller.admin.base_info.vo.ware_house_area.WareHouseAreaSaveReqVO;
import com.cdmanyou.shtwms.module.mywms.dal.dataobject.base_info.WareHouseAreaDO;
import com.cdmanyou.shtwms.module.mywms.dal.mysql.base_info.WareHouseAreaMapper;
import com.cdmanyou.shtwms.module.mywms.service.base_info.WareHouseAreaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.List;

import static com.cdmanyou.shtwms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cdmanyou.shtwms.module.mywms.enums.ErrorCodeConstants.WARE_HOUSE_AREA_NOT_EXISTS;

/**
 * 库区管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WareHouseAreaServiceImpl implements WareHouseAreaService {

    @Resource
    private WareHouseAreaMapper wareHouseAreaMapper;

    @Override
    public Long createWareHouseArea(WareHouseAreaSaveReqVO createReqVO) {
        // 插入
        WareHouseAreaDO wareHouseArea = BeanUtils.toBean(createReqVO, WareHouseAreaDO.class);
        wareHouseAreaMapper.insert(wareHouseArea);
        // 返回
        return wareHouseArea.getId();
    }

    @Override
    public void updateWareHouseArea(WareHouseAreaSaveReqVO updateReqVO) {
        // 校验存在
        validateWareHouseAreaExists(updateReqVO.getId());
        // 更新
        WareHouseAreaDO updateObj = BeanUtils.toBean(updateReqVO, WareHouseAreaDO.class);
        wareHouseAreaMapper.updateById(updateObj);
    }

    @Override
    public void deleteWareHouseArea(Long id) {
        // 校验存在
        validateWareHouseAreaExists(id);
        // 删除
        wareHouseAreaMapper.deleteById(id);
    }

    private void validateWareHouseAreaExists(Long id) {
        if (wareHouseAreaMapper.selectById(id) == null) {
            throw exception(WARE_HOUSE_AREA_NOT_EXISTS);
        }
    }

    @Override
    public WareHouseAreaDO getWareHouseArea(Long id) {
        return wareHouseAreaMapper.selectById(id);
    }

    @Override
    public PageResult<WareHouseAreaDO> getWareHouseAreaPage(WareHouseAreaPageReqVO pageReqVO) {
        return wareHouseAreaMapper.selectPage(pageReqVO,null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWareHouseAreaStatus(Long id, Integer status) {
        // 校验库区是否存在
        validateWareHouseAreaExists(id);

        // 更新状态
        WareHouseAreaDO updateObj = new WareHouseAreaDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        wareHouseAreaMapper.updateById(updateObj);
    }

    @Override
    public List<WareHouseAreaDO> getWareHouseAreaListByWarehouseId(Long warehouseId) {
        // 使用 MyBatis Plus 的条件查询
        return wareHouseAreaMapper.selectList(new LambdaQueryWrapperX<WareHouseAreaDO>()
                .eq(WareHouseAreaDO::getWarehouseId, warehouseId));
    }

}